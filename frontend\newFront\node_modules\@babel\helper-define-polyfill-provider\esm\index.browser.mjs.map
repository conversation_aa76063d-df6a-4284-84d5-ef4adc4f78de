{"version": 3, "file": "index.browser.mjs", "sources": ["../src/utils.ts", "../src/imports-injector.ts", "../src/debug-utils.ts", "../src/normalize-options.ts", "../src/visitors/usage.ts", "../src/visitors/entry.ts", "../src/browser/dependencies.ts", "../src/meta-resolver.ts", "../src/index.ts"], "sourcesContent": ["import { types as t, template } from \"@babel/core\";\nimport type { NodePath } from \"@babel/traverse\";\nimport type { Utils } from \"./types\";\nimport type ImportsCachedInjector from \"./imports-injector\";\n\nexport function intersection<T>(a: Set<T>, b: Set<T>): Set<T> {\n  const result = new Set<T>();\n  a.forEach(v => b.has(v) && result.add(v));\n  return result;\n}\n\nexport function has(object: any, key: string) {\n  return Object.prototype.hasOwnProperty.call(object, key);\n}\n\nfunction resolve(\n  path: NodePath,\n  resolved: Set<NodePath> = new Set(),\n): NodePath | undefined {\n  if (resolved.has(path)) return;\n  resolved.add(path);\n\n  if (path.isVariableDeclarator()) {\n    if (path.get(\"id\").isIdentifier()) {\n      return resolve(path.get(\"init\"), resolved);\n    }\n  } else if (path.isReferencedIdentifier()) {\n    const binding = path.scope.getBinding(path.node.name);\n    if (!binding) return path;\n    if (!binding.constant) return;\n    return resolve(binding.path, resolved);\n  }\n  return path;\n}\n\nfunction resolveId(path: NodePath): string {\n  if (\n    path.isIdentifier() &&\n    !path.scope.hasBinding(path.node.name, /* noGlobals */ true)\n  ) {\n    return path.node.name;\n  }\n\n  const resolved = resolve(path);\n  if (resolved?.isIdentifier()) {\n    return resolved.node.name;\n  }\n}\n\nexport function resolveKey(\n  path: NodePath<t.Expression | t.PrivateName>,\n  computed: boolean = false,\n) {\n  const { scope } = path;\n  if (path.isStringLiteral()) return path.node.value;\n  const isIdentifier = path.isIdentifier();\n  if (\n    isIdentifier &&\n    !(computed || (path.parent as t.MemberExpression).computed)\n  ) {\n    return path.node.name;\n  }\n\n  if (\n    computed &&\n    path.isMemberExpression() &&\n    path.get(\"object\").isIdentifier({ name: \"Symbol\" }) &&\n    !scope.hasBinding(\"Symbol\", /* noGlobals */ true)\n  ) {\n    const sym = resolveKey(path.get(\"property\"), path.node.computed);\n    if (sym) return \"Symbol.\" + sym;\n  }\n\n  if (\n    isIdentifier\n      ? scope.hasBinding(path.node.name, /* noGlobals */ true)\n      : path.isPure()\n  ) {\n    const { value } = path.evaluate();\n    if (typeof value === \"string\") return value;\n  }\n}\n\nexport function resolveSource(obj: NodePath): {\n  id: string | null;\n  placement: \"prototype\" | \"static\" | null;\n} {\n  if (\n    obj.isMemberExpression() &&\n    obj.get(\"property\").isIdentifier({ name: \"prototype\" })\n  ) {\n    const id = resolveId(obj.get(\"object\"));\n\n    if (id) {\n      return { id, placement: \"prototype\" };\n    }\n    return { id: null, placement: null };\n  }\n\n  const id = resolveId(obj);\n  if (id) {\n    return { id, placement: \"static\" };\n  }\n\n  const path = resolve(obj);\n  switch (path?.type) {\n    case \"RegExpLiteral\":\n      return { id: \"RegExp\", placement: \"prototype\" };\n    case \"FunctionExpression\":\n      return { id: \"Function\", placement: \"prototype\" };\n    case \"StringLiteral\":\n      return { id: \"String\", placement: \"prototype\" };\n    case \"NumberLiteral\":\n      return { id: \"Number\", placement: \"prototype\" };\n    case \"BooleanLiteral\":\n      return { id: \"Boolean\", placement: \"prototype\" };\n    case \"ObjectExpression\":\n      return { id: \"Object\", placement: \"prototype\" };\n    case \"ArrayExpression\":\n      return { id: \"Array\", placement: \"prototype\" };\n  }\n\n  return { id: null, placement: null };\n}\n\nexport function getImportSource({ node }: NodePath<t.ImportDeclaration>) {\n  if (node.specifiers.length === 0) return node.source.value;\n}\n\nexport function getRequireSource({ node }: NodePath<t.Statement>) {\n  if (!t.isExpressionStatement(node)) return;\n  const { expression } = node;\n  if (\n    t.isCallExpression(expression) &&\n    t.isIdentifier(expression.callee) &&\n    expression.callee.name === \"require\" &&\n    expression.arguments.length === 1 &&\n    t.isStringLiteral(expression.arguments[0])\n  ) {\n    return expression.arguments[0].value;\n  }\n}\n\nfunction hoist<T extends t.Node>(node: T): T {\n  // @ts-expect-error\n  node._blockHoist = 3;\n  return node;\n}\n\nexport function createUtilsGetter(cache: ImportsCachedInjector) {\n  return (path: NodePath): Utils => {\n    const prog = path.findParent(p => p.isProgram()) as NodePath<t.Program>;\n\n    return {\n      injectGlobalImport(url, moduleName) {\n        cache.storeAnonymous(prog, url, moduleName, (isScript, source) => {\n          return isScript\n            ? template.statement.ast`require(${source})`\n            : t.importDeclaration([], source);\n        });\n      },\n      injectNamedImport(url, name, hint = name, moduleName) {\n        return cache.storeNamed(\n          prog,\n          url,\n          name,\n          moduleName,\n          (isScript, source, name) => {\n            const id = prog.scope.generateUidIdentifier(hint);\n            return {\n              node: isScript\n                ? hoist(template.statement.ast`\n                  var ${id} = require(${source}).${name}\n                `)\n                : t.importDeclaration([t.importSpecifier(id, name)], source),\n              name: id.name,\n            };\n          },\n        );\n      },\n      injectDefaultImport(url, hint = url, moduleName) {\n        return cache.storeNamed(\n          prog,\n          url,\n          \"default\",\n          moduleName,\n          (isScript, source) => {\n            const id = prog.scope.generateUidIdentifier(hint);\n            return {\n              node: isScript\n                ? hoist(template.statement.ast`var ${id} = require(${source})`)\n                : t.importDeclaration([t.importDefaultSpecifier(id)], source),\n              name: id.name,\n            };\n          },\n        );\n      },\n    };\n  };\n}\n", "import type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\n\ntype StrMap<K> = Map<string, K>;\n\nexport default class ImportsCachedInjector {\n  _imports: WeakMap<NodePath<t.Program>, StrMap<string>>;\n  _anonymousImports: WeakMap<NodePath<t.Program>, Set<string>>;\n  _lastImports: WeakMap<\n    NodePath<t.Program>,\n    Array<{ path: NodePath<t.Node>; index: number }>\n  >;\n  _resolver: (url: string) => string;\n  _getPreferredIndex: (url: string) => number;\n\n  constructor(\n    resolver: (url: string) => string,\n    getPreferredIndex: (url: string) => number,\n  ) {\n    this._imports = new WeakMap();\n    this._anonymousImports = new WeakMap();\n    this._lastImports = new WeakMap();\n    this._resolver = resolver;\n    this._getPreferredIndex = getPreferredIndex;\n  }\n\n  storeAnonymous(\n    programPath: NodePath<t.Program>,\n    url: string,\n    moduleName: string,\n    getVal: (\n      isScript: boolean,\n      source: t.StringLiteral,\n    ) => t.Statement | t.Declaration,\n  ) {\n    const key = this._normalizeKey(programPath, url);\n    const imports = this._ensure<Set<string>>(\n      this._anonymousImports,\n      programPath,\n      Set,\n    );\n\n    if (imports.has(key)) return;\n\n    const node = getVal(\n      programPath.node.sourceType === \"script\",\n      t.stringLiteral(this._resolver(url)),\n    );\n    imports.add(key);\n    this._injectImport(programPath, node, moduleName);\n  }\n\n  storeNamed(\n    programPath: NodePath<t.Program>,\n    url: string,\n    name: string,\n    moduleName: string,\n    getVal: (\n      isScript: boolean,\n      // eslint-disable-next-line no-undef\n      source: t.StringLiteral,\n      // eslint-disable-next-line no-undef\n      name: t.Identifier,\n    ) => { node: t.Statement | t.Declaration; name: string },\n  ) {\n    const key = this._normalizeKey(programPath, url, name);\n    const imports = this._ensure<Map<string, any>>(\n      this._imports,\n      programPath,\n      Map,\n    );\n\n    if (!imports.has(key)) {\n      const { node, name: id } = getVal(\n        programPath.node.sourceType === \"script\",\n        t.stringLiteral(this._resolver(url)),\n        t.identifier(name),\n      );\n      imports.set(key, id);\n      this._injectImport(programPath, node, moduleName);\n    }\n\n    return t.identifier(imports.get(key));\n  }\n\n  _injectImport(\n    programPath: NodePath<t.Program>,\n    node: t.Statement | t.Declaration,\n    moduleName: string,\n  ) {\n    const newIndex = this._getPreferredIndex(moduleName);\n    const lastImports = this._lastImports.get(programPath) ?? [];\n\n    const isPathStillValid = (path: NodePath) =>\n      path.node &&\n      // Sometimes the AST is modified and the \"last import\"\n      // we have has been replaced\n      path.parent === programPath.node &&\n      path.container === programPath.node.body;\n\n    let last: NodePath;\n\n    if (newIndex === Infinity) {\n      // Fast path: we can always just insert at the end if newIndex is `Infinity`\n      if (lastImports.length > 0) {\n        last = lastImports[lastImports.length - 1].path;\n        if (!isPathStillValid(last)) last = undefined;\n      }\n    } else {\n      for (const [i, data] of lastImports.entries()) {\n        const { path, index } = data;\n        if (isPathStillValid(path)) {\n          if (newIndex < index) {\n            const [newPath] = path.insertBefore(node);\n            lastImports.splice(i, 0, { path: newPath, index: newIndex });\n            return;\n          }\n          last = path;\n        }\n      }\n    }\n\n    if (last) {\n      const [newPath] = last.insertAfter(node);\n      lastImports.push({ path: newPath, index: newIndex });\n    } else {\n      const [newPath] = programPath.unshiftContainer(\"body\", [node]);\n      this._lastImports.set(programPath, [{ path: newPath, index: newIndex }]);\n    }\n  }\n\n  _ensure<C extends Map<string, any> | Set<string>>(\n    map: WeakMap<NodePath<t.Program>, C>,\n    programPath: NodePath<t.Program>,\n    Collection: { new (...args: any): C },\n  ): C {\n    let collection = map.get(programPath);\n    if (!collection) {\n      collection = new Collection();\n      map.set(programPath, collection);\n    }\n    return collection;\n  }\n\n  _normalizeKey(\n    programPath: NodePath<t.Program>,\n    url: string,\n    name: string = \"\",\n  ): string {\n    const { sourceType } = programPath.node;\n\n    // If we rely on the imported binding (the \"name\" parameter), we also need to cache\n    // based on the sourceType. This is because the module transforms change the names\n    // of the import variables.\n    return `${name && sourceType}::${url}::${name}`;\n  }\n}\n", "import { prettifyTargets } from \"@babel/helper-compilation-targets\";\n\nimport type { Targets } from \"./types\";\n\nexport const presetEnvSilentDebugHeader =\n  \"#__secret_key__@babel/preset-env__don't_log_debug_header_and_resolved_targets\";\n\nexport function stringifyTargetsMultiline(targets: Targets): string {\n  return JSON.stringify(prettifyTargets(targets), null, 2);\n}\n\nexport function stringifyTargets(targets: Targets): string {\n  return JSON.stringify(targets)\n    .replace(/,/g, \", \")\n    .replace(/^\\{\"/, '{ \"')\n    .replace(/\"\\}$/, '\" }');\n}\n", "import { intersection } from \"./utils\";\nimport type {\n  Pattern,\n  PluginOptions,\n  MissingDependenciesOption,\n} from \"./types\";\n\nfunction patternToRegExp(pattern: Pattern): RegExp | null {\n  if (pattern instanceof RegExp) return pattern;\n\n  try {\n    return new RegExp(`^${pattern}$`);\n  } catch {\n    return null;\n  }\n}\n\nfunction buildUnusedError(label, unused) {\n  if (!unused.length) return \"\";\n  return (\n    `  - The following \"${label}\" patterns didn't match any polyfill:\\n` +\n    unused.map(original => `    ${String(original)}\\n`).join(\"\")\n  );\n}\n\nfunction buldDuplicatesError(duplicates) {\n  if (!duplicates.size) return \"\";\n  return (\n    `  - The following polyfills were matched both by \"include\" and \"exclude\" patterns:\\n` +\n    Array.from(duplicates, name => `    ${name}\\n`).join(\"\")\n  );\n}\n\nexport function validateIncludeExclude(\n  provider: string,\n  polyfills: Map<string, unknown>,\n  includePatterns: Pattern[],\n  excludePatterns: Pattern[],\n) {\n  let current;\n  const filter = pattern => {\n    const regexp = patternToRegExp(pattern);\n    if (!regexp) return false;\n\n    let matched = false;\n    for (const polyfill of polyfills.keys()) {\n      if (regexp.test(polyfill)) {\n        matched = true;\n        current.add(polyfill);\n      }\n    }\n    return !matched;\n  };\n\n  // prettier-ignore\n  const include = current = new Set<string> ();\n  const unusedInclude = Array.from(includePatterns).filter(filter);\n\n  // prettier-ignore\n  const exclude = current = new Set<string> ();\n  const unusedExclude = Array.from(excludePatterns).filter(filter);\n\n  const duplicates = intersection(include, exclude);\n\n  if (\n    duplicates.size > 0 ||\n    unusedInclude.length > 0 ||\n    unusedExclude.length > 0\n  ) {\n    throw new Error(\n      `Error while validating the \"${provider}\" provider options:\\n` +\n        buildUnusedError(\"include\", unusedInclude) +\n        buildUnusedError(\"exclude\", unusedExclude) +\n        buldDuplicatesError(duplicates),\n    );\n  }\n\n  return { include, exclude };\n}\n\nexport function applyMissingDependenciesDefaults(\n  options: PluginOptions,\n  babelApi: any,\n): MissingDependenciesOption {\n  const { missingDependencies = {} } = options;\n  if (missingDependencies === false) return false;\n\n  const caller = babelApi.caller(caller => caller?.name);\n\n  const {\n    log = \"deferred\",\n    inject = caller === \"rollup-plugin-babel\" ? \"throw\" : \"import\",\n    all = false,\n  } = missingDependencies;\n\n  return { log, inject, all };\n}\n", "import type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\nimport type { CallProvider } from \"./index\";\n\nimport { resolveKey, resolveSource } from \"../utils\";\n\nfunction isRemoved(path: NodePath) {\n  if (path.removed) return true;\n  if (!path.parentPath) return false;\n  if (path.listKey) {\n    if (!path.parentPath.node?.[path.listKey]?.includes(path.node)) return true;\n  } else {\n    if (path.parentPath.node?.[path.key] !== path.node) return true;\n  }\n  return isRemoved(path.parentPath);\n}\n\nexport default (callProvider: CallProvider) => {\n  function property(object, key, placement, path) {\n    return callProvider({ kind: \"property\", object, key, placement }, path);\n  }\n\n  function handleReferencedIdentifier(path) {\n    const {\n      node: { name },\n      scope,\n    } = path;\n    if (scope.getBindingIdentifier(name)) return;\n\n    callProvider({ kind: \"global\", name }, path);\n  }\n\n  function analyzeMemberExpression(\n    path: NodePath<t.MemberExpression | t.OptionalMemberExpression>,\n  ) {\n    const key = resolveKey(path.get(\"property\"), path.node.computed);\n    return { key, handleAsMemberExpression: !!key && key !== \"prototype\" };\n  }\n\n  return {\n    // Symbol(), new Promise\n    ReferencedIdentifier(path: NodePath<t.Identifier>) {\n      const { parentPath } = path;\n      if (\n        parentPath.isMemberExpression({ object: path.node }) &&\n        analyzeMemberExpression(parentPath).handleAsMemberExpression\n      ) {\n        return;\n      }\n      handleReferencedIdentifier(path);\n    },\n\n    \"MemberExpression|OptionalMemberExpression\"(\n      path: NodePath<t.MemberExpression | t.OptionalMemberExpression>,\n    ) {\n      const { key, handleAsMemberExpression } = analyzeMemberExpression(path);\n      if (!handleAsMemberExpression) return;\n\n      const object = path.get(\"object\");\n      let objectIsGlobalIdentifier = object.isIdentifier();\n      if (objectIsGlobalIdentifier) {\n        const binding = object.scope.getBinding(\n          (object.node as t.Identifier).name,\n        );\n        if (binding) {\n          if (binding.path.isImportNamespaceSpecifier()) return;\n          objectIsGlobalIdentifier = false;\n        }\n      }\n\n      const source = resolveSource(object);\n      let skipObject = property(source.id, key, source.placement, path);\n      skipObject ||=\n        !objectIsGlobalIdentifier ||\n        path.shouldSkip ||\n        object.shouldSkip ||\n        isRemoved(object);\n\n      if (!skipObject) handleReferencedIdentifier(object);\n    },\n\n    ObjectPattern(path: NodePath<t.ObjectPattern>) {\n      const { parentPath, parent } = path;\n      let obj;\n\n      // const { keys, values } = Object\n      if (parentPath.isVariableDeclarator()) {\n        obj = parentPath.get(\"init\");\n        // ({ keys, values } = Object)\n      } else if (parentPath.isAssignmentExpression()) {\n        obj = parentPath.get(\"right\");\n        // !function ({ keys, values }) {...} (Object)\n        // resolution does not work after properties transform :-(\n      } else if (parentPath.isFunction()) {\n        const grand = parentPath.parentPath;\n        if (grand.isCallExpression() || grand.isNewExpression()) {\n          if (grand.node.callee === parent) {\n            obj = grand.get(\"arguments\")[path.key];\n          }\n        }\n      }\n\n      let id = null;\n      let placement = null;\n      if (obj) ({ id, placement } = resolveSource(obj));\n\n      for (const prop of path.get(\"properties\")) {\n        if (prop.isObjectProperty()) {\n          const key = resolveKey(prop.get(\"key\"));\n          if (key) property(id, key, placement, prop);\n        }\n      }\n    },\n\n    BinaryExpression(path: NodePath<t.BinaryExpression>) {\n      if (path.node.operator !== \"in\") return;\n\n      const source = resolveSource(path.get(\"right\"));\n      const key = resolveKey(path.get(\"left\"), true);\n\n      if (!key) return;\n\n      callProvider(\n        {\n          kind: \"in\",\n          object: source.id,\n          key,\n          placement: source.placement,\n        },\n        path,\n      );\n    },\n  };\n};\n", "import type { NodePath } from \"@babel/traverse\";\nimport { types as t } from \"@babel/core\";\nimport type { CallProvider } from \"./index\";\n\nimport { getImportSource, getRequireSource } from \"../utils\";\n\nexport default (callProvider: CallProvider) => ({\n  ImportDeclaration(path: NodePath<t.ImportDeclaration>) {\n    const source = getImportSource(path);\n    if (!source) return;\n    callProvider({ kind: \"import\", source }, path);\n  },\n  Program(path: NodePath<t.Program>) {\n    path.get(\"body\").forEach(bodyPath => {\n      const source = getRequireSource(bodyPath);\n      if (!source) return;\n      callProvider({ kind: \"import\", source }, bodyPath);\n    });\n  },\n});\n", "export function resolve(\n  dirname: string,\n  moduleName: string,\n  absoluteImports: boolean | string,\n): string {\n  if (absoluteImports === false) return moduleName;\n\n  throw new Error(\n    `\"absoluteImports\" is not supported in bundles prepared for the browser.`,\n  );\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function has(basedir: string, name: string) {\n  return true;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function logMissing(missingDeps: Set<string>) {}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport function laterLogMissing(missingDeps: Set<string>) {}\n", "import type {\n  MetaDescriptor,\n  ResolverPolyfills,\n  ResolvedPolyfill,\n} from \"./types\";\n\nimport { has } from \"./utils\";\n\ntype ResolverFn<T> = (meta: MetaDescriptor) => void | ResolvedPolyfill<T>;\n\nconst PossibleGlobalObjects = new Set<string>([\n  \"global\",\n  \"globalThis\",\n  \"self\",\n  \"window\",\n]);\n\nexport default function createMetaResolver<T>(\n  polyfills: ResolverPolyfills<T>,\n): ResolverFn<T> {\n  const { static: staticP, instance: instanceP, global: globalP } = polyfills;\n\n  return meta => {\n    if (meta.kind === \"global\" && globalP && has(globalP, meta.name)) {\n      return { kind: \"global\", desc: globalP[meta.name], name: meta.name };\n    }\n\n    if (meta.kind === \"property\" || meta.kind === \"in\") {\n      const { placement, object, key } = meta;\n\n      if (object && placement === \"static\") {\n        if (globalP && PossibleGlobalObjects.has(object) && has(globalP, key)) {\n          return { kind: \"global\", desc: globalP[key], name: key };\n        }\n\n        if (staticP && has(staticP, object) && has(staticP[object], key)) {\n          return {\n            kind: \"static\",\n            desc: staticP[object][key],\n            name: `${object}$${key}`,\n          };\n        }\n      }\n\n      if (instanceP && has(instanceP, key)) {\n        return { kind: \"instance\", desc: instanceP[key], name: `${key}` };\n      }\n    }\n  };\n}\n", "import { declare } from \"@babel/helper-plugin-utils\";\nimport type { NodePath } from \"@babel/traverse\";\n\nimport _getTargets, {\n  isRequired,\n  getInclusionReasons,\n} from \"@babel/helper-compilation-targets\";\nconst getTargets = _getTargets.default || _getTargets;\n\nimport { createUtilsGetter } from \"./utils\";\nimport ImportsCachedInjector from \"./imports-injector\";\nimport {\n  stringifyTargetsMultiline,\n  presetEnvSilentDebugHeader,\n} from \"./debug-utils\";\nimport {\n  validateIncludeExclude,\n  applyMissingDependenciesDefaults,\n} from \"./normalize-options\";\n\nimport type {\n  ProviderApi,\n  MethodString,\n  Targets,\n  MetaDescriptor,\n  PolyfillProvider,\n  PluginOptions,\n  ProviderOptions,\n} from \"./types\";\n\nimport * as v from \"./visitors\";\nimport * as deps from \"./node/dependencies\";\n\nimport createMetaResolver from \"./meta-resolver\";\n\nexport type { PolyfillProvider, MetaDescriptor, Utils, Targets } from \"./types\";\n\nfunction resolveOptions<Options>(\n  options: PluginOptions,\n  babelApi,\n): {\n  method: MethodString;\n  methodName: \"usageGlobal\" | \"entryGlobal\" | \"usagePure\";\n  targets: Targets;\n  debug: boolean | typeof presetEnvSilentDebugHeader;\n  shouldInjectPolyfill:\n    | ((name: string, shouldInject: boolean) => boolean)\n    | undefined;\n  providerOptions: ProviderOptions<Options>;\n  absoluteImports: string | boolean;\n} {\n  const {\n    method,\n    targets: targetsOption,\n    ignoreBrowserslistConfig,\n    configPath,\n    debug,\n    shouldInjectPolyfill,\n    absoluteImports,\n    ...providerOptions\n  } = options;\n\n  if (isEmpty(options)) {\n    throw new Error(\n      `\\\nThis plugin requires options, for example:\n    {\n      \"plugins\": [\n        [\"<plugin name>\", { method: \"usage-pure\" }]\n      ]\n    }\n\nSee more options at https://github.com/babel/babel-polyfills/blob/main/docs/usage.md`,\n    );\n  }\n\n  let methodName;\n  if (method === \"usage-global\") methodName = \"usageGlobal\";\n  else if (method === \"entry-global\") methodName = \"entryGlobal\";\n  else if (method === \"usage-pure\") methodName = \"usagePure\";\n  else if (typeof method !== \"string\") {\n    throw new Error(\".method must be a string\");\n  } else {\n    throw new Error(\n      `.method must be one of \"entry-global\", \"usage-global\"` +\n        ` or \"usage-pure\" (received ${JSON.stringify(method)})`,\n    );\n  }\n\n  if (typeof shouldInjectPolyfill === \"function\") {\n    if (options.include || options.exclude) {\n      throw new Error(\n        `.include and .exclude are not supported when using the` +\n          ` .shouldInjectPolyfill function.`,\n      );\n    }\n  } else if (shouldInjectPolyfill != null) {\n    throw new Error(\n      `.shouldInjectPolyfill must be a function, or undefined` +\n        ` (received ${JSON.stringify(shouldInjectPolyfill)})`,\n    );\n  }\n\n  if (\n    absoluteImports != null &&\n    typeof absoluteImports !== \"boolean\" &&\n    typeof absoluteImports !== \"string\"\n  ) {\n    throw new Error(\n      `.absoluteImports must be a boolean, a string, or undefined` +\n        ` (received ${JSON.stringify(absoluteImports)})`,\n    );\n  }\n\n  let targets;\n\n  if (\n    // If any browserslist-related option is specified, fallback to the old\n    // behavior of not using the targets specified in the top-level options.\n    targetsOption ||\n    configPath ||\n    ignoreBrowserslistConfig\n  ) {\n    const targetsObj =\n      typeof targetsOption === \"string\" || Array.isArray(targetsOption)\n        ? { browsers: targetsOption }\n        : targetsOption;\n\n    targets = getTargets(targetsObj, {\n      ignoreBrowserslistConfig,\n      configPath,\n    });\n  } else {\n    targets = babelApi.targets();\n  }\n\n  return {\n    method,\n    methodName,\n    targets,\n    absoluteImports: absoluteImports ?? false,\n    shouldInjectPolyfill,\n    debug: !!debug,\n    providerOptions: providerOptions as any as ProviderOptions<Options>,\n  };\n}\n\nfunction instantiateProvider<Options>(\n  factory: PolyfillProvider<Options>,\n  options: PluginOptions,\n  missingDependencies,\n  dirname,\n  debugLog,\n  babelApi,\n) {\n  const {\n    method,\n    methodName,\n    targets,\n    debug,\n    shouldInjectPolyfill,\n    providerOptions,\n    absoluteImports,\n  } = resolveOptions<Options>(options, babelApi);\n\n  // eslint-disable-next-line prefer-const\n  let include, exclude;\n  let polyfillsSupport;\n  let polyfillsNames: Map<string, number> | undefined;\n  let filterPolyfills;\n\n  const getUtils = createUtilsGetter(\n    new ImportsCachedInjector(\n      moduleName => deps.resolve(dirname, moduleName, absoluteImports),\n      (name: string) => polyfillsNames?.get(name) ?? Infinity,\n    ),\n  );\n\n  const depsCache = new Map();\n\n  const api: ProviderApi = {\n    babel: babelApi,\n    getUtils,\n    method: options.method,\n    targets,\n    createMetaResolver,\n    shouldInjectPolyfill(name) {\n      if (polyfillsNames === undefined) {\n        throw new Error(\n          `Internal error in the ${factory.name} provider: ` +\n            `shouldInjectPolyfill() can't be called during initialization.`,\n        );\n      }\n      if (!polyfillsNames.has(name)) {\n        console.warn(\n          `Internal error in the ${providerName} provider: ` +\n            `unknown polyfill \"${name}\".`,\n        );\n      }\n\n      if (filterPolyfills && !filterPolyfills(name)) return false;\n\n      let shouldInject = isRequired(name, targets, {\n        compatData: polyfillsSupport,\n        includes: include,\n        excludes: exclude,\n      });\n\n      if (shouldInjectPolyfill) {\n        shouldInject = shouldInjectPolyfill(name, shouldInject);\n        if (typeof shouldInject !== \"boolean\") {\n          throw new Error(`.shouldInjectPolyfill must return a boolean.`);\n        }\n      }\n\n      return shouldInject;\n    },\n    debug(name) {\n      debugLog().found = true;\n\n      if (!debug || !name) return;\n\n      if (debugLog().polyfills.has(providerName)) return;\n      debugLog().polyfills.add(name);\n      debugLog().polyfillsSupport ??= polyfillsSupport;\n    },\n    assertDependency(name, version = \"*\") {\n      if (missingDependencies === false) return;\n      if (absoluteImports) {\n        // If absoluteImports is not false, we will try resolving\n        // the dependency and throw if it's not possible. We can\n        // skip the check here.\n        return;\n      }\n\n      const dep = version === \"*\" ? name : `${name}@^${version}`;\n\n      const found = missingDependencies.all\n        ? false\n        : mapGetOr(depsCache, `${name} :: ${dirname}`, () =>\n            deps.has(dirname, name),\n          );\n\n      if (!found) {\n        debugLog().missingDeps.add(dep);\n      }\n    },\n  };\n\n  const provider = factory(api, providerOptions, dirname);\n  const providerName = provider.name || factory.name;\n\n  if (typeof provider[methodName] !== \"function\") {\n    throw new Error(\n      `The \"${providerName}\" provider doesn't support the \"${method}\" polyfilling method.`,\n    );\n  }\n\n  if (Array.isArray(provider.polyfills)) {\n    polyfillsNames = new Map(\n      provider.polyfills.map((name, index) => [name, index]),\n    );\n    filterPolyfills = provider.filterPolyfills;\n  } else if (provider.polyfills) {\n    polyfillsNames = new Map(\n      Object.keys(provider.polyfills).map((name, index) => [name, index]),\n    );\n    polyfillsSupport = provider.polyfills;\n    filterPolyfills = provider.filterPolyfills;\n  } else {\n    polyfillsNames = new Map();\n  }\n\n  ({ include, exclude } = validateIncludeExclude(\n    providerName,\n    polyfillsNames,\n    providerOptions.include || [],\n    providerOptions.exclude || [],\n  ));\n\n  let callProvider: (payload: MetaDescriptor, path: NodePath) => boolean;\n  if (methodName === \"usageGlobal\") {\n    callProvider = (payload, path) => {\n      const utils = getUtils(path);\n      return (\n        (provider[methodName](payload, utils, path) satisfies boolean) ?? false\n      );\n    };\n  } else {\n    callProvider = (payload, path) => {\n      const utils = getUtils(path);\n      provider[methodName](payload, utils, path) satisfies void;\n      return false;\n    };\n  }\n\n  return {\n    debug,\n    method,\n    targets,\n    provider,\n    providerName,\n    callProvider,\n  };\n}\n\nexport default function definePolyfillProvider<Options>(\n  factory: PolyfillProvider<Options>,\n) {\n  return declare((babelApi, options: PluginOptions, dirname: string) => {\n    babelApi.assertVersion(\"^7.0.0 || ^8.0.0-alpha.0\");\n    const { traverse } = babelApi;\n\n    let debugLog;\n\n    const missingDependencies = applyMissingDependenciesDefaults(\n      options,\n      babelApi,\n    );\n\n    const { debug, method, targets, provider, providerName, callProvider } =\n      instantiateProvider<Options>(\n        factory,\n        options,\n        missingDependencies,\n        dirname,\n        () => debugLog,\n        babelApi,\n      );\n\n    const createVisitor = method === \"entry-global\" ? v.entry : v.usage;\n\n    const visitor = provider.visitor\n      ? traverse.visitors.merge([createVisitor(callProvider), provider.visitor])\n      : createVisitor(callProvider);\n\n    if (debug && debug !== presetEnvSilentDebugHeader) {\n      console.log(`${providerName}: \\`DEBUG\\` option`);\n      console.log(`\\nUsing targets: ${stringifyTargetsMultiline(targets)}`);\n      console.log(`\\nUsing polyfills with \\`${method}\\` method:`);\n    }\n\n    const { runtimeName } = provider;\n\n    return {\n      name: \"inject-polyfills\",\n      visitor,\n\n      pre(file) {\n        if (runtimeName) {\n          if (\n            file.get(\"runtimeHelpersModuleName\") &&\n            file.get(\"runtimeHelpersModuleName\") !== runtimeName\n          ) {\n            console.warn(\n              `Two different polyfill providers` +\n                ` (${file.get(\"runtimeHelpersModuleProvider\")}` +\n                ` and ${providerName}) are trying to define two` +\n                ` conflicting @babel/runtime alternatives:` +\n                ` ${file.get(\"runtimeHelpersModuleName\")} and ${runtimeName}.` +\n                ` The second one will be ignored.`,\n            );\n          } else {\n            file.set(\"runtimeHelpersModuleName\", runtimeName);\n            file.set(\"runtimeHelpersModuleProvider\", providerName);\n          }\n        }\n\n        debugLog = {\n          polyfills: new Set(),\n          polyfillsSupport: undefined,\n          found: false,\n          providers: new Set(),\n          missingDeps: new Set(),\n        };\n\n        provider.pre?.apply(this, arguments);\n      },\n      post() {\n        provider.post?.apply(this, arguments);\n\n        if (missingDependencies !== false) {\n          if (missingDependencies.log === \"per-file\") {\n            deps.logMissing(debugLog.missingDeps);\n          } else {\n            deps.laterLogMissing(debugLog.missingDeps);\n          }\n        }\n\n        if (!debug) return;\n\n        if (this.filename) console.log(`\\n[${this.filename}]`);\n\n        if (debugLog.polyfills.size === 0) {\n          console.log(\n            method === \"entry-global\"\n              ? debugLog.found\n                ? `Based on your targets, the ${providerName} polyfill did not add any polyfill.`\n                : `The entry point for the ${providerName} polyfill has not been found.`\n              : `Based on your code and targets, the ${providerName} polyfill did not add any polyfill.`,\n          );\n\n          return;\n        }\n\n        if (method === \"entry-global\") {\n          console.log(\n            `The ${providerName} polyfill entry has been replaced with ` +\n              `the following polyfills:`,\n          );\n        } else {\n          console.log(\n            `The ${providerName} polyfill added the following polyfills:`,\n          );\n        }\n\n        for (const name of debugLog.polyfills) {\n          if (debugLog.polyfillsSupport?.[name]) {\n            const filteredTargets = getInclusionReasons(\n              name,\n              targets,\n              debugLog.polyfillsSupport,\n            );\n\n            const formattedTargets = JSON.stringify(filteredTargets)\n              .replace(/,/g, \", \")\n              .replace(/^\\{\"/, '{ \"')\n              .replace(/\"\\}$/, '\" }');\n\n            console.log(`  ${name} ${formattedTargets}`);\n          } else {\n            console.log(`  ${name}`);\n          }\n        }\n      },\n    };\n  });\n}\n\nfunction mapGetOr(map, key, getDefault) {\n  let val = map.get(key);\n  if (val === undefined) {\n    val = getDefault();\n    map.set(key, val);\n  }\n  return val;\n}\n\nfunction isEmpty(obj) {\n  return Object.keys(obj).length === 0;\n}\n"], "names": ["types", "t", "template", "_babel", "default", "intersection", "a", "b", "result", "Set", "for<PERSON>ach", "v", "has", "add", "object", "key", "Object", "prototype", "hasOwnProperty", "call", "resolve", "path", "resolved", "isVariableDeclarator", "get", "isIdentifier", "isReferencedIdentifier", "binding", "scope", "getBinding", "node", "name", "constant", "resolveId", "hasBinding", "<PERSON><PERSON><PERSON>", "computed", "isStringLiteral", "value", "parent", "isMemberExpression", "sym", "isPure", "evaluate", "resolveSource", "obj", "id", "placement", "type", "getImportSource", "specifiers", "length", "source", "getRequireSource", "isExpressionStatement", "expression", "isCallExpression", "callee", "arguments", "hoist", "_blockHoist", "createUtilsGetter", "cache", "prog", "findParent", "p", "isProgram", "injectGlobalImport", "url", "moduleName", "storeAnonymous", "isScript", "statement", "ast", "importDeclaration", "injectNamedImport", "hint", "storeNamed", "generateUidIdentifier", "importSpecifier", "injectDefaultImport", "importDefaultSpecifier", "ImportsCachedInjector", "constructor", "resolver", "getPreferredIndex", "_imports", "WeakMap", "_anonymousImports", "_lastImports", "_resolver", "_getPreferredIndex", "programPath", "getVal", "_normalizeKey", "imports", "_ensure", "sourceType", "stringLiteral", "_injectImport", "Map", "identifier", "set", "_this$_lastImports$ge", "newIndex", "lastImports", "isPathStillValid", "container", "body", "last", "Infinity", "undefined", "i", "data", "entries", "index", "newPath", "insertBefore", "splice", "insertAfter", "push", "unshiftContainer", "map", "Collection", "collection", "presetEnvSilentDebugHeader", "stringifyTargetsMultiline", "targets", "JSON", "stringify", "prettifyTargets", "patternToRegExp", "pattern", "RegExp", "buildUnusedError", "label", "unused", "original", "String", "join", "buldDuplicatesError", "duplicates", "size", "Array", "from", "validateIncludeExclude", "provider", "polyfills", "includePatterns", "excludePatterns", "current", "filter", "regexp", "matched", "polyfill", "keys", "test", "include", "unusedInclude", "exclude", "unusedExclude", "Error", "applyMissingDependenciesDefaults", "options", "babelApi", "missingDependencies", "caller", "log", "inject", "all", "isRemoved", "removed", "parentPath", "<PERSON><PERSON><PERSON>", "_path$parentPath$node", "includes", "_path$parentPath$node2", "callProvider", "property", "kind", "handleReferencedIdentifier", "getBindingIdentifier", "analyzeMemberExpression", "handleAsMemberExpression", "ReferencedIdentifier", "MemberExpression|OptionalMemberExpression", "objectIsGlobalIdentifier", "isImportNamespaceSpecifier", "skipObject", "shouldSkip", "ObjectPattern", "isAssignmentExpression", "isFunction", "grand", "isNewExpression", "prop", "isObjectProperty", "BinaryExpression", "operator", "ImportDeclaration", "Program", "bodyPath", "dirname", "absoluteImports", "basedir", "logMissing", "missingDeps", "laterLog<PERSON><PERSON>ing", "PossibleGlobalObjects", "createMetaResolver", "static", "staticP", "instance", "instanceP", "global", "globalP", "meta", "desc", "getTargets", "_getTargets", "resolveOptions", "method", "targetsOption", "ignoreBrowserslistConfig", "config<PERSON><PERSON>", "debug", "shouldInjectPolyfill", "providerOptions", "isEmpty", "methodName", "targetsObj", "isArray", "browsers", "instantiateProvider", "factory", "debugLog", "polyfillsSupport", "polyfillsNames", "filterPolyfills", "getUtils", "deps", "_polyfillsNames$get", "_polyfillsNames", "deps<PERSON>ache", "api", "babel", "console", "warn", "providerName", "shouldInject", "isRequired", "compatData", "excludes", "_debugLog", "_debugLog$polyfillsSu", "found", "assertDependency", "version", "dep", "mapGetOr", "payload", "_ref", "utils", "definePolyfillProvider", "declare", "assertVersion", "traverse", "createVisitor", "visitor", "visitors", "merge", "runtimeName", "pre", "file", "_provider$pre", "providers", "apply", "post", "_provider$post", "filename", "_debugLog$polyfillsSu2", "filteredTargets", "getInclusionReasons", "formattedTargets", "replace", "getDefault", "val"], "mappings": ";;;;;AAASA,EAAAA,KAAK,EAAIC,GAAC;AAAEC,EAAAA,QAAQ,EAARA,QAAAA;AAAQ,CAAA,GAAAC,MAAA,CAAAC,OAAA,IAAAD,MAAA,CAAA;AAKtB,SAASE,YAAYA,CAAIC,CAAS,EAAEC,CAAS,EAAU;AAC5D,EAAA,MAAMC,MAAM,GAAG,IAAIC,GAAG,EAAK,CAAA;AAC3BH,EAAAA,CAAC,CAACI,OAAO,CAACC,CAAC,IAAIJ,CAAC,CAACK,GAAG,CAACD,CAAC,CAAC,IAAIH,MAAM,CAACK,GAAG,CAACF,CAAC,CAAC,CAAC,CAAA;AACzC,EAAA,OAAOH,MAAM,CAAA;AACf,CAAA;AAEO,SAASI,KAAGA,CAACE,MAAW,EAAEC,GAAW,EAAE;EAC5C,OAAOC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,MAAM,EAAEC,GAAG,CAAC,CAAA;AAC1D,CAAA;AAEA,SAASK,SAAOA,CACdC,IAAc,EACdC,QAAuB,GAAG,IAAIb,GAAG,EAAE,EACb;AACtB,EAAA,IAAIa,QAAQ,CAACV,GAAG,CAACS,IAAI,CAAC,EAAE,OAAA;AACxBC,EAAAA,QAAQ,CAACT,GAAG,CAACQ,IAAI,CAAC,CAAA;AAElB,EAAA,IAAIA,IAAI,CAACE,oBAAoB,EAAE,EAAE;IAC/B,IAAIF,IAAI,CAACG,GAAG,CAAC,IAAI,CAAC,CAACC,YAAY,EAAE,EAAE;MACjC,OAAOL,SAAO,CAACC,IAAI,CAACG,GAAG,CAAC,MAAM,CAAC,EAAEF,QAAQ,CAAC,CAAA;AAC5C,KAAA;AACF,GAAC,MAAM,IAAID,IAAI,CAACK,sBAAsB,EAAE,EAAE;AACxC,IAAA,MAAMC,OAAO,GAAGN,IAAI,CAACO,KAAK,CAACC,UAAU,CAACR,IAAI,CAACS,IAAI,CAACC,IAAI,CAAC,CAAA;AACrD,IAAA,IAAI,CAACJ,OAAO,EAAE,OAAON,IAAI,CAAA;AACzB,IAAA,IAAI,CAACM,OAAO,CAACK,QAAQ,EAAE,OAAA;AACvB,IAAA,OAAOZ,SAAO,CAACO,OAAO,CAACN,IAAI,EAAEC,QAAQ,CAAC,CAAA;AACxC,GAAA;AACA,EAAA,OAAOD,IAAI,CAAA;AACb,CAAA;AAEA,SAASY,SAASA,CAACZ,IAAc,EAAU;EACzC,IACEA,IAAI,CAACI,YAAY,EAAE,IACnB,CAACJ,IAAI,CAACO,KAAK,CAACM,UAAU,CAACb,IAAI,CAACS,IAAI,CAACC,IAAI,iBAAkB,IAAI,CAAC,EAC5D;AACA,IAAA,OAAOV,IAAI,CAACS,IAAI,CAACC,IAAI,CAAA;AACvB,GAAA;AAEA,EAAA,MAAMT,QAAQ,GAAGF,SAAO,CAACC,IAAI,CAAC,CAAA;AAC9B,EAAA,IAAIC,QAAQ,IAARA,IAAAA,IAAAA,QAAQ,CAAEG,YAAY,EAAE,EAAE;AAC5B,IAAA,OAAOH,QAAQ,CAACQ,IAAI,CAACC,IAAI,CAAA;AAC3B,GAAA;AACF,CAAA;AAEO,SAASI,UAAUA,CACxBd,IAA4C,EAC5Ce,QAAiB,GAAG,KAAK,EACzB;EACA,MAAM;AAAER,IAAAA,KAAAA;AAAM,GAAC,GAAGP,IAAI,CAAA;EACtB,IAAIA,IAAI,CAACgB,eAAe,EAAE,EAAE,OAAOhB,IAAI,CAACS,IAAI,CAACQ,KAAK,CAAA;AAClD,EAAA,MAAMb,YAAY,GAAGJ,IAAI,CAACI,YAAY,EAAE,CAAA;EACxC,IACEA,YAAY,IACZ,EAAEW,QAAQ,IAAKf,IAAI,CAACkB,MAAM,CAAwBH,QAAQ,CAAC,EAC3D;AACA,IAAA,OAAOf,IAAI,CAACS,IAAI,CAACC,IAAI,CAAA;AACvB,GAAA;AAEA,EAAA,IACEK,QAAQ,IACRf,IAAI,CAACmB,kBAAkB,EAAE,IACzBnB,IAAI,CAACG,GAAG,CAAC,QAAQ,CAAC,CAACC,YAAY,CAAC;AAAEM,IAAAA,IAAI,EAAE,QAAA;AAAS,GAAC,CAAC,IACnD,CAACH,KAAK,CAACM,UAAU,CAAC,QAAQ,iBAAkB,IAAI,CAAC,EACjD;AACA,IAAA,MAAMO,GAAG,GAAGN,UAAU,CAACd,IAAI,CAACG,GAAG,CAAC,UAAU,CAAC,EAAEH,IAAI,CAACS,IAAI,CAACM,QAAQ,CAAC,CAAA;AAChE,IAAA,IAAIK,GAAG,EAAE,OAAO,SAAS,GAAGA,GAAG,CAAA;AACjC,GAAA;EAEA,IACEhB,YAAY,GACRG,KAAK,CAACM,UAAU,CAACb,IAAI,CAACS,IAAI,CAACC,IAAI,iBAAkB,IAAI,CAAC,GACtDV,IAAI,CAACqB,MAAM,EAAE,EACjB;IACA,MAAM;AAAEJ,MAAAA,KAAAA;AAAM,KAAC,GAAGjB,IAAI,CAACsB,QAAQ,EAAE,CAAA;AACjC,IAAA,IAAI,OAAOL,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK,CAAA;AAC7C,GAAA;AACF,CAAA;AAEO,SAASM,aAAaA,CAACC,GAAa,EAGzC;AACA,EAAA,IACEA,GAAG,CAACL,kBAAkB,EAAE,IACxBK,GAAG,CAACrB,GAAG,CAAC,UAAU,CAAC,CAACC,YAAY,CAAC;AAAEM,IAAAA,IAAI,EAAE,WAAA;AAAY,GAAC,CAAC,EACvD;IACA,MAAMe,EAAE,GAAGb,SAAS,CAACY,GAAG,CAACrB,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAA;AAEvC,IAAA,IAAIsB,EAAE,EAAE;MACN,OAAO;QAAEA,EAAE;AAAEC,QAAAA,SAAS,EAAE,WAAA;OAAa,CAAA;AACvC,KAAA;IACA,OAAO;AAAED,MAAAA,EAAE,EAAE,IAAI;AAAEC,MAAAA,SAAS,EAAE,IAAA;KAAM,CAAA;AACtC,GAAA;AAEA,EAAA,MAAMD,EAAE,GAAGb,SAAS,CAACY,GAAG,CAAC,CAAA;AACzB,EAAA,IAAIC,EAAE,EAAE;IACN,OAAO;MAAEA,EAAE;AAAEC,MAAAA,SAAS,EAAE,QAAA;KAAU,CAAA;AACpC,GAAA;AAEA,EAAA,MAAM1B,IAAI,GAAGD,SAAO,CAACyB,GAAG,CAAC,CAAA;AACzB,EAAA,QAAQxB,IAAI,IAAA,IAAA,GAAA,KAAA,CAAA,GAAJA,IAAI,CAAE2B,IAAI;AAChB,IAAA,KAAK,eAAe;MAClB,OAAO;AAAEF,QAAAA,EAAE,EAAE,QAAQ;AAAEC,QAAAA,SAAS,EAAE,WAAA;OAAa,CAAA;AACjD,IAAA,KAAK,oBAAoB;MACvB,OAAO;AAAED,QAAAA,EAAE,EAAE,UAAU;AAAEC,QAAAA,SAAS,EAAE,WAAA;OAAa,CAAA;AACnD,IAAA,KAAK,eAAe;MAClB,OAAO;AAAED,QAAAA,EAAE,EAAE,QAAQ;AAAEC,QAAAA,SAAS,EAAE,WAAA;OAAa,CAAA;AACjD,IAAA,KAAK,eAAe;MAClB,OAAO;AAAED,QAAAA,EAAE,EAAE,QAAQ;AAAEC,QAAAA,SAAS,EAAE,WAAA;OAAa,CAAA;AACjD,IAAA,KAAK,gBAAgB;MACnB,OAAO;AAAED,QAAAA,EAAE,EAAE,SAAS;AAAEC,QAAAA,SAAS,EAAE,WAAA;OAAa,CAAA;AAClD,IAAA,KAAK,kBAAkB;MACrB,OAAO;AAAED,QAAAA,EAAE,EAAE,QAAQ;AAAEC,QAAAA,SAAS,EAAE,WAAA;OAAa,CAAA;AACjD,IAAA,KAAK,iBAAiB;MACpB,OAAO;AAAED,QAAAA,EAAE,EAAE,OAAO;AAAEC,QAAAA,SAAS,EAAE,WAAA;OAAa,CAAA;AAClD,GAAA;EAEA,OAAO;AAAED,IAAAA,EAAE,EAAE,IAAI;AAAEC,IAAAA,SAAS,EAAE,IAAA;GAAM,CAAA;AACtC,CAAA;AAEO,SAASE,eAAeA,CAAC;AAAEnB,EAAAA,IAAAA;AAAoC,CAAC,EAAE;AACvE,EAAA,IAAIA,IAAI,CAACoB,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE,OAAOrB,IAAI,CAACsB,MAAM,CAACd,KAAK,CAAA;AAC5D,CAAA;AAEO,SAASe,gBAAgBA,CAAC;AAAEvB,EAAAA,IAAAA;AAA4B,CAAC,EAAE;AAChE,EAAA,IAAI,CAAC7B,GAAC,CAACqD,qBAAqB,CAACxB,IAAI,CAAC,EAAE,OAAA;EACpC,MAAM;AAAEyB,IAAAA,UAAAA;AAAW,GAAC,GAAGzB,IAAI,CAAA;AAC3B,EAAA,IACE7B,GAAC,CAACuD,gBAAgB,CAACD,UAAU,CAAC,IAC9BtD,GAAC,CAACwB,YAAY,CAAC8B,UAAU,CAACE,MAAM,CAAC,IACjCF,UAAU,CAACE,MAAM,CAAC1B,IAAI,KAAK,SAAS,IACpCwB,UAAU,CAACG,SAAS,CAACP,MAAM,KAAK,CAAC,IACjClD,GAAC,CAACoC,eAAe,CAACkB,UAAU,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC,EAC1C;AACA,IAAA,OAAOH,UAAU,CAACG,SAAS,CAAC,CAAC,CAAC,CAACpB,KAAK,CAAA;AACtC,GAAA;AACF,CAAA;AAEA,SAASqB,KAAKA,CAAmB7B,IAAO,EAAK;AAC3C;EACAA,IAAI,CAAC8B,WAAW,GAAG,CAAC,CAAA;AACpB,EAAA,OAAO9B,IAAI,CAAA;AACb,CAAA;AAEO,SAAS+B,iBAAiBA,CAACC,KAA4B,EAAE;AAC9D,EAAA,OAAQzC,IAAc,IAAY;AAChC,IAAA,MAAM0C,IAAI,GAAG1C,IAAI,CAAC2C,UAAU,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,EAAE,CAAwB,CAAA;IAEvE,OAAO;AACLC,MAAAA,kBAAkBA,CAACC,GAAG,EAAEC,UAAU,EAAE;AAClCP,QAAAA,KAAK,CAACQ,cAAc,CAACP,IAAI,EAAEK,GAAG,EAAEC,UAAU,EAAE,CAACE,QAAQ,EAAEnB,MAAM,KAAK;AAChE,UAAA,OAAOmB,QAAQ,GACXrE,QAAQ,CAACsE,SAAS,CAACC,GAAG,CAAWrB,QAAAA,EAAAA,MAAM,CAAG,CAAA,CAAA,GAC1CnD,GAAC,CAACyE,iBAAiB,CAAC,EAAE,EAAEtB,MAAM,CAAC,CAAA;AACrC,SAAC,CAAC,CAAA;OACH;MACDuB,iBAAiBA,CAACP,GAAG,EAAErC,IAAI,EAAE6C,IAAI,GAAG7C,IAAI,EAAEsC,UAAU,EAAE;AACpD,QAAA,OAAOP,KAAK,CAACe,UAAU,CACrBd,IAAI,EACJK,GAAG,EACHrC,IAAI,EACJsC,UAAU,EACV,CAACE,QAAQ,EAAEnB,MAAM,EAAErB,IAAI,KAAK;UAC1B,MAAMe,EAAE,GAAGiB,IAAI,CAACnC,KAAK,CAACkD,qBAAqB,CAACF,IAAI,CAAC,CAAA;UACjD,OAAO;YACL9C,IAAI,EAAEyC,QAAQ,GACVZ,KAAK,CAACzD,QAAQ,CAACsE,SAAS,CAACC,GAAG,CAAA;AAC9C,sBAAA,EAAwB3B,EAAE,CAAA,WAAA,EAAcM,MAAM,CAAA,EAAA,EAAKrB,IAAI,CAAA;AACvD,gBAAA,CAAiB,CAAC,GACA9B,GAAC,CAACyE,iBAAiB,CAAC,CAACzE,GAAC,CAAC8E,eAAe,CAACjC,EAAE,EAAEf,IAAI,CAAC,CAAC,EAAEqB,MAAM,CAAC;YAC9DrB,IAAI,EAAEe,EAAE,CAACf,IAAAA;WACV,CAAA;AACH,SACF,CAAC,CAAA;OACF;MACDiD,mBAAmBA,CAACZ,GAAG,EAAEQ,IAAI,GAAGR,GAAG,EAAEC,UAAU,EAAE;AAC/C,QAAA,OAAOP,KAAK,CAACe,UAAU,CACrBd,IAAI,EACJK,GAAG,EACH,SAAS,EACTC,UAAU,EACV,CAACE,QAAQ,EAAEnB,MAAM,KAAK;UACpB,MAAMN,EAAE,GAAGiB,IAAI,CAACnC,KAAK,CAACkD,qBAAqB,CAACF,IAAI,CAAC,CAAA;UACjD,OAAO;AACL9C,YAAAA,IAAI,EAAEyC,QAAQ,GACVZ,KAAK,CAACzD,QAAQ,CAACsE,SAAS,CAACC,GAAG,CAAO3B,IAAAA,EAAAA,EAAE,cAAcM,MAAM,CAAA,CAAA,CAAG,CAAC,GAC7DnD,GAAC,CAACyE,iBAAiB,CAAC,CAACzE,GAAC,CAACgF,sBAAsB,CAACnC,EAAE,CAAC,CAAC,EAAEM,MAAM,CAAC;YAC/DrB,IAAI,EAAEe,EAAE,CAACf,IAAAA;WACV,CAAA;AACH,SACF,CAAC,CAAA;AACH,OAAA;KACD,CAAA;GACF,CAAA;AACH;;;ACtMS/B,EAAAA,KAAK,EAAIC,CAAAA;AAAC,CAAA,GAAAE,MAAA,CAAAC,OAAA,IAAAD,MAAA,CAAA;AAIJ,MAAM+E,qBAAqB,CAAC;AAUzCC,EAAAA,WAAWA,CACTC,QAAiC,EACjCC,iBAA0C,EAC1C;AACA,IAAA,IAAI,CAACC,QAAQ,GAAG,IAAIC,OAAO,EAAE,CAAA;AAC7B,IAAA,IAAI,CAACC,iBAAiB,GAAG,IAAID,OAAO,EAAE,CAAA;AACtC,IAAA,IAAI,CAACE,YAAY,GAAG,IAAIF,OAAO,EAAE,CAAA;IACjC,IAAI,CAACG,SAAS,GAAGN,QAAQ,CAAA;IACzB,IAAI,CAACO,kBAAkB,GAAGN,iBAAiB,CAAA;AAC7C,GAAA;EAEAf,cAAcA,CACZsB,WAAgC,EAChCxB,GAAW,EACXC,UAAkB,EAClBwB,MAGgC,EAChC;IACA,MAAM9E,GAAG,GAAG,IAAI,CAAC+E,aAAa,CAACF,WAAW,EAAExB,GAAG,CAAC,CAAA;AAChD,IAAA,MAAM2B,OAAO,GAAG,IAAI,CAACC,OAAO,CAC1B,IAAI,CAACR,iBAAiB,EACtBI,WAAW,EACXnF,GACF,CAAC,CAAA;AAED,IAAA,IAAIsF,OAAO,CAACnF,GAAG,CAACG,GAAG,CAAC,EAAE,OAAA;IAEtB,MAAMe,IAAI,GAAG+D,MAAM,CACjBD,WAAW,CAAC9D,IAAI,CAACmE,UAAU,KAAK,QAAQ,EACxChG,CAAC,CAACiG,aAAa,CAAC,IAAI,CAACR,SAAS,CAACtB,GAAG,CAAC,CACrC,CAAC,CAAA;AACD2B,IAAAA,OAAO,CAAClF,GAAG,CAACE,GAAG,CAAC,CAAA;IAChB,IAAI,CAACoF,aAAa,CAACP,WAAW,EAAE9D,IAAI,EAAEuC,UAAU,CAAC,CAAA;AACnD,GAAA;EAEAQ,UAAUA,CACRe,WAAgC,EAChCxB,GAAW,EACXrC,IAAY,EACZsC,UAAkB,EAClBwB,MAMwD,EACxD;IACA,MAAM9E,GAAG,GAAG,IAAI,CAAC+E,aAAa,CAACF,WAAW,EAAExB,GAAG,EAAErC,IAAI,CAAC,CAAA;AACtD,IAAA,MAAMgE,OAAO,GAAG,IAAI,CAACC,OAAO,CAC1B,IAAI,CAACV,QAAQ,EACbM,WAAW,EACXQ,GACF,CAAC,CAAA;AAED,IAAA,IAAI,CAACL,OAAO,CAACnF,GAAG,CAACG,GAAG,CAAC,EAAE;MACrB,MAAM;QAAEe,IAAI;AAAEC,QAAAA,IAAI,EAAEe,EAAAA;AAAG,OAAC,GAAG+C,MAAM,CAC/BD,WAAW,CAAC9D,IAAI,CAACmE,UAAU,KAAK,QAAQ,EACxChG,CAAC,CAACiG,aAAa,CAAC,IAAI,CAACR,SAAS,CAACtB,GAAG,CAAC,CAAC,EACpCnE,CAAC,CAACoG,UAAU,CAACtE,IAAI,CACnB,CAAC,CAAA;AACDgE,MAAAA,OAAO,CAACO,GAAG,CAACvF,GAAG,EAAE+B,EAAE,CAAC,CAAA;MACpB,IAAI,CAACqD,aAAa,CAACP,WAAW,EAAE9D,IAAI,EAAEuC,UAAU,CAAC,CAAA;AACnD,KAAA;IAEA,OAAOpE,CAAC,CAACoG,UAAU,CAACN,OAAO,CAACvE,GAAG,CAACT,GAAG,CAAC,CAAC,CAAA;AACvC,GAAA;AAEAoF,EAAAA,aAAaA,CACXP,WAAgC,EAChC9D,IAAiC,EACjCuC,UAAkB,EAClB;AAAA,IAAA,IAAAkC,qBAAA,CAAA;AACA,IAAA,MAAMC,QAAQ,GAAG,IAAI,CAACb,kBAAkB,CAACtB,UAAU,CAAC,CAAA;AACpD,IAAA,MAAMoC,WAAW,GAAA,CAAAF,qBAAA,GAAG,IAAI,CAACd,YAAY,CAACjE,GAAG,CAACoE,WAAW,CAAC,KAAAW,IAAAA,GAAAA,qBAAA,GAAI,EAAE,CAAA;AAE5D,IAAA,MAAMG,gBAAgB,GAAIrF,IAAc,IACtCA,IAAI,CAACS,IAAI;AACT;AACA;AACAT,IAAAA,IAAI,CAACkB,MAAM,KAAKqD,WAAW,CAAC9D,IAAI,IAChCT,IAAI,CAACsF,SAAS,KAAKf,WAAW,CAAC9D,IAAI,CAAC8E,IAAI,CAAA;AAE1C,IAAA,IAAIC,IAAc,CAAA;IAElB,IAAIL,QAAQ,KAAKM,QAAQ,EAAE;AACzB;AACA,MAAA,IAAIL,WAAW,CAACtD,MAAM,GAAG,CAAC,EAAE;QAC1B0D,IAAI,GAAGJ,WAAW,CAACA,WAAW,CAACtD,MAAM,GAAG,CAAC,CAAC,CAAC9B,IAAI,CAAA;QAC/C,IAAI,CAACqF,gBAAgB,CAACG,IAAI,CAAC,EAAEA,IAAI,GAAGE,SAAS,CAAA;AAC/C,OAAA;AACF,KAAC,MAAM;AACL,MAAA,KAAK,MAAM,CAACC,CAAC,EAAEC,IAAI,CAAC,IAAIR,WAAW,CAACS,OAAO,EAAE,EAAE;QAC7C,MAAM;UAAE7F,IAAI;AAAE8F,UAAAA,KAAAA;AAAM,SAAC,GAAGF,IAAI,CAAA;AAC5B,QAAA,IAAIP,gBAAgB,CAACrF,IAAI,CAAC,EAAE;UAC1B,IAAImF,QAAQ,GAAGW,KAAK,EAAE;YACpB,MAAM,CAACC,OAAO,CAAC,GAAG/F,IAAI,CAACgG,YAAY,CAACvF,IAAI,CAAC,CAAA;AACzC2E,YAAAA,WAAW,CAACa,MAAM,CAACN,CAAC,EAAE,CAAC,EAAE;AAAE3F,cAAAA,IAAI,EAAE+F,OAAO;AAAED,cAAAA,KAAK,EAAEX,QAAAA;AAAS,aAAC,CAAC,CAAA;AAC5D,YAAA,OAAA;AACF,WAAA;AACAK,UAAAA,IAAI,GAAGxF,IAAI,CAAA;AACb,SAAA;AACF,OAAA;AACF,KAAA;AAEA,IAAA,IAAIwF,IAAI,EAAE;MACR,MAAM,CAACO,OAAO,CAAC,GAAGP,IAAI,CAACU,WAAW,CAACzF,IAAI,CAAC,CAAA;MACxC2E,WAAW,CAACe,IAAI,CAAC;AAAEnG,QAAAA,IAAI,EAAE+F,OAAO;AAAED,QAAAA,KAAK,EAAEX,QAAAA;AAAS,OAAC,CAAC,CAAA;AACtD,KAAC,MAAM;AACL,MAAA,MAAM,CAACY,OAAO,CAAC,GAAGxB,WAAW,CAAC6B,gBAAgB,CAAC,MAAM,EAAE,CAAC3F,IAAI,CAAC,CAAC,CAAA;AAC9D,MAAA,IAAI,CAAC2D,YAAY,CAACa,GAAG,CAACV,WAAW,EAAE,CAAC;AAAEvE,QAAAA,IAAI,EAAE+F,OAAO;AAAED,QAAAA,KAAK,EAAEX,QAAAA;AAAS,OAAC,CAAC,CAAC,CAAA;AAC1E,KAAA;AACF,GAAA;AAEAR,EAAAA,OAAOA,CACL0B,GAAoC,EACpC9B,WAAgC,EAChC+B,UAAqC,EAClC;AACH,IAAA,IAAIC,UAAU,GAAGF,GAAG,CAAClG,GAAG,CAACoE,WAAW,CAAC,CAAA;IACrC,IAAI,CAACgC,UAAU,EAAE;AACfA,MAAAA,UAAU,GAAG,IAAID,UAAU,EAAE,CAAA;AAC7BD,MAAAA,GAAG,CAACpB,GAAG,CAACV,WAAW,EAAEgC,UAAU,CAAC,CAAA;AAClC,KAAA;AACA,IAAA,OAAOA,UAAU,CAAA;AACnB,GAAA;EAEA9B,aAAaA,CACXF,WAAgC,EAChCxB,GAAW,EACXrC,IAAY,GAAG,EAAE,EACT;IACR,MAAM;AAAEkE,MAAAA,UAAAA;KAAY,GAAGL,WAAW,CAAC9D,IAAI,CAAA;;AAEvC;AACA;AACA;IACA,OAAO,CAAA,EAAGC,IAAI,IAAIkE,UAAU,KAAK7B,GAAG,CAAA,EAAA,EAAKrC,IAAI,CAAE,CAAA,CAAA;AACjD,GAAA;AACF;;ACxJO,MAAM8F,0BAA0B,GACrC,+EAA+E,CAAA;AAE1E,SAASC,yBAAyBA,CAACC,OAAgB,EAAU;AAClE,EAAA,OAAOC,IAAI,CAACC,SAAS,CAACC,eAAe,CAACH,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;AAC1D;;ACFA,SAASI,eAAeA,CAACC,OAAgB,EAAiB;AACxD,EAAA,IAAIA,OAAO,YAAYC,MAAM,EAAE,OAAOD,OAAO,CAAA;EAE7C,IAAI;AACF,IAAA,OAAO,IAAIC,MAAM,CAAC,CAAID,CAAAA,EAAAA,OAAO,GAAG,CAAC,CAAA;AACnC,GAAC,CAAC,MAAM;AACN,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;AACF,CAAA;AAEA,SAASE,gBAAgBA,CAACC,KAAK,EAAEC,MAAM,EAAE;AACvC,EAAA,IAAI,CAACA,MAAM,CAACrF,MAAM,EAAE,OAAO,EAAE,CAAA;EAC7B,OACE,CAAA,mBAAA,EAAsBoF,KAAK,CAAyC,uCAAA,CAAA,GACpEC,MAAM,CAACd,GAAG,CAACe,QAAQ,IAAI,OAAOC,MAAM,CAACD,QAAQ,CAAC,CAAA,EAAA,CAAI,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC,CAAA;AAEhE,CAAA;AAEA,SAASC,mBAAmBA,CAACC,UAAU,EAAE;AACvC,EAAA,IAAI,CAACA,UAAU,CAACC,IAAI,EAAE,OAAO,EAAE,CAAA;AAC/B,EAAA,OACE,sFAAsF,GACtFC,KAAK,CAACC,IAAI,CAACH,UAAU,EAAE9G,IAAI,IAAI,CAAA,IAAA,EAAOA,IAAI,CAAI,EAAA,CAAA,CAAC,CAAC4G,IAAI,CAAC,EAAE,CAAC,CAAA;AAE5D,CAAA;AAEO,SAASM,sBAAsBA,CACpCC,QAAgB,EAChBC,SAA+B,EAC/BC,eAA0B,EAC1BC,eAA0B,EAC1B;AACA,EAAA,IAAIC,OAAO,CAAA;EACX,MAAMC,MAAM,GAAGnB,OAAO,IAAI;AACxB,IAAA,MAAMoB,MAAM,GAAGrB,eAAe,CAACC,OAAO,CAAC,CAAA;AACvC,IAAA,IAAI,CAACoB,MAAM,EAAE,OAAO,KAAK,CAAA;IAEzB,IAAIC,OAAO,GAAG,KAAK,CAAA;IACnB,KAAK,MAAMC,QAAQ,IAAIP,SAAS,CAACQ,IAAI,EAAE,EAAE;AACvC,MAAA,IAAIH,MAAM,CAACI,IAAI,CAACF,QAAQ,CAAC,EAAE;AACzBD,QAAAA,OAAO,GAAG,IAAI,CAAA;AACdH,QAAAA,OAAO,CAACzI,GAAG,CAAC6I,QAAQ,CAAC,CAAA;AACvB,OAAA;AACF,KAAA;AACA,IAAA,OAAO,CAACD,OAAO,CAAA;GAChB,CAAA;;AAED;AACA,EAAA,MAAMI,OAAO,GAAGP,OAAO,GAAG,IAAI7I,GAAG,EAAW,CAAA;AAC5C,EAAA,MAAMqJ,aAAa,GAAGf,KAAK,CAACC,IAAI,CAACI,eAAe,CAAC,CAACG,MAAM,CAACA,MAAM,CAAC,CAAA;;AAEhE;AACA,EAAA,MAAMQ,OAAO,GAAGT,OAAO,GAAG,IAAI7I,GAAG,EAAW,CAAA;AAC5C,EAAA,MAAMuJ,aAAa,GAAGjB,KAAK,CAACC,IAAI,CAACK,eAAe,CAAC,CAACE,MAAM,CAACA,MAAM,CAAC,CAAA;AAEhE,EAAA,MAAMV,UAAU,GAAGxI,YAAY,CAACwJ,OAAO,EAAEE,OAAO,CAAC,CAAA;AAEjD,EAAA,IACElB,UAAU,CAACC,IAAI,GAAG,CAAC,IACnBgB,aAAa,CAAC3G,MAAM,GAAG,CAAC,IACxB6G,aAAa,CAAC7G,MAAM,GAAG,CAAC,EACxB;IACA,MAAM,IAAI8G,KAAK,CACb,CAA+Bf,4BAAAA,EAAAA,QAAQ,uBAAuB,GAC5DZ,gBAAgB,CAAC,SAAS,EAAEwB,aAAa,CAAC,GAC1CxB,gBAAgB,CAAC,SAAS,EAAE0B,aAAa,CAAC,GAC1CpB,mBAAmB,CAACC,UAAU,CAClC,CAAC,CAAA;AACH,GAAA;EAEA,OAAO;IAAEgB,OAAO;AAAEE,IAAAA,OAAAA;GAAS,CAAA;AAC7B,CAAA;AAEO,SAASG,gCAAgCA,CAC9CC,OAAsB,EACtBC,QAAa,EACc;EAC3B,MAAM;AAAEC,IAAAA,mBAAmB,GAAG,EAAC;AAAE,GAAC,GAAGF,OAAO,CAAA;AAC5C,EAAA,IAAIE,mBAAmB,KAAK,KAAK,EAAE,OAAO,KAAK,CAAA;AAE/C,EAAA,MAAMC,MAAM,GAAGF,QAAQ,CAACE,MAAM,CAACA,MAAM,IAAIA,MAAM,IAAA,IAAA,GAAA,KAAA,CAAA,GAANA,MAAM,CAAEvI,IAAI,CAAC,CAAA;EAEtD,MAAM;AACJwI,IAAAA,GAAG,GAAG,UAAU;AAChBC,IAAAA,MAAM,GAAGF,MAAM,KAAK,qBAAqB,GAAG,OAAO,GAAG,QAAQ;AAC9DG,IAAAA,GAAG,GAAG,KAAA;AACR,GAAC,GAAGJ,mBAAmB,CAAA;EAEvB,OAAO;IAAEE,GAAG;IAAEC,MAAM;AAAEC,IAAAA,GAAAA;GAAK,CAAA;AAC7B;;AC1FA,SAASC,SAASA,CAACrJ,IAAc,EAAE;AACjC,EAAA,IAAIA,IAAI,CAACsJ,OAAO,EAAE,OAAO,IAAI,CAAA;AAC7B,EAAA,IAAI,CAACtJ,IAAI,CAACuJ,UAAU,EAAE,OAAO,KAAK,CAAA;EAClC,IAAIvJ,IAAI,CAACwJ,OAAO,EAAE;AAAA,IAAA,IAAAC,qBAAA,CAAA;AAChB,IAAA,IAAI,EAAAA,CAAAA,qBAAA,GAACzJ,IAAI,CAACuJ,UAAU,CAAC9I,IAAI,KAAAgJ,IAAAA,IAAAA,CAAAA,qBAAA,GAApBA,qBAAA,CAAuBzJ,IAAI,CAACwJ,OAAO,CAAC,KAAA,IAAA,IAApCC,qBAAA,CAAsCC,QAAQ,CAAC1J,IAAI,CAACS,IAAI,CAAC,CAAE,EAAA,OAAO,IAAI,CAAA;AAC7E,GAAC,MAAM;AAAA,IAAA,IAAAkJ,sBAAA,CAAA;IACL,IAAI,CAAA,CAAAA,sBAAA,GAAA3J,IAAI,CAACuJ,UAAU,CAAC9I,IAAI,KAApBkJ,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,sBAAA,CAAuB3J,IAAI,CAACN,GAAG,CAAC,MAAKM,IAAI,CAACS,IAAI,EAAE,OAAO,IAAI,CAAA;AACjE,GAAA;AACA,EAAA,OAAO4I,SAAS,CAACrJ,IAAI,CAACuJ,UAAU,CAAC,CAAA;AACnC,CAAA;AAEA,YAAgBK,YAA0B,IAAK;EAC7C,SAASC,QAAQA,CAACpK,MAAM,EAAEC,GAAG,EAAEgC,SAAS,EAAE1B,IAAI,EAAE;AAC9C,IAAA,OAAO4J,YAAY,CAAC;AAAEE,MAAAA,IAAI,EAAE,UAAU;MAAErK,MAAM;MAAEC,GAAG;AAAEgC,MAAAA,SAAAA;KAAW,EAAE1B,IAAI,CAAC,CAAA;AACzE,GAAA;EAEA,SAAS+J,0BAA0BA,CAAC/J,IAAI,EAAE;IACxC,MAAM;AACJS,MAAAA,IAAI,EAAE;AAAEC,QAAAA,IAAAA;OAAM;AACdH,MAAAA,KAAAA;AACF,KAAC,GAAGP,IAAI,CAAA;AACR,IAAA,IAAIO,KAAK,CAACyJ,oBAAoB,CAACtJ,IAAI,CAAC,EAAE,OAAA;AAEtCkJ,IAAAA,YAAY,CAAC;AAAEE,MAAAA,IAAI,EAAE,QAAQ;AAAEpJ,MAAAA,IAAAA;KAAM,EAAEV,IAAI,CAAC,CAAA;AAC9C,GAAA;EAEA,SAASiK,uBAAuBA,CAC9BjK,IAA+D,EAC/D;AACA,IAAA,MAAMN,GAAG,GAAGoB,UAAU,CAACd,IAAI,CAACG,GAAG,CAAC,UAAU,CAAC,EAAEH,IAAI,CAACS,IAAI,CAACM,QAAQ,CAAC,CAAA;IAChE,OAAO;MAAErB,GAAG;AAAEwK,MAAAA,wBAAwB,EAAE,CAAC,CAACxK,GAAG,IAAIA,GAAG,KAAK,WAAA;KAAa,CAAA;AACxE,GAAA;EAEA,OAAO;AACL;IACAyK,oBAAoBA,CAACnK,IAA4B,EAAE;MACjD,MAAM;AAAEuJ,QAAAA,UAAAA;AAAW,OAAC,GAAGvJ,IAAI,CAAA;MAC3B,IACEuJ,UAAU,CAACpI,kBAAkB,CAAC;QAAE1B,MAAM,EAAEO,IAAI,CAACS,IAAAA;OAAM,CAAC,IACpDwJ,uBAAuB,CAACV,UAAU,CAAC,CAACW,wBAAwB,EAC5D;AACA,QAAA,OAAA;AACF,OAAA;MACAH,0BAA0B,CAAC/J,IAAI,CAAC,CAAA;KACjC;IAED,2CAA2CoK,CACzCpK,IAA+D,EAC/D;MACA,MAAM;QAAEN,GAAG;AAAEwK,QAAAA,wBAAAA;AAAyB,OAAC,GAAGD,uBAAuB,CAACjK,IAAI,CAAC,CAAA;MACvE,IAAI,CAACkK,wBAAwB,EAAE,OAAA;AAE/B,MAAA,MAAMzK,MAAM,GAAGO,IAAI,CAACG,GAAG,CAAC,QAAQ,CAAC,CAAA;AACjC,MAAA,IAAIkK,wBAAwB,GAAG5K,MAAM,CAACW,YAAY,EAAE,CAAA;AACpD,MAAA,IAAIiK,wBAAwB,EAAE;AAC5B,QAAA,MAAM/J,OAAO,GAAGb,MAAM,CAACc,KAAK,CAACC,UAAU,CACpCf,MAAM,CAACgB,IAAI,CAAkBC,IAChC,CAAC,CAAA;AACD,QAAA,IAAIJ,OAAO,EAAE;AACX,UAAA,IAAIA,OAAO,CAACN,IAAI,CAACsK,0BAA0B,EAAE,EAAE,OAAA;AAC/CD,UAAAA,wBAAwB,GAAG,KAAK,CAAA;AAClC,SAAA;AACF,OAAA;AAEA,MAAA,MAAMtI,MAAM,GAAGR,aAAa,CAAC9B,MAAM,CAAC,CAAA;AACpC,MAAA,IAAI8K,UAAU,GAAGV,QAAQ,CAAC9H,MAAM,CAACN,EAAE,EAAE/B,GAAG,EAAEqC,MAAM,CAACL,SAAS,EAAE1B,IAAI,CAAC,CAAA;AACjEuK,MAAAA,UAAU,KAAVA,UAAU,GACR,CAACF,wBAAwB,IACzBrK,IAAI,CAACwK,UAAU,IACf/K,MAAM,CAAC+K,UAAU,IACjBnB,SAAS,CAAC5J,MAAM,CAAC,CAAA,CAAA;AAEnB,MAAA,IAAI,CAAC8K,UAAU,EAAER,0BAA0B,CAACtK,MAAM,CAAC,CAAA;KACpD;IAEDgL,aAAaA,CAACzK,IAA+B,EAAE;MAC7C,MAAM;QAAEuJ,UAAU;AAAErI,QAAAA,MAAAA;AAAO,OAAC,GAAGlB,IAAI,CAAA;AACnC,MAAA,IAAIwB,GAAG,CAAA;;AAEP;AACA,MAAA,IAAI+H,UAAU,CAACrJ,oBAAoB,EAAE,EAAE;AACrCsB,QAAAA,GAAG,GAAG+H,UAAU,CAACpJ,GAAG,CAAC,MAAM,CAAC,CAAA;AAC5B;AACF,OAAC,MAAM,IAAIoJ,UAAU,CAACmB,sBAAsB,EAAE,EAAE;AAC9ClJ,QAAAA,GAAG,GAAG+H,UAAU,CAACpJ,GAAG,CAAC,OAAO,CAAC,CAAA;AAC7B;AACA;AACF,OAAC,MAAM,IAAIoJ,UAAU,CAACoB,UAAU,EAAE,EAAE;AAClC,QAAA,MAAMC,KAAK,GAAGrB,UAAU,CAACA,UAAU,CAAA;QACnC,IAAIqB,KAAK,CAACzI,gBAAgB,EAAE,IAAIyI,KAAK,CAACC,eAAe,EAAE,EAAE;AACvD,UAAA,IAAID,KAAK,CAACnK,IAAI,CAAC2B,MAAM,KAAKlB,MAAM,EAAE;YAChCM,GAAG,GAAGoJ,KAAK,CAACzK,GAAG,CAAC,WAAW,CAAC,CAACH,IAAI,CAACN,GAAG,CAAC,CAAA;AACxC,WAAA;AACF,SAAA;AACF,OAAA;MAEA,IAAI+B,EAAE,GAAG,IAAI,CAAA;MACb,IAAIC,SAAS,GAAG,IAAI,CAAA;MACpB,IAAIF,GAAG,EAAE,CAAC;QAAEC,EAAE;AAAEC,QAAAA,SAAAA;AAAU,OAAC,GAAGH,aAAa,CAACC,GAAG,CAAC,EAAA;MAEhD,KAAK,MAAMsJ,IAAI,IAAI9K,IAAI,CAACG,GAAG,CAAC,YAAY,CAAC,EAAE;AACzC,QAAA,IAAI2K,IAAI,CAACC,gBAAgB,EAAE,EAAE;UAC3B,MAAMrL,GAAG,GAAGoB,UAAU,CAACgK,IAAI,CAAC3K,GAAG,CAAC,KAAK,CAAC,CAAC,CAAA;UACvC,IAAIT,GAAG,EAAEmK,QAAQ,CAACpI,EAAE,EAAE/B,GAAG,EAAEgC,SAAS,EAAEoJ,IAAI,CAAC,CAAA;AAC7C,SAAA;AACF,OAAA;KACD;IAEDE,gBAAgBA,CAAChL,IAAkC,EAAE;AACnD,MAAA,IAAIA,IAAI,CAACS,IAAI,CAACwK,QAAQ,KAAK,IAAI,EAAE,OAAA;MAEjC,MAAMlJ,MAAM,GAAGR,aAAa,CAACvB,IAAI,CAACG,GAAG,CAAC,OAAO,CAAC,CAAC,CAAA;AAC/C,MAAA,MAAMT,GAAG,GAAGoB,UAAU,CAACd,IAAI,CAACG,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAA;MAE9C,IAAI,CAACT,GAAG,EAAE,OAAA;AAEVkK,MAAAA,YAAY,CACV;AACEE,QAAAA,IAAI,EAAE,IAAI;QACVrK,MAAM,EAAEsC,MAAM,CAACN,EAAE;QACjB/B,GAAG;QACHgC,SAAS,EAAEK,MAAM,CAACL,SAAAA;OACnB,EACD1B,IACF,CAAC,CAAA;AACH,KAAA;GACD,CAAA;AACH,CAAC;;AC/HD,YAAgB4J,YAA0B,KAAM;EAC9CsB,iBAAiBA,CAAClL,IAAmC,EAAE;AACrD,IAAA,MAAM+B,MAAM,GAAGH,eAAe,CAAC5B,IAAI,CAAC,CAAA;IACpC,IAAI,CAAC+B,MAAM,EAAE,OAAA;AACb6H,IAAAA,YAAY,CAAC;AAAEE,MAAAA,IAAI,EAAE,QAAQ;AAAE/H,MAAAA,MAAAA;KAAQ,EAAE/B,IAAI,CAAC,CAAA;GAC/C;EACDmL,OAAOA,CAACnL,IAAyB,EAAE;IACjCA,IAAI,CAACG,GAAG,CAAC,MAAM,CAAC,CAACd,OAAO,CAAC+L,QAAQ,IAAI;AACnC,MAAA,MAAMrJ,MAAM,GAAGC,gBAAgB,CAACoJ,QAAQ,CAAC,CAAA;MACzC,IAAI,CAACrJ,MAAM,EAAE,OAAA;AACb6H,MAAAA,YAAY,CAAC;AAAEE,QAAAA,IAAI,EAAE,QAAQ;AAAE/H,QAAAA,MAAAA;OAAQ,EAAEqJ,QAAQ,CAAC,CAAA;AACpD,KAAC,CAAC,CAAA;AACJ,GAAA;AACF,CAAC,CAAC;;ACnBK,SAASrL,OAAOA,CACrBsL,OAAe,EACfrI,UAAkB,EAClBsI,eAAiC,EACzB;AACR,EAAA,IAAIA,eAAe,KAAK,KAAK,EAAE,OAAOtI,UAAU,CAAA;AAEhD,EAAA,MAAM,IAAI4F,KAAK,CACb,CAAA,uEAAA,CACF,CAAC,CAAA;AACH,CAAA;;AAEA;AACO,SAASrJ,GAAGA,CAACgM,OAAe,EAAE7K,IAAY,EAAE;AACjD,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACO,SAAS8K,UAAUA,CAACC,WAAwB,EAAE,EAAC;;AAEtD;AACO,SAASC,eAAeA,CAACD,WAAwB,EAAE;;ACX1D,MAAME,qBAAqB,GAAG,IAAIvM,GAAG,CAAS,CAC5C,QAAQ,EACR,YAAY,EACZ,MAAM,EACN,QAAQ,CACT,CAAC,CAAA;AAEa,SAASwM,kBAAkBA,CACxC9D,SAA+B,EAChB;EACf,MAAM;AAAE+D,IAAAA,MAAM,EAAEC,OAAO;AAAEC,IAAAA,QAAQ,EAAEC,SAAS;AAAEC,IAAAA,MAAM,EAAEC,OAAAA;AAAQ,GAAC,GAAGpE,SAAS,CAAA;AAE3E,EAAA,OAAOqE,IAAI,IAAI;AACb,IAAA,IAAIA,IAAI,CAACrC,IAAI,KAAK,QAAQ,IAAIoC,OAAO,IAAI3M,KAAG,CAAC2M,OAAO,EAAEC,IAAI,CAACzL,IAAI,CAAC,EAAE;MAChE,OAAO;AAAEoJ,QAAAA,IAAI,EAAE,QAAQ;AAAEsC,QAAAA,IAAI,EAAEF,OAAO,CAACC,IAAI,CAACzL,IAAI,CAAC;QAAEA,IAAI,EAAEyL,IAAI,CAACzL,IAAAA;OAAM,CAAA;AACtE,KAAA;IAEA,IAAIyL,IAAI,CAACrC,IAAI,KAAK,UAAU,IAAIqC,IAAI,CAACrC,IAAI,KAAK,IAAI,EAAE;MAClD,MAAM;QAAEpI,SAAS;QAAEjC,MAAM;AAAEC,QAAAA,GAAAA;AAAI,OAAC,GAAGyM,IAAI,CAAA;AAEvC,MAAA,IAAI1M,MAAM,IAAIiC,SAAS,KAAK,QAAQ,EAAE;AACpC,QAAA,IAAIwK,OAAO,IAAIP,qBAAqB,CAACpM,GAAG,CAACE,MAAM,CAAC,IAAIF,KAAG,CAAC2M,OAAO,EAAExM,GAAG,CAAC,EAAE;UACrE,OAAO;AAAEoK,YAAAA,IAAI,EAAE,QAAQ;AAAEsC,YAAAA,IAAI,EAAEF,OAAO,CAACxM,GAAG,CAAC;AAAEgB,YAAAA,IAAI,EAAEhB,GAAAA;WAAK,CAAA;AAC1D,SAAA;AAEA,QAAA,IAAIoM,OAAO,IAAIvM,KAAG,CAACuM,OAAO,EAAErM,MAAM,CAAC,IAAIF,KAAG,CAACuM,OAAO,CAACrM,MAAM,CAAC,EAAEC,GAAG,CAAC,EAAE;UAChE,OAAO;AACLoK,YAAAA,IAAI,EAAE,QAAQ;AACdsC,YAAAA,IAAI,EAAEN,OAAO,CAACrM,MAAM,CAAC,CAACC,GAAG,CAAC;AAC1BgB,YAAAA,IAAI,EAAE,CAAA,EAAGjB,MAAM,CAAA,CAAA,EAAIC,GAAG,CAAA,CAAA;WACvB,CAAA;AACH,SAAA;AACF,OAAA;MAEA,IAAIsM,SAAS,IAAIzM,KAAG,CAACyM,SAAS,EAAEtM,GAAG,CAAC,EAAE;QACpC,OAAO;AAAEoK,UAAAA,IAAI,EAAE,UAAU;AAAEsC,UAAAA,IAAI,EAAEJ,SAAS,CAACtM,GAAG,CAAC;UAAEgB,IAAI,EAAE,GAAGhB,GAAG,CAAA,CAAA;SAAI,CAAA;AACnE,OAAA;AACF,KAAA;GACD,CAAA;AACH;;AC1CA,MAAM2M,UAAU,GAAGC,WAAW,CAACvN,OAAO,IAAIuN,WAAW,CAAA;AA8BrD,SAASC,cAAcA,CACrBzD,OAAsB,EACtBC,QAAQ,EAWR;EACA,MAAM;IACJyD,MAAM;AACN9F,IAAAA,OAAO,EAAE+F,aAAa;IACtBC,wBAAwB;IACxBC,UAAU;IACVC,KAAK;IACLC,oBAAoB;IACpBvB,eAAe;IACf,GAAGwB,eAAAA;AACL,GAAC,GAAGhE,OAAO,CAAA;AAEX,EAAA,IAAIiE,OAAO,CAACjE,OAAO,CAAC,EAAE;IACpB,MAAM,IAAIF,KAAK,CACb,CAAA;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oFAAA,CACI,CAAC,CAAA;AACH,GAAA;AAEA,EAAA,IAAIoE,UAAU,CAAA;AACd,EAAA,IAAIR,MAAM,KAAK,cAAc,EAAEQ,UAAU,GAAG,aAAa,CAAC,KACrD,IAAIR,MAAM,KAAK,cAAc,EAAEQ,UAAU,GAAG,aAAa,CAAC,KAC1D,IAAIR,MAAM,KAAK,YAAY,EAAEQ,UAAU,GAAG,WAAW,CAAC,KACtD,IAAI,OAAOR,MAAM,KAAK,QAAQ,EAAE;AACnC,IAAA,MAAM,IAAI5D,KAAK,CAAC,0BAA0B,CAAC,CAAA;AAC7C,GAAC,MAAM;AACL,IAAA,MAAM,IAAIA,KAAK,CACb,CAAA,qDAAA,CAAuD,GACrD,CAAA,2BAAA,EAA8BjC,IAAI,CAACC,SAAS,CAAC4F,MAAM,CAAC,GACxD,CAAC,CAAA;AACH,GAAA;AAEA,EAAA,IAAI,OAAOK,oBAAoB,KAAK,UAAU,EAAE;AAC9C,IAAA,IAAI/D,OAAO,CAACN,OAAO,IAAIM,OAAO,CAACJ,OAAO,EAAE;AACtC,MAAA,MAAM,IAAIE,KAAK,CACb,CAAwD,sDAAA,CAAA,GACtD,kCACJ,CAAC,CAAA;AACH,KAAA;AACF,GAAC,MAAM,IAAIiE,oBAAoB,IAAI,IAAI,EAAE;AACvC,IAAA,MAAM,IAAIjE,KAAK,CACb,CAAA,sDAAA,CAAwD,GACtD,CAAA,WAAA,EAAcjC,IAAI,CAACC,SAAS,CAACiG,oBAAoB,CAAC,GACtD,CAAC,CAAA;AACH,GAAA;AAEA,EAAA,IACEvB,eAAe,IAAI,IAAI,IACvB,OAAOA,eAAe,KAAK,SAAS,IACpC,OAAOA,eAAe,KAAK,QAAQ,EACnC;AACA,IAAA,MAAM,IAAI1C,KAAK,CACb,CAAA,0DAAA,CAA4D,GAC1D,CAAA,WAAA,EAAcjC,IAAI,CAACC,SAAS,CAAC0E,eAAe,CAAC,GACjD,CAAC,CAAA;AACH,GAAA;AAEA,EAAA,IAAI5E,OAAO,CAAA;AAEX,EAAA;AACE;AACA;AACA+F,EAAAA,aAAa,IACbE,UAAU,IACVD,wBAAwB,EACxB;AACA,IAAA,MAAMO,UAAU,GACd,OAAOR,aAAa,KAAK,QAAQ,IAAI/E,KAAK,CAACwF,OAAO,CAACT,aAAa,CAAC,GAC7D;AAAEU,MAAAA,QAAQ,EAAEV,aAAAA;AAAc,KAAC,GAC3BA,aAAa,CAAA;AAEnB/F,IAAAA,OAAO,GAAG2F,UAAU,CAACY,UAAU,EAAE;MAC/BP,wBAAwB;AACxBC,MAAAA,UAAAA;AACF,KAAC,CAAC,CAAA;AACJ,GAAC,MAAM;AACLjG,IAAAA,OAAO,GAAGqC,QAAQ,CAACrC,OAAO,EAAE,CAAA;AAC9B,GAAA;EAEA,OAAO;IACL8F,MAAM;IACNQ,UAAU;IACVtG,OAAO;AACP4E,IAAAA,eAAe,EAAEA,eAAe,IAAfA,IAAAA,GAAAA,eAAe,GAAI,KAAK;IACzCuB,oBAAoB;IACpBD,KAAK,EAAE,CAAC,CAACA,KAAK;AACdE,IAAAA,eAAe,EAAEA,eAAAA;GAClB,CAAA;AACH,CAAA;AAEA,SAASM,mBAAmBA,CAC1BC,OAAkC,EAClCvE,OAAsB,EACtBE,mBAAmB,EACnBqC,OAAO,EACPiC,QAAQ,EACRvE,QAAQ,EACR;EACA,MAAM;IACJyD,MAAM;IACNQ,UAAU;IACVtG,OAAO;IACPkG,KAAK;IACLC,oBAAoB;IACpBC,eAAe;AACfxB,IAAAA,eAAAA;AACF,GAAC,GAAGiB,cAAc,CAAUzD,OAAO,EAAEC,QAAQ,CAAC,CAAA;;AAE9C;EACA,IAAIP,OAAO,EAAEE,OAAO,CAAA;AACpB,EAAA,IAAI6E,gBAAgB,CAAA;AACpB,EAAA,IAAIC,cAA+C,CAAA;AACnD,EAAA,IAAIC,eAAe,CAAA;EAEnB,MAAMC,QAAQ,GAAGlL,iBAAiB,CAChC,IAAIqB,qBAAqB,CACvBb,UAAU,IAAI2K,OAAY,CAACtC,OAAO,EAAErI,UAAU,EAAEsI,eAAe,CAAC,EAC/D5K,IAAY,IAAA;IAAA,IAAAkN,mBAAA,EAAAC,eAAA,CAAA;AAAA,IAAA,OAAA,CAAAD,mBAAA,GAAA,CAAAC,eAAA,GAAKL,cAAc,KAAdK,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,eAAA,CAAgB1N,GAAG,CAACO,IAAI,CAAC,KAAAkN,IAAAA,GAAAA,mBAAA,GAAInI,QAAQ,CAAA;AAAA,GACzD,CACF,CAAC,CAAA;AAED,EAAA,MAAMqI,SAAS,GAAG,IAAI/I,GAAG,EAAE,CAAA;AAE3B,EAAA,MAAMgJ,GAAgB,GAAG;AACvBC,IAAAA,KAAK,EAAEjF,QAAQ;IACf2E,QAAQ;IACRlB,MAAM,EAAE1D,OAAO,CAAC0D,MAAM;IACtB9F,OAAO;IACPkF,kBAAkB;IAClBiB,oBAAoBA,CAACnM,IAAI,EAAE;MACzB,IAAI8M,cAAc,KAAK9H,SAAS,EAAE;QAChC,MAAM,IAAIkD,KAAK,CACb,CAAyByE,sBAAAA,EAAAA,OAAO,CAAC3M,IAAI,CAAA,WAAA,CAAa,GAChD,CAAA,6DAAA,CACJ,CAAC,CAAA;AACH,OAAA;AACA,MAAA,IAAI,CAAC8M,cAAc,CAACjO,GAAG,CAACmB,IAAI,CAAC,EAAE;QAC7BuN,OAAO,CAACC,IAAI,CACV,CAAyBC,sBAAAA,EAAAA,YAAY,aAAa,GAChD,CAAA,kBAAA,EAAqBzN,IAAI,CAAA,EAAA,CAC7B,CAAC,CAAA;AACH,OAAA;MAEA,IAAI+M,eAAe,IAAI,CAACA,eAAe,CAAC/M,IAAI,CAAC,EAAE,OAAO,KAAK,CAAA;AAE3D,MAAA,IAAI0N,YAAY,GAAGC,UAAU,CAAC3N,IAAI,EAAEgG,OAAO,EAAE;AAC3C4H,QAAAA,UAAU,EAAEf,gBAAgB;AAC5B7D,QAAAA,QAAQ,EAAElB,OAAO;AACjB+F,QAAAA,QAAQ,EAAE7F,OAAAA;AACZ,OAAC,CAAC,CAAA;AAEF,MAAA,IAAImE,oBAAoB,EAAE;AACxBuB,QAAAA,YAAY,GAAGvB,oBAAoB,CAACnM,IAAI,EAAE0N,YAAY,CAAC,CAAA;AACvD,QAAA,IAAI,OAAOA,YAAY,KAAK,SAAS,EAAE;AACrC,UAAA,MAAM,IAAIxF,KAAK,CAAC,CAAA,4CAAA,CAA8C,CAAC,CAAA;AACjE,SAAA;AACF,OAAA;AAEA,MAAA,OAAOwF,YAAY,CAAA;KACpB;IACDxB,KAAKA,CAAClM,IAAI,EAAE;MAAA,IAAA8N,SAAA,EAAAC,qBAAA,CAAA;AACVnB,MAAAA,QAAQ,EAAE,CAACoB,KAAK,GAAG,IAAI,CAAA;AAEvB,MAAA,IAAI,CAAC9B,KAAK,IAAI,CAAClM,IAAI,EAAE,OAAA;MAErB,IAAI4M,QAAQ,EAAE,CAACxF,SAAS,CAACvI,GAAG,CAAC4O,YAAY,CAAC,EAAE,OAAA;MAC5Cb,QAAQ,EAAE,CAACxF,SAAS,CAACtI,GAAG,CAACkB,IAAI,CAAC,CAAA;AAC9B,MAAA,CAAA+N,qBAAA,GAAAD,CAAAA,SAAA,GAAAlB,QAAQ,EAAE,EAACC,gBAAgB,KAAA,IAAA,GAAAkB,qBAAA,GAA3BD,SAAA,CAAWjB,gBAAgB,GAAKA,gBAAgB,CAAA;KACjD;AACDoB,IAAAA,gBAAgBA,CAACjO,IAAI,EAAEkO,OAAO,GAAG,GAAG,EAAE;MACpC,IAAI5F,mBAAmB,KAAK,KAAK,EAAE,OAAA;AACnC,MAAA,IAAIsC,eAAe,EAAE;AACnB;AACA;AACA;AACA,QAAA,OAAA;AACF,OAAA;AAEA,MAAA,MAAMuD,GAAG,GAAGD,OAAO,KAAK,GAAG,GAAGlO,IAAI,GAAG,CAAGA,EAAAA,IAAI,CAAKkO,EAAAA,EAAAA,OAAO,CAAE,CAAA,CAAA;MAE1D,MAAMF,KAAK,GAAG1F,mBAAmB,CAACI,GAAG,GACjC,KAAK,GACL0F,QAAQ,CAAChB,SAAS,EAAE,CAAA,EAAGpN,IAAI,CAAA,IAAA,EAAO2K,OAAO,CAAA,CAAE,EAAE,MAC3CsC,GAAQ,CAAc,CACxB,CAAC,CAAA;MAEL,IAAI,CAACe,KAAK,EAAE;QACVpB,QAAQ,EAAE,CAAC7B,WAAW,CAACjM,GAAG,CAACqP,GAAG,CAAC,CAAA;AACjC,OAAA;AACF,KAAA;GACD,CAAA;EAED,MAAMhH,QAAQ,GAAGwF,OAAO,CAACU,GAAG,EAAEjB,eAAe,EAAEzB,OAAO,CAAC,CAAA;EACvD,MAAM8C,YAAY,GAAGtG,QAAQ,CAACnH,IAAI,IAAI2M,OAAO,CAAC3M,IAAI,CAAA;AAElD,EAAA,IAAI,OAAOmH,QAAQ,CAACmF,UAAU,CAAC,KAAK,UAAU,EAAE;IAC9C,MAAM,IAAIpE,KAAK,CACb,CAAA,KAAA,EAAQuF,YAAY,CAAmC3B,gCAAAA,EAAAA,MAAM,uBAC/D,CAAC,CAAA;AACH,GAAA;EAEA,IAAI9E,KAAK,CAACwF,OAAO,CAACrF,QAAQ,CAACC,SAAS,CAAC,EAAE;IACrC0F,cAAc,GAAG,IAAIzI,GAAG,CACtB8C,QAAQ,CAACC,SAAS,CAACzB,GAAG,CAAC,CAAC3F,IAAI,EAAEoF,KAAK,KAAK,CAACpF,IAAI,EAAEoF,KAAK,CAAC,CACvD,CAAC,CAAA;IACD2H,eAAe,GAAG5F,QAAQ,CAAC4F,eAAe,CAAA;AAC5C,GAAC,MAAM,IAAI5F,QAAQ,CAACC,SAAS,EAAE;IAC7B0F,cAAc,GAAG,IAAIzI,GAAG,CACtBpF,MAAM,CAAC2I,IAAI,CAACT,QAAQ,CAACC,SAAS,CAAC,CAACzB,GAAG,CAAC,CAAC3F,IAAI,EAAEoF,KAAK,KAAK,CAACpF,IAAI,EAAEoF,KAAK,CAAC,CACpE,CAAC,CAAA;IACDyH,gBAAgB,GAAG1F,QAAQ,CAACC,SAAS,CAAA;IACrC2F,eAAe,GAAG5F,QAAQ,CAAC4F,eAAe,CAAA;AAC5C,GAAC,MAAM;AACLD,IAAAA,cAAc,GAAG,IAAIzI,GAAG,EAAE,CAAA;AAC5B,GAAA;EAEA,CAAC;IAAEyD,OAAO;AAAEE,IAAAA,OAAAA;AAAQ,GAAC,GAAGd,sBAAsB,CAC5CuG,YAAY,EACZX,cAAc,EACdV,eAAe,CAACtE,OAAO,IAAI,EAAE,EAC7BsE,eAAe,CAACpE,OAAO,IAAI,EAC7B,CAAC,EAAA;AAED,EAAA,IAAIkB,YAAkE,CAAA;EACtE,IAAIoD,UAAU,KAAK,aAAa,EAAE;AAChCpD,IAAAA,YAAY,GAAGA,CAACmF,OAAO,EAAE/O,IAAI,KAAK;AAAA,MAAA,IAAAgP,IAAA,CAAA;AAChC,MAAA,MAAMC,KAAK,GAAGvB,QAAQ,CAAC1N,IAAI,CAAC,CAAA;AAC5B,MAAA,OAAA,CAAAgP,IAAA,GACGnH,QAAQ,CAACmF,UAAU,CAAC,CAAC+B,OAAO,EAAEE,KAAK,EAAEjP,IAAI,CAAC,KAAAgP,IAAAA,GAAAA,IAAA,GAAuB,KAAK,CAAA;KAE1E,CAAA;AACH,GAAC,MAAM;AACLpF,IAAAA,YAAY,GAAGA,CAACmF,OAAO,EAAE/O,IAAI,KAAK;AAChC,MAAA,MAAMiP,KAAK,GAAGvB,QAAQ,CAAC1N,IAAI,CAAC,CAAA;MAC5B6H,QAAQ,CAACmF,UAAU,CAAC,CAAC+B,OAAO,EAAEE,KAAK,EAAEjP,IAAI,CAAC,CAAA;AAC1C,MAAA,OAAO,KAAK,CAAA;KACb,CAAA;AACH,GAAA;EAEA,OAAO;IACL4M,KAAK;IACLJ,MAAM;IACN9F,OAAO;IACPmB,QAAQ;IACRsG,YAAY;AACZvE,IAAAA,YAAAA;GACD,CAAA;AACH,CAAA;AAEe,SAASsF,sBAAsBA,CAC5C7B,OAAkC,EAClC;EACA,OAAO8B,OAAO,CAAC,CAACpG,QAAQ,EAAED,OAAsB,EAAEuC,OAAe,KAAK;AACpEtC,IAAAA,QAAQ,CAACqG,aAAa,CAAC,0BAA0B,CAAC,CAAA;IAClD,MAAM;AAAEC,MAAAA,QAAAA;AAAS,KAAC,GAAGtG,QAAQ,CAAA;AAE7B,IAAA,IAAIuE,QAAQ,CAAA;AAEZ,IAAA,MAAMtE,mBAAmB,GAAGH,gCAAgC,CAC1DC,OAAO,EACPC,QACF,CAAC,CAAA;IAED,MAAM;MAAE6D,KAAK;MAAEJ,MAAM;MAAE9F,OAAO;MAAEmB,QAAQ;MAAEsG,YAAY;AAAEvE,MAAAA,YAAAA;AAAa,KAAC,GACpEwD,mBAAmB,CACjBC,OAAO,EACPvE,OAAO,EACPE,mBAAmB,EACnBqC,OAAO,EACP,MAAMiC,QAAQ,EACdvE,QACF,CAAC,CAAA;AAEH,IAAA,MAAMuG,aAAa,GAAG9C,MAAM,KAAK,cAAc,GAAGlN,KAAO,GAAGA,KAAO,CAAA;IAEnE,MAAMiQ,OAAO,GAAG1H,QAAQ,CAAC0H,OAAO,GAC5BF,QAAQ,CAACG,QAAQ,CAACC,KAAK,CAAC,CAACH,aAAa,CAAC1F,YAAY,CAAC,EAAE/B,QAAQ,CAAC0H,OAAO,CAAC,CAAC,GACxED,aAAa,CAAC1F,YAAY,CAAC,CAAA;AAE/B,IAAA,IAAIgD,KAAK,IAAIA,KAAK,KAAKpG,0BAA0B,EAAE;AACjDyH,MAAAA,OAAO,CAAC/E,GAAG,CAAC,CAAGiF,EAAAA,YAAY,oBAAoB,CAAC,CAAA;MAChDF,OAAO,CAAC/E,GAAG,CAAC,CAAA,iBAAA,EAAoBzC,yBAAyB,CAACC,OAAO,CAAC,CAAA,CAAE,CAAC,CAAA;AACrEuH,MAAAA,OAAO,CAAC/E,GAAG,CAAC,CAA4BsD,yBAAAA,EAAAA,MAAM,YAAY,CAAC,CAAA;AAC7D,KAAA;IAEA,MAAM;AAAEkD,MAAAA,WAAAA;AAAY,KAAC,GAAG7H,QAAQ,CAAA;IAEhC,OAAO;AACLnH,MAAAA,IAAI,EAAE,kBAAkB;MACxB6O,OAAO;MAEPI,GAAGA,CAACC,IAAI,EAAE;AAAA,QAAA,IAAAC,aAAA,CAAA;AACR,QAAA,IAAIH,WAAW,EAAE;AACf,UAAA,IACEE,IAAI,CAACzP,GAAG,CAAC,0BAA0B,CAAC,IACpCyP,IAAI,CAACzP,GAAG,CAAC,0BAA0B,CAAC,KAAKuP,WAAW,EACpD;AACAzB,YAAAA,OAAO,CAACC,IAAI,CACV,CAAA,gCAAA,CAAkC,GAChC,CAAK0B,EAAAA,EAAAA,IAAI,CAACzP,GAAG,CAAC,8BAA8B,CAAC,CAAA,CAAE,GAC/C,CAAQgO,KAAAA,EAAAA,YAAY,CAA4B,0BAAA,CAAA,GAChD,CAA2C,yCAAA,CAAA,GAC3C,CAAIyB,CAAAA,EAAAA,IAAI,CAACzP,GAAG,CAAC,0BAA0B,CAAC,CAAQuP,KAAAA,EAAAA,WAAW,CAAG,CAAA,CAAA,GAC9D,kCACJ,CAAC,CAAA;AACH,WAAC,MAAM;AACLE,YAAAA,IAAI,CAAC3K,GAAG,CAAC,0BAA0B,EAAEyK,WAAW,CAAC,CAAA;AACjDE,YAAAA,IAAI,CAAC3K,GAAG,CAAC,8BAA8B,EAAEkJ,YAAY,CAAC,CAAA;AACxD,WAAA;AACF,SAAA;AAEAb,QAAAA,QAAQ,GAAG;AACTxF,UAAAA,SAAS,EAAE,IAAI1I,GAAG,EAAE;AACpBmO,UAAAA,gBAAgB,EAAE7H,SAAS;AAC3BgJ,UAAAA,KAAK,EAAE,KAAK;AACZoB,UAAAA,SAAS,EAAE,IAAI1Q,GAAG,EAAE;UACpBqM,WAAW,EAAE,IAAIrM,GAAG,EAAC;SACtB,CAAA;AAED,QAAA,CAAAyQ,aAAA,GAAAhI,QAAQ,CAAC8H,GAAG,KAAA,IAAA,IAAZE,aAAA,CAAcE,KAAK,CAAC,IAAI,EAAE1N,SAAS,CAAC,CAAA;OACrC;AACD2N,MAAAA,IAAIA,GAAG;AAAA,QAAA,IAAAC,cAAA,CAAA;AACL,QAAA,CAAAA,cAAA,GAAApI,QAAQ,CAACmI,IAAI,KAAA,IAAA,IAAbC,cAAA,CAAeF,KAAK,CAAC,IAAI,EAAE1N,SAAS,CAAC,CAAA;QAErC,IAAI2G,mBAAmB,KAAK,KAAK,EAAE;AACjC,UAAA,IAAIA,mBAAmB,CAACE,GAAG,KAAK,UAAU,EAAE;AAC1CyE,YAAAA,UAAe,CAACL,QAAQ,CAAC7B,WAAW,CAAC,CAAA;AACvC,WAAC,MAAM;AACLkC,YAAAA,eAAoB,CAACL,QAAQ,CAAC7B,WAAW,CAAC,CAAA;AAC5C,WAAA;AACF,SAAA;QAEA,IAAI,CAACmB,KAAK,EAAE,OAAA;AAEZ,QAAA,IAAI,IAAI,CAACsD,QAAQ,EAAEjC,OAAO,CAAC/E,GAAG,CAAC,CAAM,GAAA,EAAA,IAAI,CAACgH,QAAQ,GAAG,CAAC,CAAA;AAEtD,QAAA,IAAI5C,QAAQ,CAACxF,SAAS,CAACL,IAAI,KAAK,CAAC,EAAE;UACjCwG,OAAO,CAAC/E,GAAG,CACTsD,MAAM,KAAK,cAAc,GACrBc,QAAQ,CAACoB,KAAK,GACZ,8BAA8BP,YAAY,CAAA,mCAAA,CAAqC,GAC/E,CAA2BA,wBAAAA,EAAAA,YAAY,+BAA+B,GACxE,CAAA,oCAAA,EAAuCA,YAAY,CAAA,mCAAA,CACzD,CAAC,CAAA;AAED,UAAA,OAAA;AACF,SAAA;QAEA,IAAI3B,MAAM,KAAK,cAAc,EAAE;UAC7ByB,OAAO,CAAC/E,GAAG,CACT,CAAA,IAAA,EAAOiF,YAAY,CAAyC,uCAAA,CAAA,GAC1D,0BACJ,CAAC,CAAA;AACH,SAAC,MAAM;AACLF,UAAAA,OAAO,CAAC/E,GAAG,CACT,CAAOiF,IAAAA,EAAAA,YAAY,0CACrB,CAAC,CAAA;AACH,SAAA;AAEA,QAAA,KAAK,MAAMzN,IAAI,IAAI4M,QAAQ,CAACxF,SAAS,EAAE;AAAA,UAAA,IAAAqI,sBAAA,CAAA;UACrC,IAAAA,CAAAA,sBAAA,GAAI7C,QAAQ,CAACC,gBAAgB,aAAzB4C,sBAAA,CAA4BzP,IAAI,CAAC,EAAE;YACrC,MAAM0P,eAAe,GAAGC,mBAAmB,CACzC3P,IAAI,EACJgG,OAAO,EACP4G,QAAQ,CAACC,gBACX,CAAC,CAAA;AAED,YAAA,MAAM+C,gBAAgB,GAAG3J,IAAI,CAACC,SAAS,CAACwJ,eAAe,CAAC,CACrDG,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CACnBA,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CACtBA,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YAEzBtC,OAAO,CAAC/E,GAAG,CAAC,CAAA,EAAA,EAAKxI,IAAI,CAAI4P,CAAAA,EAAAA,gBAAgB,EAAE,CAAC,CAAA;AAC9C,WAAC,MAAM;AACLrC,YAAAA,OAAO,CAAC/E,GAAG,CAAC,CAAKxI,EAAAA,EAAAA,IAAI,EAAE,CAAC,CAAA;AAC1B,WAAA;AACF,SAAA;AACF,OAAA;KACD,CAAA;AACH,GAAC,CAAC,CAAA;AACJ,CAAA;AAEA,SAASoO,QAAQA,CAACzI,GAAG,EAAE3G,GAAG,EAAE8Q,UAAU,EAAE;AACtC,EAAA,IAAIC,GAAG,GAAGpK,GAAG,CAAClG,GAAG,CAACT,GAAG,CAAC,CAAA;EACtB,IAAI+Q,GAAG,KAAK/K,SAAS,EAAE;IACrB+K,GAAG,GAAGD,UAAU,EAAE,CAAA;AAClBnK,IAAAA,GAAG,CAACpB,GAAG,CAACvF,GAAG,EAAE+Q,GAAG,CAAC,CAAA;AACnB,GAAA;AACA,EAAA,OAAOA,GAAG,CAAA;AACZ,CAAA;AAEA,SAAS1D,OAAOA,CAACvL,GAAG,EAAE;EACpB,OAAO7B,MAAM,CAAC2I,IAAI,CAAC9G,GAAG,CAAC,CAACM,MAAM,KAAK,CAAC,CAAA;AACtC;;;;"}