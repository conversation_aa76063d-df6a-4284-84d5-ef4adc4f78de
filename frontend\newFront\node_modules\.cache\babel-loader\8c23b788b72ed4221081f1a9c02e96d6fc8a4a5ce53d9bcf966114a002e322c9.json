{"ast": null, "code": "import request from '@/utils/request';\n\n/**\n * 商户相关API\n */\nexport default {\n  /**\n   * 获取商户类型列表\n   */\n  getShopTypes() {\n    return request({\n      url: '/shop-type/list',\n      method: 'get'\n    });\n  },\n  /**\n   * 根据ID获取商户详情\n   * @param {number} shopId 商户ID\n   */\n  getShopById(shopId) {\n    return request({\n      url: `/shop/${shopId}`,\n      method: 'get'\n    });\n  },\n  /**\n   * 根据商户类型分页查询商户列表\n   * @param {Object} params 查询参数\n   * @param {number} params.typeId 商户类型ID\n   * @param {number} params.current 当前页码\n   * @param {number} params.x 经度（可选）\n   * @param {number} params.y 纬度（可选）\n   */\n  getShopsByType(params) {\n    return request({\n      url: '/shop/of/type',\n      method: 'get',\n      params\n    });\n  },\n  /**\n   * 根据商户名称关键字分页查询商户列表\n   * @param {Object} params 查询参数\n   * @param {string} params.name 商户名称关键字\n   * @param {number} params.current 当前页码\n   */\n  getShopsByName(params) {\n    return request({\n      url: '/shop/of/name',\n      method: 'get',\n      params\n    });\n  },\n  /**\n   * 新增商户\n   * @param {Object} shop 商户数据\n   */\n  createShop(shop) {\n    return request({\n      url: '/shop',\n      method: 'post',\n      data: shop\n    });\n  },\n  /**\n   * 更新商户信息\n   * @param {Object} shop 商户数据\n   */\n  updateShop(shop) {\n    return request({\n      url: '/shop',\n      method: 'put',\n      data: shop\n    });\n  }\n};", "map": {"version": 3, "names": ["request", "getShopTypes", "url", "method", "getShopById", "shopId", "getShopsByType", "params", "getShopsByName", "createShop", "shop", "data", "updateShop"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/src/api/shop.js"], "sourcesContent": ["import request from '@/utils/request'\n\n/**\n * 商户相关API\n */\nexport default {\n  /**\n   * 获取商户类型列表\n   */\n  getShopTypes() {\n    return request({\n      url: '/shop-type/list',\n      method: 'get'\n    })\n  },\n\n  /**\n   * 根据ID获取商户详情\n   * @param {number} shopId 商户ID\n   */\n  getShopById(shopId) {\n    return request({\n      url: `/shop/${shopId}`,\n      method: 'get'\n    })\n  },\n\n  /**\n   * 根据商户类型分页查询商户列表\n   * @param {Object} params 查询参数\n   * @param {number} params.typeId 商户类型ID\n   * @param {number} params.current 当前页码\n   * @param {number} params.x 经度（可选）\n   * @param {number} params.y 纬度（可选）\n   */\n  getShopsByType(params) {\n    return request({\n      url: '/shop/of/type',\n      method: 'get',\n      params\n    })\n  },\n\n  /**\n   * 根据商户名称关键字分页查询商户列表\n   * @param {Object} params 查询参数\n   * @param {string} params.name 商户名称关键字\n   * @param {number} params.current 当前页码\n   */\n  getShopsByName(params) {\n    return request({\n      url: '/shop/of/name',\n      method: 'get',\n      params\n    })\n  },\n\n  /**\n   * 新增商户\n   * @param {Object} shop 商户数据\n   */\n  createShop(shop) {\n    return request({\n      url: '/shop',\n      method: 'post',\n      data: shop\n    })\n  },\n\n  /**\n   * 更新商户信息\n   * @param {Object} shop 商户数据\n   */\n  updateShop(shop) {\n    return request({\n      url: '/shop',\n      method: 'put',\n      data: shop\n    })\n  }\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA;AACA;AACA,eAAe;EACb;AACF;AACA;EACEC,YAAYA,CAAA,EAAG;IACb,OAAOD,OAAO,CAAC;MACbE,GAAG,EAAE,iBAAiB;MACtBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACEC,WAAWA,CAACC,MAAM,EAAE;IAClB,OAAOL,OAAO,CAAC;MACbE,GAAG,EAAE,SAASG,MAAM,EAAE;MACtBF,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,cAAcA,CAACC,MAAM,EAAE;IACrB,OAAOP,OAAO,CAAC;MACbE,GAAG,EAAE,eAAe;MACpBC,MAAM,EAAE,KAAK;MACbI;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEC,cAAcA,CAACD,MAAM,EAAE;IACrB,OAAOP,OAAO,CAAC;MACbE,GAAG,EAAE,eAAe;MACpBC,MAAM,EAAE,KAAK;MACbI;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACEE,UAAUA,CAACC,IAAI,EAAE;IACf,OAAOV,OAAO,CAAC;MACbE,GAAG,EAAE,OAAO;MACZC,MAAM,EAAE,MAAM;MACdQ,IAAI,EAAED;IACR,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACEE,UAAUA,CAACF,IAAI,EAAE;IACf,OAAOV,OAAO,CAAC;MACbE,GAAG,EAAE,OAAO;MACZC,MAAM,EAAE,KAAK;MACbQ,IAAI,EAAED;IACR,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}