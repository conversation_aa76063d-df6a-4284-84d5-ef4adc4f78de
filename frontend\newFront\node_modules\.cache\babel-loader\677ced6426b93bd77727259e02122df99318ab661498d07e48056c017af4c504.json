{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport HeaderBar from '@/components/HeaderBar.vue';\nimport LoadingSpinner from '@/components/LoadingSpinner.vue';\nimport EmptyState from '@/components/EmptyState.vue';\nimport { shopApi } from '@/api';\nexport default {\n  name: 'ShopList',\n  components: {\n    HeaderBar,\n    LoadingSpinner,\n    EmptyState\n  },\n  data() {\n    return {\n      typeId: null,\n      typeName: '商户',\n      shopTypes: [],\n      shops: [],\n      currentPage: 1,\n      loading: false,\n      noMore: false,\n      sortBy: '',\n      // 排序方式\n      isReachBottom: false,\n      userLocation: {\n        x: null,\n        y: null\n      }\n    };\n  },\n  created() {\n    this.initData();\n  },\n  methods: {\n    // 初始化数据\n    async initData() {\n      // 从路由参数获取类型信息\n      this.typeId = this.$route.query.type;\n      this.typeName = this.$route.query.name || '商户';\n      await Promise.all([this.loadShopTypes(), this.loadShops()]);\n    },\n    // 加载商户类型\n    async loadShopTypes() {\n      try {\n        const response = await shopApi.getShopTypes();\n        this.shopTypes = response.data || [];\n      } catch (error) {\n        console.error('加载商户类型失败:', error);\n      }\n    },\n    // 加载商户列表\n    async loadShops(page = 1, append = false) {\n      if (this.loading) return;\n      this.loading = true;\n      try {\n        const params = {\n          typeId: this.typeId,\n          current: page\n        };\n\n        // 如果有位置信息，添加到参数中\n        if (this.userLocation.x && this.userLocation.y) {\n          params.x = this.userLocation.x;\n          params.y = this.userLocation.y;\n        }\n        const response = await shopApi.getShopsByType(params);\n        const newShops = response.data || [];\n        if (append) {\n          this.shops = this.shops.concat(newShops);\n        } else {\n          this.shops = newShops;\n        }\n\n        // 判断是否还有更多数据\n        if (newShops.length === 0) {\n          this.noMore = true;\n        }\n        this.currentPage = page;\n      } catch (error) {\n        console.error('加载商户列表失败:', error);\n        this.$message.error('加载商户列表失败');\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 处理类型切换\n    handleTypeChange(type) {\n      this.typeId = type.id;\n      this.typeName = type.name;\n      this.refreshData();\n\n      // 更新路由参数\n      this.$router.replace({\n        query: {\n          ...this.$route.query,\n          type: type.id,\n          name: type.name\n        }\n      });\n    },\n    // 处理排序\n    handleSort(sortType) {\n      this.sortBy = sortType;\n      this.refreshData();\n      // 这里可以根据sortType调用不同的API或添加排序参数\n    },\n    // 处理搜索\n    handleSearch() {\n      this.$message.info('搜索功能开发中');\n    },\n    // 滚动处理\n    handleScroll(e) {\n      const {\n        scrollTop,\n        offsetHeight,\n        scrollHeight\n      } = e.target;\n\n      // 判断是否滚动到底部\n      if (scrollTop + offsetHeight >= scrollHeight - 10 && !this.isReachBottom && !this.noMore) {\n        this.isReachBottom = true;\n        this.loadMoreShops();\n      } else {\n        this.isReachBottom = false;\n      }\n    },\n    // 加载更多商户\n    async loadMoreShops() {\n      if (this.loading || this.noMore) return;\n      const nextPage = this.currentPage + 1;\n      await this.loadShops(nextPage, true);\n    },\n    // 跳转到商户详情\n    toShopDetail(shopId) {\n      this.$router.push(`/shop-detail/${shopId}`);\n    },\n    // 返回上一页\n    goBack() {\n      console.log('ShopList: 处理返回事件');\n      this.$router.go(-1);\n    },\n    // 格式化距离\n    formatDistance(distance) {\n      if (distance < 1000) {\n        return `${Math.round(distance)}m`;\n      } else {\n        return `${(distance / 1000).toFixed(1)}km`;\n      }\n    },\n    // 刷新数据\n    async refreshData() {\n      this.currentPage = 1;\n      this.noMore = false;\n      this.shops = [];\n      await this.loadShops();\n    },\n    // 获取用户位置\n    getUserLocation() {\n      if (navigator.geolocation) {\n        navigator.geolocation.getCurrentPosition(position => {\n          this.userLocation.x = position.coords.longitude;\n          this.userLocation.y = position.coords.latitude;\n          this.refreshData();\n        }, error => {\n          console.error('获取位置失败:', error);\n        });\n      }\n    }\n  },\n  mounted() {\n    // 尝试获取用户位置\n    this.getUserLocation();\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "LoadingSpinner", "EmptyState", "shopApi", "name", "components", "data", "typeId", "typeName", "shopTypes", "shops", "currentPage", "loading", "noMore", "sortBy", "isReachBottom", "userLocation", "x", "y", "created", "initData", "methods", "$route", "query", "type", "Promise", "all", "loadShopTypes", "loadShops", "response", "getShopTypes", "error", "console", "page", "append", "params", "current", "getShopsByType", "newShops", "concat", "length", "$message", "handleTypeChange", "id", "refreshData", "$router", "replace", "handleSort", "sortType", "handleSearch", "info", "handleScroll", "e", "scrollTop", "offsetHeight", "scrollHeight", "target", "loadMoreShops", "nextPage", "toShopDetail", "shopId", "push", "goBack", "log", "go", "formatDistance", "distance", "Math", "round", "toFixed", "getUserLocation", "navigator", "geolocation", "getCurrentPosition", "position", "coords", "longitude", "latitude", "mounted"], "sources": ["src/views/ShopList.vue"], "sourcesContent": ["<template>\n  <div class=\"shop-list-page\">\n    <!-- 头部 -->\n    <HeaderBar \n      :title=\"typeName\" \n      :show-back=\"true\" \n      :show-search=\"true\"\n      @back=\"goBack\"\n      @search=\"handleSearch\"\n    />\n    \n    <!-- 排序栏 -->\n    <div class=\"sort-bar\">\n      <!-- 类型选择 -->\n      <div class=\"sort-item\">\n        <el-dropdown trigger=\"click\" @command=\"handleTypeChange\">\n          <span class=\"dropdown-link\">\n            {{ typeName }}\n            <i class=\"el-icon-arrow-down\"></i>\n          </span>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item \n              v-for=\"type in shopTypes\" \n              :key=\"type.id\" \n              :command=\"type\"\n            >\n              {{ type.name }}\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n      </div>\n      \n      <!-- 距离排序 -->\n      <div \n        class=\"sort-item\" \n        :class=\"{ active: sortBy === 'distance' }\"\n        @click=\"handleSort('distance')\"\n      >\n        距离\n        <i class=\"el-icon-arrow-down\"></i>\n      </div>\n      \n      <!-- 人气排序 -->\n      <div \n        class=\"sort-item\" \n        :class=\"{ active: sortBy === 'comments' }\"\n        @click=\"handleSort('comments')\"\n      >\n        人气\n        <i class=\"el-icon-arrow-down\"></i>\n      </div>\n      \n      <!-- 评分排序 -->\n      <div \n        class=\"sort-item\" \n        :class=\"{ active: sortBy === 'score' }\"\n        @click=\"handleSort('score')\"\n      >\n        评分\n        <i class=\"el-icon-arrow-down\"></i>\n      </div>\n    </div>\n    \n    <!-- 商户列表 -->\n    <div class=\"shop-list\" @scroll=\"handleScroll\" ref=\"shopList\">\n      <div \n        v-for=\"shop in shops\" \n        :key=\"shop.id\" \n        class=\"shop-item\" \n        @click=\"toShopDetail(shop.id)\"\n      >\n        <div class=\"shop-image\">\n          <img :src=\"shop.images ? shop.images.split(',')[0] : '/imgs/default-shop.png'\" :alt=\"shop.name\">\n        </div>\n        <div class=\"shop-content\">\n          <div class=\"shop-name\">{{ shop.name }}</div>\n          <div class=\"shop-type\">{{ shop.typeName }}</div>\n          <div class=\"shop-address\">{{ shop.address }}</div>\n          <div class=\"shop-footer\">\n            <div class=\"shop-score\">\n              <el-rate \n                v-model=\"shop.score\" \n                disabled \n                show-score \n                text-color=\"#ff9900\"\n                score-template=\"{value}\"\n              />\n            </div>\n            <div class=\"shop-distance\" v-if=\"shop.distance\">\n              {{ formatDistance(shop.distance) }}\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- 加载更多 -->\n      <div v-if=\"loading\" class=\"loading-more\">\n        <LoadingSpinner text=\"加载中...\" />\n      </div>\n      \n      <!-- 没有更多数据 -->\n      <div v-if=\"noMore && shops.length > 0\" class=\"no-more\">\n        没有更多商户了\n      </div>\n      \n      <!-- 空状态 -->\n      <EmptyState \n        v-if=\"!loading && shops.length === 0\" \n        text=\"暂无商户信息\"\n        icon=\"el-icon-shop\"\n        :show-action=\"true\"\n        action-text=\"刷新\"\n        @action=\"refreshData\"\n      />\n    </div>\n  </div>\n</template>\n\n<script>\nimport HeaderBar from '@/components/HeaderBar.vue'\nimport LoadingSpinner from '@/components/LoadingSpinner.vue'\nimport EmptyState from '@/components/EmptyState.vue'\nimport { shopApi } from '@/api'\n\nexport default {\n  name: 'ShopList',\n  components: {\n    HeaderBar,\n    LoadingSpinner,\n    EmptyState\n  },\n  data() {\n    return {\n      typeId: null,\n      typeName: '商户',\n      shopTypes: [],\n      shops: [],\n      currentPage: 1,\n      loading: false,\n      noMore: false,\n      sortBy: '', // 排序方式\n      isReachBottom: false,\n      userLocation: {\n        x: null,\n        y: null\n      }\n    }\n  },\n  created() {\n    this.initData()\n  },\n  methods: {\n    // 初始化数据\n    async initData() {\n      // 从路由参数获取类型信息\n      this.typeId = this.$route.query.type\n      this.typeName = this.$route.query.name || '商户'\n      \n      await Promise.all([\n        this.loadShopTypes(),\n        this.loadShops()\n      ])\n    },\n    \n    // 加载商户类型\n    async loadShopTypes() {\n      try {\n        const response = await shopApi.getShopTypes()\n        this.shopTypes = response.data || []\n      } catch (error) {\n        console.error('加载商户类型失败:', error)\n      }\n    },\n    \n    // 加载商户列表\n    async loadShops(page = 1, append = false) {\n      if (this.loading) return\n      \n      this.loading = true\n      try {\n        const params = {\n          typeId: this.typeId,\n          current: page\n        }\n        \n        // 如果有位置信息，添加到参数中\n        if (this.userLocation.x && this.userLocation.y) {\n          params.x = this.userLocation.x\n          params.y = this.userLocation.y\n        }\n        \n        const response = await shopApi.getShopsByType(params)\n        const newShops = response.data || []\n        \n        if (append) {\n          this.shops = this.shops.concat(newShops)\n        } else {\n          this.shops = newShops\n        }\n        \n        // 判断是否还有更多数据\n        if (newShops.length === 0) {\n          this.noMore = true\n        }\n        \n        this.currentPage = page\n      } catch (error) {\n        console.error('加载商户列表失败:', error)\n        this.$message.error('加载商户列表失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 处理类型切换\n    handleTypeChange(type) {\n      this.typeId = type.id\n      this.typeName = type.name\n      this.refreshData()\n      \n      // 更新路由参数\n      this.$router.replace({\n        query: { \n          ...this.$route.query,\n          type: type.id,\n          name: type.name\n        }\n      })\n    },\n    \n    // 处理排序\n    handleSort(sortType) {\n      this.sortBy = sortType\n      this.refreshData()\n      // 这里可以根据sortType调用不同的API或添加排序参数\n    },\n    \n    // 处理搜索\n    handleSearch() {\n      this.$message.info('搜索功能开发中')\n    },\n    \n    // 滚动处理\n    handleScroll(e) {\n      const { scrollTop, offsetHeight, scrollHeight } = e.target\n      \n      // 判断是否滚动到底部\n      if (scrollTop + offsetHeight >= scrollHeight - 10 && !this.isReachBottom && !this.noMore) {\n        this.isReachBottom = true\n        this.loadMoreShops()\n      } else {\n        this.isReachBottom = false\n      }\n    },\n    \n    // 加载更多商户\n    async loadMoreShops() {\n      if (this.loading || this.noMore) return\n      \n      const nextPage = this.currentPage + 1\n      await this.loadShops(nextPage, true)\n    },\n    \n    // 跳转到商户详情\n    toShopDetail(shopId) {\n      this.$router.push(`/shop-detail/${shopId}`)\n    },\n    \n    // 返回上一页\n    goBack() {\n      console.log('ShopList: 处理返回事件')\n      this.$router.go(-1)\n    },\n    \n    // 格式化距离\n    formatDistance(distance) {\n      if (distance < 1000) {\n        return `${Math.round(distance)}m`\n      } else {\n        return `${(distance / 1000).toFixed(1)}km`\n      }\n    },\n    \n    // 刷新数据\n    async refreshData() {\n      this.currentPage = 1\n      this.noMore = false\n      this.shops = []\n      await this.loadShops()\n    },\n    \n    // 获取用户位置\n    getUserLocation() {\n      if (navigator.geolocation) {\n        navigator.geolocation.getCurrentPosition(\n          (position) => {\n            this.userLocation.x = position.coords.longitude\n            this.userLocation.y = position.coords.latitude\n            this.refreshData()\n          },\n          (error) => {\n            console.error('获取位置失败:', error)\n          }\n        )\n      }\n    }\n  },\n  \n  mounted() {\n    // 尝试获取用户位置\n    this.getUserLocation()\n  }\n}\n</script>\n\n<style scoped>\n.shop-list-page {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: #f5f5f5;\n}\n\n/* 排序栏 */\n.sort-bar {\n  display: flex;\n  align-items: center;\n  background-color: #fff;\n  border-bottom: 1px solid #e8e8e8;\n  padding: 0 15px;\n  height: 45px;\n}\n\n.sort-item {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  font-size: 14px;\n  color: #333;\n  transition: color 0.3s ease;\n}\n\n.sort-item:hover {\n  color: #ff6633;\n}\n\n.sort-item.active {\n  color: #ff6633;\n}\n\n.sort-item i {\n  margin-left: 4px;\n  font-size: 12px;\n}\n\n.dropdown-link {\n  display: flex;\n  align-items: center;\n  color: inherit;\n}\n\n/* 商户列表 */\n.shop-list {\n  flex: 1;\n  overflow-y: auto;\n  padding-top: 10px;\n}\n\n.shop-item {\n  display: flex;\n  padding: 15px;\n  background-color: #fff;\n  margin-bottom: 10px;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n}\n\n.shop-item:hover {\n  background-color: #fafafa;\n}\n\n.shop-image {\n  width: 80px;\n  height: 80px;\n  margin-right: 12px;\n  border-radius: 8px;\n  overflow: hidden;\n  flex-shrink: 0;\n}\n\n.shop-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.shop-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.shop-name {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n  overflow: hidden;\n}\n\n.shop-type {\n  font-size: 12px;\n  color: #ff6633;\n  background-color: #fff2f0;\n  padding: 2px 6px;\n  border-radius: 4px;\n  display: inline-block;\n  margin-bottom: 4px;\n  width: fit-content;\n}\n\n.shop-address {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 8px;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n  overflow: hidden;\n}\n\n.shop-footer {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.shop-score {\n  display: flex;\n  align-items: center;\n}\n\n.shop-score .el-rate {\n  margin-right: 8px;\n}\n\n.shop-distance {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 加载状态 */\n.loading-more {\n  padding: 20px;\n  text-align: center;\n}\n\n.no-more {\n  padding: 20px;\n  text-align: center;\n  font-size: 12px;\n  color: #999;\n}\n\n/* Element UI 样式覆盖 */\n.el-dropdown-link {\n  cursor: pointer;\n  color: inherit;\n}\n\n.el-rate__item {\n  margin-right: 2px;\n}\n\n.el-rate__text {\n  font-size: 12px;\n  color: #666;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .sort-bar {\n    height: 40px;\n    padding: 0 10px;\n  }\n\n  .sort-item {\n    font-size: 12px;\n  }\n\n  .shop-item {\n    padding: 12px;\n  }\n\n  .shop-image {\n    width: 70px;\n    height: 70px;\n  }\n\n  .shop-name {\n    font-size: 14px;\n  }\n\n  .shop-type {\n    font-size: 10px;\n  }\n\n  .shop-address {\n    font-size: 11px;\n  }\n}\n</style>\n"], "mappings": ";AAuHA,OAAAA,SAAA;AACA,OAAAC,cAAA;AACA,OAAAC,UAAA;AACA,SAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL,SAAA;IACAC,cAAA;IACAC;EACA;EACAI,KAAA;IACA;MACAC,MAAA;MACAC,QAAA;MACAC,SAAA;MACAC,KAAA;MACAC,WAAA;MACAC,OAAA;MACAC,MAAA;MACAC,MAAA;MAAA;MACAC,aAAA;MACAC,YAAA;QACAC,CAAA;QACAC,CAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACA;IACA,MAAAD,SAAA;MACA;MACA,KAAAb,MAAA,QAAAe,MAAA,CAAAC,KAAA,CAAAC,IAAA;MACA,KAAAhB,QAAA,QAAAc,MAAA,CAAAC,KAAA,CAAAnB,IAAA;MAEA,MAAAqB,OAAA,CAAAC,GAAA,EACA,KAAAC,aAAA,IACA,KAAAC,SAAA,GACA;IACA;IAEA;IACA,MAAAD,cAAA;MACA;QACA,MAAAE,QAAA,SAAA1B,OAAA,CAAA2B,YAAA;QACA,KAAArB,SAAA,GAAAoB,QAAA,CAAAvB,IAAA;MACA,SAAAyB,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IAEA;IACA,MAAAH,UAAAK,IAAA,MAAAC,MAAA;MACA,SAAAtB,OAAA;MAEA,KAAAA,OAAA;MACA;QACA,MAAAuB,MAAA;UACA5B,MAAA,OAAAA,MAAA;UACA6B,OAAA,EAAAH;QACA;;QAEA;QACA,SAAAjB,YAAA,CAAAC,CAAA,SAAAD,YAAA,CAAAE,CAAA;UACAiB,MAAA,CAAAlB,CAAA,QAAAD,YAAA,CAAAC,CAAA;UACAkB,MAAA,CAAAjB,CAAA,QAAAF,YAAA,CAAAE,CAAA;QACA;QAEA,MAAAW,QAAA,SAAA1B,OAAA,CAAAkC,cAAA,CAAAF,MAAA;QACA,MAAAG,QAAA,GAAAT,QAAA,CAAAvB,IAAA;QAEA,IAAA4B,MAAA;UACA,KAAAxB,KAAA,QAAAA,KAAA,CAAA6B,MAAA,CAAAD,QAAA;QACA;UACA,KAAA5B,KAAA,GAAA4B,QAAA;QACA;;QAEA;QACA,IAAAA,QAAA,CAAAE,MAAA;UACA,KAAA3B,MAAA;QACA;QAEA,KAAAF,WAAA,GAAAsB,IAAA;MACA,SAAAF,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA,KAAAU,QAAA,CAAAV,KAAA;MACA;QACA,KAAAnB,OAAA;MACA;IACA;IAEA;IACA8B,iBAAAlB,IAAA;MACA,KAAAjB,MAAA,GAAAiB,IAAA,CAAAmB,EAAA;MACA,KAAAnC,QAAA,GAAAgB,IAAA,CAAApB,IAAA;MACA,KAAAwC,WAAA;;MAEA;MACA,KAAAC,OAAA,CAAAC,OAAA;QACAvB,KAAA;UACA,QAAAD,MAAA,CAAAC,KAAA;UACAC,IAAA,EAAAA,IAAA,CAAAmB,EAAA;UACAvC,IAAA,EAAAoB,IAAA,CAAApB;QACA;MACA;IACA;IAEA;IACA2C,WAAAC,QAAA;MACA,KAAAlC,MAAA,GAAAkC,QAAA;MACA,KAAAJ,WAAA;MACA;IACA;IAEA;IACAK,aAAA;MACA,KAAAR,QAAA,CAAAS,IAAA;IACA;IAEA;IACAC,aAAAC,CAAA;MACA;QAAAC,SAAA;QAAAC,YAAA;QAAAC;MAAA,IAAAH,CAAA,CAAAI,MAAA;;MAEA;MACA,IAAAH,SAAA,GAAAC,YAAA,IAAAC,YAAA,eAAAxC,aAAA,UAAAF,MAAA;QACA,KAAAE,aAAA;QACA,KAAA0C,aAAA;MACA;QACA,KAAA1C,aAAA;MACA;IACA;IAEA;IACA,MAAA0C,cAAA;MACA,SAAA7C,OAAA,SAAAC,MAAA;MAEA,MAAA6C,QAAA,QAAA/C,WAAA;MACA,WAAAiB,SAAA,CAAA8B,QAAA;IACA;IAEA;IACAC,aAAAC,MAAA;MACA,KAAAf,OAAA,CAAAgB,IAAA,iBAAAD,MAAA;IACA;IAEA;IACAE,OAAA;MACA9B,OAAA,CAAA+B,GAAA;MACA,KAAAlB,OAAA,CAAAmB,EAAA;IACA;IAEA;IACAC,eAAAC,QAAA;MACA,IAAAA,QAAA;QACA,UAAAC,IAAA,CAAAC,KAAA,CAAAF,QAAA;MACA;QACA,WAAAA,QAAA,SAAAG,OAAA;MACA;IACA;IAEA;IACA,MAAAzB,YAAA;MACA,KAAAjC,WAAA;MACA,KAAAE,MAAA;MACA,KAAAH,KAAA;MACA,WAAAkB,SAAA;IACA;IAEA;IACA0C,gBAAA;MACA,IAAAC,SAAA,CAAAC,WAAA;QACAD,SAAA,CAAAC,WAAA,CAAAC,kBAAA,CACAC,QAAA;UACA,KAAA1D,YAAA,CAAAC,CAAA,GAAAyD,QAAA,CAAAC,MAAA,CAAAC,SAAA;UACA,KAAA5D,YAAA,CAAAE,CAAA,GAAAwD,QAAA,CAAAC,MAAA,CAAAE,QAAA;UACA,KAAAjC,WAAA;QACA,GACAb,KAAA;UACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;QACA,CACA;MACA;IACA;EACA;EAEA+C,QAAA;IACA;IACA,KAAAR,eAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}