{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport HeaderBar from '@/components/HeaderBar.vue';\nimport LoadingSpinner from '@/components/LoadingSpinner.vue';\nimport EmptyState from '@/components/EmptyState.vue';\nimport { shopApi } from '@/api';\nexport default {\n  name: 'ShopDetail',\n  components: {\n    HeaderBar,\n    LoadingSpinner,\n    EmptyState\n  },\n  data() {\n    return {\n      shop: {},\n      loading: false\n    };\n  },\n  computed: {\n    shopImages() {\n      if (this.shop.images) {\n        return this.shop.images.split(',').filter(img => img.trim());\n      }\n      return ['/imgs/default-shop.png'];\n    }\n  },\n  created() {\n    this.loadShopDetail();\n  },\n  methods: {\n    // 加载商户详情\n    async loadShopDetail() {\n      const shopId = this.$route.params.id;\n      if (!shopId) {\n        this.$message.error('商户ID不存在');\n        this.goBack();\n        return;\n      }\n      this.loading = true;\n      try {\n        const response = await shopApi.getShopById(shopId);\n        this.shop = response.data || {};\n      } catch (error) {\n        console.error('加载商户详情失败:', error);\n        this.$message.error('加载商户详情失败');\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 返回上一页\n    goBack() {\n      this.$router.go(-1);\n    },\n    // 拨打电话\n    callShop() {\n      if (this.shop.phone) {\n        window.location.href = `tel:${this.shop.phone}`;\n      }\n    },\n    // 导航到商户\n    navigateToShop() {\n      if (this.shop.x && this.shop.y) {\n        // 这里可以调用地图API进行导航\n        this.$message.info('导航功能开发中');\n      } else {\n        this.$message.warning('商户位置信息不完整');\n      }\n    },\n    // 分享商户\n    shareShop() {\n      if (navigator.share) {\n        navigator.share({\n          title: this.shop.name,\n          text: this.shop.intro || '推荐一个不错的商户',\n          url: window.location.href\n        });\n      } else {\n        // 复制链接到剪贴板\n        navigator.clipboard.writeText(window.location.href).then(() => {\n          this.$message.success('链接已复制到剪贴板');\n        }).catch(() => {\n          this.$message.error('分享失败');\n        });\n      }\n    },\n    // 收藏商户\n    collectShop() {\n      this.$message.info('收藏功能开发中');\n    }\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "LoadingSpinner", "EmptyState", "shopApi", "name", "components", "data", "shop", "loading", "computed", "shopImages", "images", "split", "filter", "img", "trim", "created", "loadShopDetail", "methods", "shopId", "$route", "params", "id", "$message", "error", "goBack", "response", "getShopById", "console", "$router", "go", "callShop", "phone", "window", "location", "href", "navigateToShop", "x", "y", "info", "warning", "shareShop", "navigator", "share", "title", "text", "intro", "url", "clipboard", "writeText", "then", "success", "catch", "collectShop"], "sources": ["src/views/ShopDetail.vue"], "sourcesContent": ["<template>\n  <div class=\"shop-detail-page\">\n    <!-- 头部 -->\n    <HeaderBar \n      :title=\"shop.name || '商户详情'\" \n      :show-back=\"true\" \n      @back=\"goBack\"\n    />\n    \n    <!-- 商户信息 -->\n    <div v-if=\"shop.id\" class=\"shop-detail\">\n      <!-- 商户图片 -->\n      <div class=\"shop-images\">\n        <el-carousel height=\"200px\" indicator-position=\"outside\">\n          <el-carousel-item v-for=\"(image, index) in shopImages\" :key=\"index\">\n            <img :src=\"image\" :alt=\"shop.name\" class=\"shop-image\">\n          </el-carousel-item>\n        </el-carousel>\n      </div>\n      \n      <!-- 商户基本信息 -->\n      <div class=\"shop-info\">\n        <div class=\"shop-header\">\n          <h2 class=\"shop-name\">{{ shop.name }}</h2>\n          <div class=\"shop-type\">{{ shop.typeName }}</div>\n        </div>\n        \n        <div class=\"shop-rating\">\n          <el-rate \n            v-model=\"shop.score\" \n            disabled \n            show-score \n            text-color=\"#ff9900\"\n            score-template=\"{value}分\"\n          />\n          <span class=\"comment-count\">({{ shop.comments }}条评价)</span>\n        </div>\n        \n        <div class=\"shop-address\">\n          <i class=\"el-icon-location-outline\"></i>\n          <span>{{ shop.address }}</span>\n        </div>\n        \n        <div class=\"shop-phone\" v-if=\"shop.phone\">\n          <i class=\"el-icon-phone-outline\"></i>\n          <span>{{ shop.phone }}</span>\n          <el-button type=\"text\" @click=\"callShop\">拨打电话</el-button>\n        </div>\n        \n        <div class=\"shop-hours\" v-if=\"shop.openHours\">\n          <i class=\"el-icon-time\"></i>\n          <span>营业时间：{{ shop.openHours }}</span>\n        </div>\n      </div>\n      \n      <!-- 商户介绍 -->\n      <div v-if=\"shop.intro\" class=\"shop-intro\">\n        <h3>商户介绍</h3>\n        <p>{{ shop.intro }}</p>\n      </div>\n      \n      <!-- 操作按钮 -->\n      <div class=\"shop-actions\">\n        <el-button type=\"primary\" @click=\"navigateToShop\">\n          <i class=\"el-icon-location\"></i>\n          导航\n        </el-button>\n        <el-button @click=\"shareShop\">\n          <i class=\"el-icon-share\"></i>\n          分享\n        </el-button>\n        <el-button @click=\"collectShop\">\n          <i class=\"el-icon-star-off\"></i>\n          收藏\n        </el-button>\n      </div>\n    </div>\n    \n    <!-- 加载状态 -->\n    <LoadingSpinner v-if=\"loading\" :full-screen=\"true\" text=\"加载中...\" />\n    \n    <!-- 错误状态 -->\n    <EmptyState \n      v-if=\"!loading && !shop.id\" \n      text=\"商户信息不存在\"\n      icon=\"el-icon-warning\"\n      :show-action=\"true\"\n      action-text=\"返回\"\n      @action=\"goBack\"\n    />\n  </div>\n</template>\n\n<script>\nimport HeaderBar from '@/components/HeaderBar.vue'\nimport LoadingSpinner from '@/components/LoadingSpinner.vue'\nimport EmptyState from '@/components/EmptyState.vue'\nimport { shopApi } from '@/api'\n\nexport default {\n  name: 'ShopDetail',\n  components: {\n    HeaderBar,\n    LoadingSpinner,\n    EmptyState\n  },\n  data() {\n    return {\n      shop: {},\n      loading: false\n    }\n  },\n  computed: {\n    shopImages() {\n      if (this.shop.images) {\n        return this.shop.images.split(',').filter(img => img.trim())\n      }\n      return ['/imgs/default-shop.png']\n    }\n  },\n  created() {\n    this.loadShopDetail()\n  },\n  methods: {\n    // 加载商户详情\n    async loadShopDetail() {\n      const shopId = this.$route.params.id\n      if (!shopId) {\n        this.$message.error('商户ID不存在')\n        this.goBack()\n        return\n      }\n      \n      this.loading = true\n      try {\n        const response = await shopApi.getShopById(shopId)\n        this.shop = response.data || {}\n      } catch (error) {\n        console.error('加载商户详情失败:', error)\n        this.$message.error('加载商户详情失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 返回上一页\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    // 拨打电话\n    callShop() {\n      if (this.shop.phone) {\n        window.location.href = `tel:${this.shop.phone}`\n      }\n    },\n    \n    // 导航到商户\n    navigateToShop() {\n      if (this.shop.x && this.shop.y) {\n        // 这里可以调用地图API进行导航\n        this.$message.info('导航功能开发中')\n      } else {\n        this.$message.warning('商户位置信息不完整')\n      }\n    },\n    \n    // 分享商户\n    shareShop() {\n      if (navigator.share) {\n        navigator.share({\n          title: this.shop.name,\n          text: this.shop.intro || '推荐一个不错的商户',\n          url: window.location.href\n        })\n      } else {\n        // 复制链接到剪贴板\n        navigator.clipboard.writeText(window.location.href).then(() => {\n          this.$message.success('链接已复制到剪贴板')\n        }).catch(() => {\n          this.$message.error('分享失败')\n        })\n      }\n    },\n    \n    // 收藏商户\n    collectShop() {\n      this.$message.info('收藏功能开发中')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.shop-detail-page {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.shop-detail {\n  padding-bottom: 20px;\n}\n\n/* 商户图片 */\n.shop-images {\n  background-color: #fff;\n}\n\n.shop-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* 商户信息 */\n.shop-info {\n  background-color: #fff;\n  padding: 20px;\n  margin-bottom: 10px;\n}\n\n.shop-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 15px;\n}\n\n.shop-name {\n  font-size: 20px;\n  font-weight: 600;\n  color: #333;\n  margin: 0;\n}\n\n.shop-type {\n  font-size: 12px;\n  color: #ff6633;\n  background-color: #fff2f0;\n  padding: 4px 8px;\n  border-radius: 4px;\n}\n\n.shop-rating {\n  display: flex;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.comment-count {\n  margin-left: 10px;\n  font-size: 12px;\n  color: #666;\n}\n\n.shop-address,\n.shop-phone,\n.shop-hours {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n  font-size: 14px;\n  color: #666;\n}\n\n.shop-address i,\n.shop-phone i,\n.shop-hours i {\n  margin-right: 8px;\n  color: #999;\n}\n\n.shop-phone .el-button {\n  margin-left: auto;\n  padding: 0;\n  color: #ff6633;\n}\n\n/* 商户介绍 */\n.shop-intro {\n  background-color: #fff;\n  padding: 20px;\n  margin-bottom: 10px;\n}\n\n.shop-intro h3 {\n  font-size: 16px;\n  color: #333;\n  margin: 0 0 10px 0;\n}\n\n.shop-intro p {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.6;\n  margin: 0;\n}\n\n/* 操作按钮 */\n.shop-actions {\n  background-color: #fff;\n  padding: 20px;\n  display: flex;\n  gap: 15px;\n}\n\n.shop-actions .el-button {\n  flex: 1;\n  height: 40px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .shop-info {\n    padding: 15px;\n  }\n  \n  .shop-name {\n    font-size: 18px;\n  }\n  \n  .shop-intro {\n    padding: 15px;\n  }\n  \n  .shop-actions {\n    padding: 15px;\n    gap: 10px;\n  }\n}\n</style>\n"], "mappings": ";;AA8FA,OAAAA,SAAA;AACA,OAAAC,cAAA;AACA,OAAAC,UAAA;AACA,SAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL,SAAA;IACAC,cAAA;IACAC;EACA;EACAI,KAAA;IACA;MACAC,IAAA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACAC,WAAA;MACA,SAAAH,IAAA,CAAAI,MAAA;QACA,YAAAJ,IAAA,CAAAI,MAAA,CAAAC,KAAA,MAAAC,MAAA,CAAAC,GAAA,IAAAA,GAAA,CAAAC,IAAA;MACA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA;IACA,MAAAD,eAAA;MACA,MAAAE,MAAA,QAAAC,MAAA,CAAAC,MAAA,CAAAC,EAAA;MACA,KAAAH,MAAA;QACA,KAAAI,QAAA,CAAAC,KAAA;QACA,KAAAC,MAAA;QACA;MACA;MAEA,KAAAjB,OAAA;MACA;QACA,MAAAkB,QAAA,SAAAvB,OAAA,CAAAwB,WAAA,CAAAR,MAAA;QACA,KAAAZ,IAAA,GAAAmB,QAAA,CAAApB,IAAA;MACA,SAAAkB,KAAA;QACAI,OAAA,CAAAJ,KAAA,cAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;QACA,KAAAhB,OAAA;MACA;IACA;IAEA;IACAiB,OAAA;MACA,KAAAI,OAAA,CAAAC,EAAA;IACA;IAEA;IACAC,SAAA;MACA,SAAAxB,IAAA,CAAAyB,KAAA;QACAC,MAAA,CAAAC,QAAA,CAAAC,IAAA,eAAA5B,IAAA,CAAAyB,KAAA;MACA;IACA;IAEA;IACAI,eAAA;MACA,SAAA7B,IAAA,CAAA8B,CAAA,SAAA9B,IAAA,CAAA+B,CAAA;QACA;QACA,KAAAf,QAAA,CAAAgB,IAAA;MACA;QACA,KAAAhB,QAAA,CAAAiB,OAAA;MACA;IACA;IAEA;IACAC,UAAA;MACA,IAAAC,SAAA,CAAAC,KAAA;QACAD,SAAA,CAAAC,KAAA;UACAC,KAAA,OAAArC,IAAA,CAAAH,IAAA;UACAyC,IAAA,OAAAtC,IAAA,CAAAuC,KAAA;UACAC,GAAA,EAAAd,MAAA,CAAAC,QAAA,CAAAC;QACA;MACA;QACA;QACAO,SAAA,CAAAM,SAAA,CAAAC,SAAA,CAAAhB,MAAA,CAAAC,QAAA,CAAAC,IAAA,EAAAe,IAAA;UACA,KAAA3B,QAAA,CAAA4B,OAAA;QACA,GAAAC,KAAA;UACA,KAAA7B,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IAEA;IACA6B,YAAA;MACA,KAAA9B,QAAA,CAAAgB,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}