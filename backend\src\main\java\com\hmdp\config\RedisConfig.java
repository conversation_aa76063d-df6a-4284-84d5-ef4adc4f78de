package com.hmdp.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;

@Configuration
public class RedisConfig {

    /**
     * 定义一个 StringRedisTemplate 类型的 Bean，并将其命名为 redisTemplate。
     * Spring Boot 会自动为我们配置好 RedisConnectionFactory，我们只需要注入它即可。
     *
     * @param connectionFactory 自动注入的 Redis 连接工厂
     * @return StringRedisTemplate 的实例
     */
    @Bean
    public StringRedisTemplate redisTemplate(RedisConnectionFactory connectionFactory) {
        // 直接创建并返回 StringRedisTemplate，它的 key 和 value 序列化器默认就是 StringRedisSerializer
        return new StringRedisTemplate(connectionFactory);
    }
}
