package com.hmdp.utils;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import static com.hmdp.utils.RedisConstants.*;
@Slf4j
@Component
public class CacheClient {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public void set(String key, Object value, Long time, TimeUnit unit) {
        stringRedisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(value), time, unit);
    }

    public void setWithLogicExpire(String key, Object value, Long time, TimeUnit unit) {
        // 设置逻辑过期
        RedisData redisData = new RedisData();
        redisData.setData(value);
        redisData.setExpireTime(LocalDateTime.now().plusSeconds(unit.toSeconds(time)));

        // 写入 redis
        stringRedisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(redisData));
    }

    /**
     * 防止缓存穿透代码，
     * @param keyPrefix
     * @param id
     * @param type
     * @param dbFallback
     * @param time
     * @param unit
     * @return
     * @param <R>
     * @param <ID>
     */
    public <R, ID> R queryWithPassThrough(String keyPrefix, ID id, Class<R> type, Function<ID, R> dbFallback, Long time, TimeUnit unit) {

        String key = keyPrefix + id;

        // 1. 先从 redis 中查商铺 id
        String Json = stringRedisTemplate.opsForValue().get(key);

        if (StrUtil.isNotBlank(Json)) {
            // 2. 查到了，直接返回
            R r = JSONUtil.toBean(Json, type);
            return r;
        }

        // 判断命中是否是空值
        if (Json != null) {
            return null;
        }

        // 3. 没查到，从数据库中查
        R r = dbFallback.apply(id);
        // 4. 数据库中不存在，直接返回错误
        if (r == null) {
            // 缓存空对象，防止缓存穿透
            stringRedisTemplate.opsForValue().set(key, "", time, unit);

            return null;
        }
        // 5. 从数据库获得结果后，插入到 redis 中, 设置超时时间
        this.set(key, r, time, unit);

        return r;
    }

    private static final ExecutorService CACHE_REBUILD_EXECUTOR = Executors.newFixedThreadPool(10);

    /**
     * 避免缓存击穿代码，逻辑过期实现
     *
     * @return
     */
    public <R, ID> R queryWithLogicExpire(String keyPrefix, ID id, Class<R> type, Function<ID, R> dbFallback, Long time, TimeUnit unit) {
        String key = keyPrefix + id;

        // 1. 先从 redis 中获取 shop
        String json = stringRedisTemplate.opsForValue().get(key);

        // 2. 判断是否命中
        if (StrUtil.isBlank(json)) {
            // 3. 未名中，返回空
            return null;
        }
        // 4. 如果 redis 命中,先将 对象反序列化
        RedisData redisData = JSONUtil.toBean(json, RedisData.class);
        R r = JSONUtil.toBean((JSONObject) redisData.getData(), type);
        LocalDateTime expireTime = redisData.getExpireTime();

        // 5. 判断是否过期
        if (expireTime.isAfter(LocalDateTime.now())) {
            // 未过期，返回信息
            return r;
        }

        // 4.1. 过期了，需要缓存重建，尝试获取锁
        String lockKey = LOCK_SHOP_KEY + id;
        Boolean isLock = tryLock(lockKey);
        // 4.2. 判断是否获取锁
        if (isLock) {
            // 5. 获取锁了
            // 5.1 开启 独立线程
            CACHE_REBUILD_EXECUTOR.submit(() -> {
                try {

                    // 先查数据库
                    R r1 = dbFallback.apply(id);

                    // 存入 redis
                    this.setWithLogicExpire(key, r1, time, unit);

                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    // 5.3 释放互斥锁
                    unLock(lockKey);
                }
            });
        }

        // 未获取锁，返回过期的信息
        return r;
    }

    /**
     * 互斥锁，解决缓存击穿
     *
     * @param key
     * @return
     */
    private Boolean tryLock(String key) {
        Boolean flag = stringRedisTemplate.opsForValue().setIfAbsent(key, "1", 10, TimeUnit.MINUTES);

        return BooleanUtil.isTrue(flag);
    }

    private void unLock(String key) {
        stringRedisTemplate.delete(key);
    }

}
