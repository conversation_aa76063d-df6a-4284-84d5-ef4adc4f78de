{"ast": null, "code": "import request from '@/utils/request';\n\n/**\n * 用户相关API\n */\nexport default {\n  /**\n   * 发送手机验证码\n   * @param {string} phone 手机号\n   */\n  sendCode(phone) {\n    return request({\n      url: '/user/code',\n      method: 'post',\n      params: {\n        phone\n      }\n    });\n  },\n  /**\n   * 用户登录\n   * @param {Object} loginForm 登录表单数据\n   * @param {string} loginForm.phone 手机号\n   * @param {string} loginForm.code 验证码（验证码登录）\n   * @param {string} loginForm.password 密码（密码登录）\n   */\n  login(loginForm) {\n    return request({\n      url: '/user/login',\n      method: 'post',\n      data: loginForm\n    });\n  },\n  /**\n   * 用户登出\n   */\n  logout() {\n    return request({\n      url: '/user/logout',\n      method: 'post'\n    });\n  },\n  /**\n   * 获取当前用户信息\n   */\n  getCurrentUser() {\n    return request({\n      url: '/user/me',\n      method: 'get'\n    });\n  },\n  /**\n   * 根据用户ID获取用户信息\n   * @param {number} userId 用户ID\n   */\n  getUserById(userId) {\n    return request({\n      url: `/user/${userId}`,\n      method: 'get'\n    });\n  },\n  /**\n   * 获取用户详细信息\n   * @param {number} userId 用户ID\n   */\n  getUserInfo(userId) {\n    return request({\n      url: `/user/info/${userId}`,\n      method: 'get'\n    });\n  },\n  /**\n   * 用户签到\n   */\n  sign() {\n    return request({\n      url: '/user/sign',\n      method: 'post'\n    });\n  },\n  /**\n   * 获取签到次数\n   */\n  getSignCount() {\n    return request({\n      url: '/user/sign/count',\n      method: 'get'\n    });\n  }\n};", "map": {"version": 3, "names": ["request", "sendCode", "phone", "url", "method", "params", "login", "loginForm", "data", "logout", "getCurrentUser", "getUserById", "userId", "getUserInfo", "sign", "getSignCount"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/src/api/user.js"], "sourcesContent": ["import request from '@/utils/request'\n\n/**\n * 用户相关API\n */\nexport default {\n  /**\n   * 发送手机验证码\n   * @param {string} phone 手机号\n   */\n  sendCode(phone) {\n    return request({\n      url: '/user/code',\n      method: 'post',\n      params: { phone }\n    })\n  },\n\n  /**\n   * 用户登录\n   * @param {Object} loginForm 登录表单数据\n   * @param {string} loginForm.phone 手机号\n   * @param {string} loginForm.code 验证码（验证码登录）\n   * @param {string} loginForm.password 密码（密码登录）\n   */\n  login(loginForm) {\n    return request({\n      url: '/user/login',\n      method: 'post',\n      data: loginForm\n    })\n  },\n\n  /**\n   * 用户登出\n   */\n  logout() {\n    return request({\n      url: '/user/logout',\n      method: 'post'\n    })\n  },\n\n  /**\n   * 获取当前用户信息\n   */\n  getCurrentUser() {\n    return request({\n      url: '/user/me',\n      method: 'get'\n    })\n  },\n\n  /**\n   * 根据用户ID获取用户信息\n   * @param {number} userId 用户ID\n   */\n  getUserById(userId) {\n    return request({\n      url: `/user/${userId}`,\n      method: 'get'\n    })\n  },\n\n  /**\n   * 获取用户详细信息\n   * @param {number} userId 用户ID\n   */\n  getUserInfo(userId) {\n    return request({\n      url: `/user/info/${userId}`,\n      method: 'get'\n    })\n  },\n\n  /**\n   * 用户签到\n   */\n  sign() {\n    return request({\n      url: '/user/sign',\n      method: 'post'\n    })\n  },\n\n  /**\n   * 获取签到次数\n   */\n  getSignCount() {\n    return request({\n      url: '/user/sign/count',\n      method: 'get'\n    })\n  }\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA;AACA;AACA,eAAe;EACb;AACF;AACA;AACA;EACEC,QAAQA,CAACC,KAAK,EAAE;IACd,OAAOF,OAAO,CAAC;MACbG,GAAG,EAAE,YAAY;MACjBC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;QAAEH;MAAM;IAClB,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEI,KAAKA,CAACC,SAAS,EAAE;IACf,OAAOP,OAAO,CAAC;MACbG,GAAG,EAAE,aAAa;MAClBC,MAAM,EAAE,MAAM;MACdI,IAAI,EAAED;IACR,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;EACEE,MAAMA,CAAA,EAAG;IACP,OAAOT,OAAO,CAAC;MACbG,GAAG,EAAE,cAAc;MACnBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;EACEM,cAAcA,CAAA,EAAG;IACf,OAAOV,OAAO,CAAC;MACbG,GAAG,EAAE,UAAU;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACEO,WAAWA,CAACC,MAAM,EAAE;IAClB,OAAOZ,OAAO,CAAC;MACbG,GAAG,EAAE,SAASS,MAAM,EAAE;MACtBR,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACES,WAAWA,CAACD,MAAM,EAAE;IAClB,OAAOZ,OAAO,CAAC;MACbG,GAAG,EAAE,cAAcS,MAAM,EAAE;MAC3BR,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;EACEU,IAAIA,CAAA,EAAG;IACL,OAAOd,OAAO,CAAC;MACbG,GAAG,EAAE,YAAY;MACjBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;EACEW,YAAYA,CAAA,EAAG;IACb,OAAOf,OAAO,CAAC;MACbG,GAAG,EAAE,kBAAkB;MACvBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}