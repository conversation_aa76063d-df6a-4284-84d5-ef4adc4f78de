{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"shop-detail-page\"\n  }, [_c(\"HeaderBar\", {\n    attrs: {\n      title: _vm.shop.name || \"商户详情\",\n      \"show-back\": true\n    },\n    on: {\n      back: _vm.goBack\n    }\n  }), _vm.shop.id ? _c(\"div\", {\n    staticClass: \"shop-detail\"\n  }, [_c(\"div\", {\n    staticClass: \"shop-images\"\n  }, [_c(\"el-carousel\", {\n    attrs: {\n      height: \"200px\",\n      \"indicator-position\": \"outside\"\n    }\n  }, _vm._l(_vm.shopImages, function (image, index) {\n    return _c(\"el-carousel-item\", {\n      key: index\n    }, [_c(\"img\", {\n      staticClass: \"shop-image\",\n      attrs: {\n        src: image,\n        alt: _vm.shop.name\n      }\n    })]);\n  }), 1)], 1), _c(\"div\", {\n    staticClass: \"shop-info\"\n  }, [_c(\"div\", {\n    staticClass: \"shop-header\"\n  }, [_c(\"h2\", {\n    staticClass: \"shop-name\"\n  }, [_vm._v(_vm._s(_vm.shop.name))]), _c(\"div\", {\n    staticClass: \"shop-type\"\n  }, [_vm._v(_vm._s(_vm.shop.typeName))])]), _c(\"div\", {\n    staticClass: \"shop-rating\"\n  }, [_c(\"el-rate\", {\n    attrs: {\n      disabled: \"\",\n      \"show-score\": \"\",\n      \"text-color\": \"#ff9900\",\n      \"score-template\": \"{value}分\"\n    },\n    model: {\n      value: _vm.shop.score,\n      callback: function ($$v) {\n        _vm.$set(_vm.shop, \"score\", $$v);\n      },\n      expression: \"shop.score\"\n    }\n  }), _c(\"span\", {\n    staticClass: \"comment-count\"\n  }, [_vm._v(\"(\" + _vm._s(_vm.shop.comments) + \"条评价)\")])], 1), _c(\"div\", {\n    staticClass: \"shop-address\"\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-location-outline\"\n  }), _c(\"span\", [_vm._v(_vm._s(_vm.shop.address))])]), _vm.shop.phone ? _c(\"div\", {\n    staticClass: \"shop-phone\"\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-phone-outline\"\n  }), _c(\"span\", [_vm._v(_vm._s(_vm.shop.phone))]), _c(\"el-button\", {\n    attrs: {\n      type: \"text\"\n    },\n    on: {\n      click: _vm.callShop\n    }\n  }, [_vm._v(\"拨打电话\")])], 1) : _vm._e(), _vm.shop.openHours ? _c(\"div\", {\n    staticClass: \"shop-hours\"\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-time\"\n  }), _c(\"span\", [_vm._v(\"营业时间：\" + _vm._s(_vm.shop.openHours))])]) : _vm._e()]), _vm.shop.intro ? _c(\"div\", {\n    staticClass: \"shop-intro\"\n  }, [_c(\"h3\", [_vm._v(\"商户介绍\")]), _c(\"p\", [_vm._v(_vm._s(_vm.shop.intro))])]) : _vm._e(), _c(\"div\", {\n    staticClass: \"shop-actions\"\n  }, [_c(\"el-button\", {\n    attrs: {\n      type: \"primary\"\n    },\n    on: {\n      click: _vm.navigateToShop\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-location\"\n  }), _vm._v(\" 导航 \")]), _c(\"el-button\", {\n    on: {\n      click: _vm.shareShop\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-share\"\n  }), _vm._v(\" 分享 \")]), _c(\"el-button\", {\n    on: {\n      click: _vm.collectShop\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-star-off\"\n  }), _vm._v(\" 收藏 \")])], 1)]) : _vm._e(), _vm.loading ? _c(\"LoadingSpinner\", {\n    attrs: {\n      \"full-screen\": true,\n      text: \"加载中...\"\n    }\n  }) : _vm._e(), !_vm.loading && !_vm.shop.id ? _c(\"EmptyState\", {\n    attrs: {\n      text: \"商户信息不存在\",\n      icon: \"el-icon-warning\",\n      \"show-action\": true,\n      \"action-text\": \"返回\"\n    },\n    on: {\n      action: _vm.goBack\n    }\n  }) : _vm._e()], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "title", "shop", "name", "on", "back", "goBack", "id", "height", "_l", "shopImages", "image", "index", "key", "src", "alt", "_v", "_s", "typeName", "disabled", "model", "value", "score", "callback", "$$v", "$set", "expression", "comments", "address", "phone", "type", "click", "callShop", "_e", "openHours", "intro", "navigateToShop", "shareShop", "collectShop", "loading", "text", "icon", "action", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/src/views/ShopDetail.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"shop-detail-page\" },\n    [\n      _c(\"HeaderBar\", {\n        attrs: { title: _vm.shop.name || \"商户详情\", \"show-back\": true },\n        on: { back: _vm.goBack },\n      }),\n      _vm.shop.id\n        ? _c(\"div\", { staticClass: \"shop-detail\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"shop-images\" },\n              [\n                _c(\n                  \"el-carousel\",\n                  {\n                    attrs: { height: \"200px\", \"indicator-position\": \"outside\" },\n                  },\n                  _vm._l(_vm.shopImages, function (image, index) {\n                    return _c(\"el-carousel-item\", { key: index }, [\n                      _c(\"img\", {\n                        staticClass: \"shop-image\",\n                        attrs: { src: image, alt: _vm.shop.name },\n                      }),\n                    ])\n                  }),\n                  1\n                ),\n              ],\n              1\n            ),\n            _c(\"div\", { staticClass: \"shop-info\" }, [\n              _c(\"div\", { staticClass: \"shop-header\" }, [\n                _c(\"h2\", { staticClass: \"shop-name\" }, [\n                  _vm._v(_vm._s(_vm.shop.name)),\n                ]),\n                _c(\"div\", { staticClass: \"shop-type\" }, [\n                  _vm._v(_vm._s(_vm.shop.typeName)),\n                ]),\n              ]),\n              _c(\n                \"div\",\n                { staticClass: \"shop-rating\" },\n                [\n                  _c(\"el-rate\", {\n                    attrs: {\n                      disabled: \"\",\n                      \"show-score\": \"\",\n                      \"text-color\": \"#ff9900\",\n                      \"score-template\": \"{value}分\",\n                    },\n                    model: {\n                      value: _vm.shop.score,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.shop, \"score\", $$v)\n                      },\n                      expression: \"shop.score\",\n                    },\n                  }),\n                  _c(\"span\", { staticClass: \"comment-count\" }, [\n                    _vm._v(\"(\" + _vm._s(_vm.shop.comments) + \"条评价)\"),\n                  ]),\n                ],\n                1\n              ),\n              _c(\"div\", { staticClass: \"shop-address\" }, [\n                _c(\"i\", { staticClass: \"el-icon-location-outline\" }),\n                _c(\"span\", [_vm._v(_vm._s(_vm.shop.address))]),\n              ]),\n              _vm.shop.phone\n                ? _c(\n                    \"div\",\n                    { staticClass: \"shop-phone\" },\n                    [\n                      _c(\"i\", { staticClass: \"el-icon-phone-outline\" }),\n                      _c(\"span\", [_vm._v(_vm._s(_vm.shop.phone))]),\n                      _c(\n                        \"el-button\",\n                        {\n                          attrs: { type: \"text\" },\n                          on: { click: _vm.callShop },\n                        },\n                        [_vm._v(\"拨打电话\")]\n                      ),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm.shop.openHours\n                ? _c(\"div\", { staticClass: \"shop-hours\" }, [\n                    _c(\"i\", { staticClass: \"el-icon-time\" }),\n                    _c(\"span\", [\n                      _vm._v(\"营业时间：\" + _vm._s(_vm.shop.openHours)),\n                    ]),\n                  ])\n                : _vm._e(),\n            ]),\n            _vm.shop.intro\n              ? _c(\"div\", { staticClass: \"shop-intro\" }, [\n                  _c(\"h3\", [_vm._v(\"商户介绍\")]),\n                  _c(\"p\", [_vm._v(_vm._s(_vm.shop.intro))]),\n                ])\n              : _vm._e(),\n            _c(\n              \"div\",\n              { staticClass: \"shop-actions\" },\n              [\n                _c(\n                  \"el-button\",\n                  {\n                    attrs: { type: \"primary\" },\n                    on: { click: _vm.navigateToShop },\n                  },\n                  [\n                    _c(\"i\", { staticClass: \"el-icon-location\" }),\n                    _vm._v(\" 导航 \"),\n                  ]\n                ),\n                _c(\"el-button\", { on: { click: _vm.shareShop } }, [\n                  _c(\"i\", { staticClass: \"el-icon-share\" }),\n                  _vm._v(\" 分享 \"),\n                ]),\n                _c(\"el-button\", { on: { click: _vm.collectShop } }, [\n                  _c(\"i\", { staticClass: \"el-icon-star-off\" }),\n                  _vm._v(\" 收藏 \"),\n                ]),\n              ],\n              1\n            ),\n          ])\n        : _vm._e(),\n      _vm.loading\n        ? _c(\"LoadingSpinner\", {\n            attrs: { \"full-screen\": true, text: \"加载中...\" },\n          })\n        : _vm._e(),\n      !_vm.loading && !_vm.shop.id\n        ? _c(\"EmptyState\", {\n            attrs: {\n              text: \"商户信息不存在\",\n              icon: \"el-icon-warning\",\n              \"show-action\": true,\n              \"action-text\": \"返回\",\n            },\n            on: { action: _vm.goBack },\n          })\n        : _vm._e(),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAmB,CAAC,EACnC,CACEF,EAAE,CAAC,WAAW,EAAE;IACdG,KAAK,EAAE;MAAEC,KAAK,EAAEL,GAAG,CAACM,IAAI,CAACC,IAAI,IAAI,MAAM;MAAE,WAAW,EAAE;IAAK,CAAC;IAC5DC,EAAE,EAAE;MAAEC,IAAI,EAAET,GAAG,CAACU;IAAO;EACzB,CAAC,CAAC,EACFV,GAAG,CAACM,IAAI,CAACK,EAAE,GACPV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CACA,aAAa,EACb;IACEG,KAAK,EAAE;MAAEQ,MAAM,EAAE,OAAO;MAAE,oBAAoB,EAAE;IAAU;EAC5D,CAAC,EACDZ,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,UAAU,EAAE,UAAUC,KAAK,EAAEC,KAAK,EAAE;IAC7C,OAAOf,EAAE,CAAC,kBAAkB,EAAE;MAAEgB,GAAG,EAAED;IAAM,CAAC,EAAE,CAC5Cf,EAAE,CAAC,KAAK,EAAE;MACRE,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE;QAAEc,GAAG,EAAEH,KAAK;QAAEI,GAAG,EAAEnB,GAAG,CAACM,IAAI,CAACC;MAAK;IAC1C,CAAC,CAAC,CACH,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDN,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACrCH,GAAG,CAACoB,EAAE,CAACpB,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAACM,IAAI,CAACC,IAAI,CAAC,CAAC,CAC9B,CAAC,EACFN,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAACoB,EAAE,CAACpB,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAACM,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAClC,CAAC,CACH,CAAC,EACFrB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CAAC,SAAS,EAAE;IACZG,KAAK,EAAE;MACLmB,QAAQ,EAAE,EAAE;MACZ,YAAY,EAAE,EAAE;MAChB,YAAY,EAAE,SAAS;MACvB,gBAAgB,EAAE;IACpB,CAAC;IACDC,KAAK,EAAE;MACLC,KAAK,EAAEzB,GAAG,CAACM,IAAI,CAACoB,KAAK;MACrBC,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;QACvB5B,GAAG,CAAC6B,IAAI,CAAC7B,GAAG,CAACM,IAAI,EAAE,OAAO,EAAEsB,GAAG,CAAC;MAClC,CAAC;MACDE,UAAU,EAAE;IACd;EACF,CAAC,CAAC,EACF7B,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACoB,EAAE,CAAC,GAAG,GAAGpB,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAACM,IAAI,CAACyB,QAAQ,CAAC,GAAG,MAAM,CAAC,CACjD,CAAC,CACH,EACD,CACF,CAAC,EACD9B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAA2B,CAAC,CAAC,EACpDF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACoB,EAAE,CAACpB,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAACM,IAAI,CAAC0B,OAAO,CAAC,CAAC,CAAC,CAAC,CAC/C,CAAC,EACFhC,GAAG,CAACM,IAAI,CAAC2B,KAAK,GACVhC,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAa,CAAC,EAC7B,CACEF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAwB,CAAC,CAAC,EACjDF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACoB,EAAE,CAACpB,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAACM,IAAI,CAAC2B,KAAK,CAAC,CAAC,CAAC,CAAC,EAC5ChC,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MAAE8B,IAAI,EAAE;IAAO,CAAC;IACvB1B,EAAE,EAAE;MAAE2B,KAAK,EAAEnC,GAAG,CAACoC;IAAS;EAC5B,CAAC,EACD,CAACpC,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,GACDpB,GAAG,CAACqC,EAAE,CAAC,CAAC,EACZrC,GAAG,CAACM,IAAI,CAACgC,SAAS,GACdrC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,CAAC,EACxCF,EAAE,CAAC,MAAM,EAAE,CACTD,GAAG,CAACoB,EAAE,CAAC,OAAO,GAAGpB,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAACM,IAAI,CAACgC,SAAS,CAAC,CAAC,CAC7C,CAAC,CACH,CAAC,GACFtC,GAAG,CAACqC,EAAE,CAAC,CAAC,CACb,CAAC,EACFrC,GAAG,CAACM,IAAI,CAACiC,KAAK,GACVtC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BnB,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACoB,EAAE,CAACpB,GAAG,CAACqB,EAAE,CAACrB,GAAG,CAACM,IAAI,CAACiC,KAAK,CAAC,CAAC,CAAC,CAAC,CAC1C,CAAC,GACFvC,GAAG,CAACqC,EAAE,CAAC,CAAC,EACZpC,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MAAE8B,IAAI,EAAE;IAAU,CAAC;IAC1B1B,EAAE,EAAE;MAAE2B,KAAK,EAAEnC,GAAG,CAACwC;IAAe;EAClC,CAAC,EACD,CACEvC,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,CAAC,EAC5CH,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CAElB,CAAC,EACDnB,EAAE,CAAC,WAAW,EAAE;IAAEO,EAAE,EAAE;MAAE2B,KAAK,EAAEnC,GAAG,CAACyC;IAAU;EAAE,CAAC,EAAE,CAChDxC,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,CAAC,EACzCH,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFnB,EAAE,CAAC,WAAW,EAAE;IAAEO,EAAE,EAAE;MAAE2B,KAAK,EAAEnC,GAAG,CAAC0C;IAAY;EAAE,CAAC,EAAE,CAClDzC,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,CAAC,EAC5CH,GAAG,CAACoB,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,CACH,EACD,CACF,CAAC,CACF,CAAC,GACFpB,GAAG,CAACqC,EAAE,CAAC,CAAC,EACZrC,GAAG,CAAC2C,OAAO,GACP1C,EAAE,CAAC,gBAAgB,EAAE;IACnBG,KAAK,EAAE;MAAE,aAAa,EAAE,IAAI;MAAEwC,IAAI,EAAE;IAAS;EAC/C,CAAC,CAAC,GACF5C,GAAG,CAACqC,EAAE,CAAC,CAAC,EACZ,CAACrC,GAAG,CAAC2C,OAAO,IAAI,CAAC3C,GAAG,CAACM,IAAI,CAACK,EAAE,GACxBV,EAAE,CAAC,YAAY,EAAE;IACfG,KAAK,EAAE;MACLwC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,iBAAiB;MACvB,aAAa,EAAE,IAAI;MACnB,aAAa,EAAE;IACjB,CAAC;IACDrC,EAAE,EAAE;MAAEsC,MAAM,EAAE9C,GAAG,CAACU;IAAO;EAC3B,CAAC,CAAC,GACFV,GAAG,CAACqC,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIU,eAAe,GAAG,EAAE;AACxBhD,MAAM,CAACiD,aAAa,GAAG,IAAI;AAE3B,SAASjD,MAAM,EAAEgD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}