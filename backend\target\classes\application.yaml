server:
  port: 8081
spring:
  application:
    name: hmdp
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************
    username: root
    password: 123456
  redis:
    host: 127.0.0.1
    port: 6379
    password: 123321
    lettuce:
      pool:
        max-active: 10
        max-idle: 10
        min-idle: 1
        time-between-eviction-runs: 10s
        
  jackson:
    default-property-inclusion: non_null # JSON处理时忽略非空字段
mybatis-plus:
  type-aliases-package: com.hmdp.entity # 别名扫描包
logging:
  level:
    com.hmdp: debug

# HMDP 博客上传配置
hmdp:
  blog:
    upload:
      # 是否启用博客图片上传功能
      enabled: true
      # 图片上传本地存储目录
      upload-dir: C:\Users\<USER>\Desktop\interview\hmqp\nginx-1.18.0\html\hmdp\imgs
      # 图片访问URL前缀（可选，如果不配置则返回相对路径）
      url-prefix: http://localhost:8080/hmdp/imgs
      # 最大文件大小（字节，默认5MB）
      max-file-size: 5242880
      # 允许的文件扩展名
      allowed-extensions:
        - jpg
        - jpeg
        - png
        - gif
        - bmp
        - webp

