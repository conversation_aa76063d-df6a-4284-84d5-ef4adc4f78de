# 博客图片上传功能测试说明

## 快速测试

### 1. 启动应用
确保应用正常启动，检查日志中是否有以下信息：
```
开始创建博客图片上传工具类对象
上传目录: C:\Users\<USER>\Desktop\interview\hmqp\nginx-1.18.0\html\hmdp\imgs
URL前缀: http://localhost:8080/hmdp/imgs
```

### 2. 使用Postman测试上传

#### 上传图片
- **URL**: `POST http://localhost:8081/upload/blog`
- **Headers**: `Content-Type: multipart/form-data`
- **Body**: 
  - 选择 `form-data`
  - Key: `file` (类型选择File)
  - Value: 选择一个图片文件

#### 预期响应
```json
{
    "success": true,
    "data": "http://localhost:8080/hmdp/imgs/blogs/5/12/a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg",
    "errorMsg": null
}
```

### 3. 测试删除功能

#### 删除图片
- **URL**: `GET http://localhost:8081/upload/blog/delete?name=/blogs/5/12/a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg`

#### 预期响应
```json
{
    "success": true,
    "data": "文件删除成功",
    "errorMsg": null
}
```

## 配置测试

### 1. 测试配置开关
在 `application.yaml` 中设置：
```yaml
hmdp:
  blog:
    upload:
      enabled: false
```
重启应用，此时会使用原有的上传方式。

### 2. 测试文件大小限制
上传一个超过5MB的文件，应该返回错误信息。

### 3. 测试文件类型限制
上传一个非图片文件（如.txt），应该返回错误信息。

## 故障排除

### 1. Bean创建失败
如果看到以下错误：
```
博客上传目录不能为空，请配置 hmdp.blog.upload.upload-dir
```
检查 `application.yaml` 中的 `upload-dir` 配置是否正确。

### 2. 目录权限问题
如果上传失败，检查配置的目录是否存在且有写权限。

### 3. 兼容模式
如果BlogUploadUtil未正确配置，系统会自动使用原有的上传方式，检查日志中的警告信息。

## 目录结构示例

上传成功后，文件会按以下结构存储：
```
C:\Users\<USER>\Desktop\interview\hmqp\nginx-1.18.0\html\hmdp\imgs\
└── blogs\
    ├── 0\
    │   ├── 0\
    │   ├── 1\
    │   └── ...
    ├── 1\
    │   ├── 0\
    │   ├── 1\
    │   └── ...
    └── ...
```

每个文件名都是UUID格式，确保唯一性。
