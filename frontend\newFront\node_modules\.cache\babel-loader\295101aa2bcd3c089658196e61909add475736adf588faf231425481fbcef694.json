{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: 'FooterBar',\n  props: {\n    activeBtn: {\n      type: Number,\n      default: 1\n    }\n  },\n  methods: {\n    handleNavClick(index) {\n      this.$emit('nav-click', index);\n\n      // 根据索引进行页面跳转\n      switch (index) {\n        case 0:\n          // 发布博客\n          this.$router.push('/blog-edit');\n          break;\n        case 1:\n          // 首页\n          this.$router.push('/');\n          break;\n        case 2:\n          // 地图（暂未实现）\n          this.$message.info('地图功能暂未开放');\n          break;\n        case 3:\n          // 消息（暂未实现）\n          this.$message.info('消息功能暂未开放');\n          break;\n        case 4:\n          // 我的\n          this.$router.push('/info');\n          break;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "activeBtn", "type", "Number", "default", "methods", "handleNavClick", "index", "$emit", "$router", "push", "$message", "info"], "sources": ["src/components/FooterBar.vue"], "sourcesContent": ["<template>\n  <div class=\"footer-bar\">\n    <div \n      class=\"footer-item\" \n      :class=\"{ active: activeBtn === 1 }\" \n      @click=\"handleNavClick(1)\"\n    >\n      <div class=\"footer-icon\">\n        <i class=\"el-icon-s-home\"></i>\n      </div>\n      <div class=\"footer-text\">首页</div>\n    </div>\n    \n    <div \n      class=\"footer-item\" \n      :class=\"{ active: activeBtn === 2 }\" \n      @click=\"handleNavClick(2)\"\n    >\n      <div class=\"footer-icon\">\n        <i class=\"el-icon-map-location\"></i>\n      </div>\n      <div class=\"footer-text\">地图</div>\n    </div>\n    \n    <div class=\"footer-item\" @click=\"handleNavClick(0)\">\n      <div class=\"add-button\">\n        <img src=\"/imgs/add.png\" alt=\"发布\" class=\"add-icon\">\n      </div>\n    </div>\n    \n    <div \n      class=\"footer-item\" \n      :class=\"{ active: activeBtn === 3 }\" \n      @click=\"handleNavClick(3)\"\n    >\n      <div class=\"footer-icon\">\n        <i class=\"el-icon-chat-dot-round\"></i>\n      </div>\n      <div class=\"footer-text\">消息</div>\n    </div>\n    \n    <div \n      class=\"footer-item\" \n      :class=\"{ active: activeBtn === 4 }\" \n      @click=\"handleNavClick(4)\"\n    >\n      <div class=\"footer-icon\">\n        <i class=\"el-icon-user\"></i>\n      </div>\n      <div class=\"footer-text\">我的</div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'FooterBar',\n  props: {\n    activeBtn: {\n      type: Number,\n      default: 1\n    }\n  },\n  methods: {\n    handleNavClick(index) {\n      this.$emit('nav-click', index)\n      \n      // 根据索引进行页面跳转\n      switch (index) {\n        case 0:\n          // 发布博客\n          this.$router.push('/blog-edit')\n          break\n        case 1:\n          // 首页\n          this.$router.push('/')\n          break\n        case 2:\n          // 地图（暂未实现）\n          this.$message.info('地图功能暂未开放')\n          break\n        case 3:\n          // 消息（暂未实现）\n          this.$message.info('消息功能暂未开放')\n          break\n        case 4:\n          // 我的\n          this.$router.push('/info')\n          break\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.footer-bar {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 60px;\n  background-color: #fff;\n  border-top: 1px solid #e8e8e8;\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n  z-index: 1000;\n}\n\n.footer-item {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.footer-item:hover {\n  background-color: #f5f5f5;\n}\n\n.footer-item.active {\n  color: #ff6633;\n}\n\n.footer-icon {\n  font-size: 20px;\n  margin-bottom: 2px;\n}\n\n.footer-text {\n  font-size: 12px;\n  color: inherit;\n}\n\n.add-button {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.add-icon {\n  width: 32px;\n  height: 32px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .footer-bar {\n    height: 50px;\n  }\n  \n  .footer-icon {\n    font-size: 18px;\n  }\n  \n  .footer-text {\n    font-size: 10px;\n  }\n  \n  .add-icon {\n    width: 28px;\n    height: 28px;\n  }\n}\n</style>\n"], "mappings": ";AAuDA;EACAA,IAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,OAAA;IACAC,eAAAC,KAAA;MACA,KAAAC,KAAA,cAAAD,KAAA;;MAEA;MACA,QAAAA,KAAA;QACA;UACA;UACA,KAAAE,OAAA,CAAAC,IAAA;UACA;QACA;UACA;UACA,KAAAD,OAAA,CAAAC,IAAA;UACA;QACA;UACA;UACA,KAAAC,QAAA,CAAAC,IAAA;UACA;QACA;UACA;UACA,KAAAD,QAAA,CAAAC,IAAA;UACA;QACA;UACA;UACA,KAAAH,OAAA,CAAAC,IAAA;UACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}