package com.hmdp.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedissonConfig {

    @Bean
    public RedissonClient redissonClient() {

        // 1. 创建配置对象
        Config config = new Config();

        // 2. 配置使用 单机模式； useClusterServers 多个redis服务器，集群
        config.useSingleServer().setAddress("redis://127.0.0.1:6379").setPassword("123321");
        return Redisson.create(config);
    }
}
