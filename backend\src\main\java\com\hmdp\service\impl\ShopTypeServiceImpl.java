package com.hmdp.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hmdp.dto.Result;
import com.hmdp.entity.ShopType;
import com.hmdp.mapper.ShopTypeMapper;
import com.hmdp.service.IShopTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 服务实现类
 * </p>
 */
@Service
public class ShopTypeServiceImpl extends ServiceImpl<ShopTypeMapper, ShopType> implements IShopTypeService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ShopTypeMapper shopTypeMapper;

    private static final String CACHE_SHOP_TYPE_KEY = "cache:shop:list_individual"; // 更改key，与String类型方案区分
    private static final Long CACHE_TTL_MINUTES = 30L;
    private static final Long CACHE_NULL_TTL_MINUTES = 5L;

    private static final String CACHE_NULL_VALUE = "[]";

    /**
     * @return
     */
    @Override
    public Result queryList() {
        // 1. 从Redis查询商铺类型缓存 (STRING类型)
        String jsonCache = stringRedisTemplate.opsForValue().get(CACHE_SHOP_TYPE_KEY);

        // 2. 判断缓存是否命中
        if (StrUtil.isNotBlank(jsonCache)) {
            // 2.1 缓存命中，直接将JSON数组字符串反序列化为List并返回
            // Hutool的toList方法可以方便地处理JSON数组
            List<ShopType> shopTypeList = JSONUtil.toList(jsonCache, ShopType.class);
            return Result.ok(shopTypeList);
        }

        // 3. 缓存未命中，查询数据库
        // 使用MyBatis-Plus的链式查询，按sort字段升序排序
        List<ShopType> shopTypeList = query().orderByAsc("sort").list();

        // 4. 判断数据库中是否存在数据
        if (shopTypeList == null || shopTypeList.isEmpty()) {
            // 4.1 数据库也不存在，为了防止缓存穿透，将空值写入Redis
            // 我们存入一个空的JSON数组字符串 "[]" 并设置较短的TTL
            stringRedisTemplate.opsForValue().set(CACHE_SHOP_TYPE_KEY, CACHE_NULL_VALUE, CACHE_NULL_TTL_MINUTES, TimeUnit.MINUTES);
            // 返回一个空列表给前端
            return Result.ok(Collections.emptyList());
        }

        // 5. 数据库存在数据，将其序列化为JSON数组字符串，并写入Redis
        stringRedisTemplate.opsForValue().set(CACHE_SHOP_TYPE_KEY, JSONUtil.toJsonStr(shopTypeList), CACHE_TTL_MINUTES, TimeUnit.MINUTES);

        // 6. 返回数据给前端
        return Result.ok(shopTypeList);
    }
}
