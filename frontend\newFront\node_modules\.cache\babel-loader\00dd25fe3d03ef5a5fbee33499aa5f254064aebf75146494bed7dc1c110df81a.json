{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport HeaderBar from '@/components/HeaderBar.vue';\nimport FooterBar from '@/components/FooterBar.vue';\nimport LoadingSpinner from '@/components/LoadingSpinner.vue';\nimport { userApi } from '@/api';\nexport default {\n  name: 'UserInfo',\n  components: {\n    HeaderBar,\n    FooterBar,\n    LoadingSpinner\n  },\n  data() {\n    return {\n      user: null,\n      signCount: 0,\n      loading: false\n    };\n  },\n  created() {\n    this.loadUserInfo();\n    this.loadSignCount();\n  },\n  methods: {\n    // 加载用户信息\n    async loadUserInfo() {\n      this.loading = true;\n      try {\n        const response = await userApi.getCurrentUser();\n        this.user = response.data;\n      } catch (error) {\n        console.error('加载用户信息失败:', error);\n        this.$message.error('加载用户信息失败');\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 加载签到次数\n    async loadSignCount() {\n      try {\n        const response = await userApi.getSignCount();\n        this.signCount = response.data || 0;\n      } catch (error) {\n        console.error('加载签到信息失败:', error);\n      }\n    },\n    // 格式化手机号\n    formatPhone(phone) {\n      if (!phone) return '';\n      return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2');\n    },\n    // 返回上一页\n    goBack() {\n      this.$router.go(-1);\n    },\n    // 跳转到编辑信息\n    toEditInfo() {\n      this.$router.push('/info-edit');\n    },\n    // 跳转到我的博客\n    toMyBlogs() {\n      this.$message.info('我的博客功能开发中');\n    },\n    // 跳转到我的收藏\n    toMyCollections() {\n      this.$message.info('我的收藏功能开发中');\n    },\n    // 跳转到我的关注\n    toMyFollows() {\n      this.$message.info('我的关注功能开发中');\n    },\n    // 签到\n    async signIn() {\n      try {\n        await userApi.sign();\n        this.$message.success('签到成功');\n        this.loadSignCount();\n      } catch (error) {\n        console.error('签到失败:', error);\n        this.$message.error('签到失败');\n      }\n    },\n    // 显示设置\n    showSettings() {\n      this.$message.info('设置功能开发中');\n    },\n    // 显示关于我们\n    showAbout() {\n      this.$message.info('关于我们功能开发中');\n    },\n    // 退出登录\n    handleLogout() {\n      this.$confirm('确定要退出登录吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$store.dispatch('logout');\n        this.$message.success('已退出登录');\n        this.$router.push('/login');\n      }).catch(() => {\n        // 用户取消\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "LoadingSpinner", "userApi", "name", "components", "data", "user", "signCount", "loading", "created", "loadUserInfo", "loadSignCount", "methods", "response", "getCurrentUser", "error", "console", "$message", "getSignCount", "formatPhone", "phone", "replace", "goBack", "$router", "go", "toEditInfo", "push", "toMyBlogs", "info", "toMyCollections", "toMyFollows", "signIn", "sign", "success", "showSettings", "showAbout", "handleLogout", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "$store", "dispatch", "catch"], "sources": ["src/views/UserInfo.vue"], "sourcesContent": ["<template>\n  <div class=\"user-info-page\">\n    <!-- 头部 -->\n    <HeaderBar \n      title=\"个人信息\" \n      :show-back=\"true\" \n      @back=\"goBack\"\n    />\n    \n    <!-- 用户信息 -->\n    <div v-if=\"user\" class=\"user-profile\">\n      <!-- 用户头像和基本信息 -->\n      <div class=\"user-header\">\n        <div class=\"user-avatar\">\n          <img :src=\"user.icon || '/imgs/icons/default-icon.png'\" :alt=\"user.nickName\">\n        </div>\n        <div class=\"user-info\">\n          <div class=\"user-name\">{{ user.nickName || '未设置昵称' }}</div>\n          <div class=\"user-phone\">{{ formatPhone(user.phone) }}</div>\n        </div>\n        <div class=\"edit-button\">\n          <el-button type=\"text\" @click=\"toEditInfo\">编辑</el-button>\n        </div>\n      </div>\n      \n      <!-- 功能菜单 -->\n      <div class=\"menu-list\">\n        <div class=\"menu-item\" @click=\"toMyBlogs\">\n          <i class=\"el-icon-document\"></i>\n          <span>我的博客</span>\n          <i class=\"el-icon-arrow-right\"></i>\n        </div>\n        \n        <div class=\"menu-item\" @click=\"toMyCollections\">\n          <i class=\"el-icon-star-off\"></i>\n          <span>我的收藏</span>\n          <i class=\"el-icon-arrow-right\"></i>\n        </div>\n        \n        <div class=\"menu-item\" @click=\"toMyFollows\">\n          <i class=\"el-icon-user\"></i>\n          <span>我的关注</span>\n          <i class=\"el-icon-arrow-right\"></i>\n        </div>\n        \n        <div class=\"menu-item\" @click=\"signIn\">\n          <i class=\"el-icon-calendar\"></i>\n          <span>签到</span>\n          <span class=\"sign-count\">已连续签到{{ signCount }}天</span>\n        </div>\n      </div>\n      \n      <!-- 设置菜单 -->\n      <div class=\"menu-list\">\n        <div class=\"menu-item\" @click=\"showSettings\">\n          <i class=\"el-icon-setting\"></i>\n          <span>设置</span>\n          <i class=\"el-icon-arrow-right\"></i>\n        </div>\n        \n        <div class=\"menu-item\" @click=\"showAbout\">\n          <i class=\"el-icon-info\"></i>\n          <span>关于我们</span>\n          <i class=\"el-icon-arrow-right\"></i>\n        </div>\n      </div>\n      \n      <!-- 退出登录 -->\n      <div class=\"logout-section\">\n        <el-button type=\"danger\" plain @click=\"handleLogout\">退出登录</el-button>\n      </div>\n    </div>\n    \n    <!-- 底部导航 -->\n    <FooterBar :active-btn=\"4\" />\n    \n    <!-- 加载状态 -->\n    <LoadingSpinner v-if=\"loading\" :full-screen=\"true\" text=\"加载中...\" />\n  </div>\n</template>\n\n<script>\nimport HeaderBar from '@/components/HeaderBar.vue'\nimport FooterBar from '@/components/FooterBar.vue'\nimport LoadingSpinner from '@/components/LoadingSpinner.vue'\nimport { userApi } from '@/api'\n\nexport default {\n  name: 'UserInfo',\n  components: {\n    HeaderBar,\n    FooterBar,\n    LoadingSpinner\n  },\n  data() {\n    return {\n      user: null,\n      signCount: 0,\n      loading: false\n    }\n  },\n  created() {\n    this.loadUserInfo()\n    this.loadSignCount()\n  },\n  methods: {\n    // 加载用户信息\n    async loadUserInfo() {\n      this.loading = true\n      try {\n        const response = await userApi.getCurrentUser()\n        this.user = response.data\n      } catch (error) {\n        console.error('加载用户信息失败:', error)\n        this.$message.error('加载用户信息失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 加载签到次数\n    async loadSignCount() {\n      try {\n        const response = await userApi.getSignCount()\n        this.signCount = response.data || 0\n      } catch (error) {\n        console.error('加载签到信息失败:', error)\n      }\n    },\n    \n    // 格式化手机号\n    formatPhone(phone) {\n      if (!phone) return ''\n      return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2')\n    },\n    \n    // 返回上一页\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    // 跳转到编辑信息\n    toEditInfo() {\n      this.$router.push('/info-edit')\n    },\n    \n    // 跳转到我的博客\n    toMyBlogs() {\n      this.$message.info('我的博客功能开发中')\n    },\n    \n    // 跳转到我的收藏\n    toMyCollections() {\n      this.$message.info('我的收藏功能开发中')\n    },\n    \n    // 跳转到我的关注\n    toMyFollows() {\n      this.$message.info('我的关注功能开发中')\n    },\n    \n    // 签到\n    async signIn() {\n      try {\n        await userApi.sign()\n        this.$message.success('签到成功')\n        this.loadSignCount()\n      } catch (error) {\n        console.error('签到失败:', error)\n        this.$message.error('签到失败')\n      }\n    },\n    \n    // 显示设置\n    showSettings() {\n      this.$message.info('设置功能开发中')\n    },\n    \n    // 显示关于我们\n    showAbout() {\n      this.$message.info('关于我们功能开发中')\n    },\n    \n    // 退出登录\n    handleLogout() {\n      this.$confirm('确定要退出登录吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$store.dispatch('logout')\n        this.$message.success('已退出登录')\n        this.$router.push('/login')\n      }).catch(() => {\n        // 用户取消\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.user-info-page {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  padding-bottom: 60px;\n}\n\n.user-profile {\n  padding-top: 50px;\n}\n\n/* 用户头部 */\n.user-header {\n  background-color: #fff;\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.user-avatar {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  overflow: hidden;\n  margin-right: 15px;\n}\n\n.user-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.user-info {\n  flex: 1;\n}\n\n.user-name {\n  font-size: 18px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.user-phone {\n  font-size: 14px;\n  color: #666;\n}\n\n.edit-button .el-button {\n  color: #ff6633;\n}\n\n/* 菜单列表 */\n.menu-list {\n  background-color: #fff;\n  margin-bottom: 10px;\n}\n\n.menu-item {\n  display: flex;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n}\n\n.menu-item:last-child {\n  border-bottom: none;\n}\n\n.menu-item:hover {\n  background-color: #fafafa;\n}\n\n.menu-item i:first-child {\n  font-size: 18px;\n  color: #666;\n  margin-right: 15px;\n  width: 20px;\n}\n\n.menu-item span {\n  flex: 1;\n  font-size: 14px;\n  color: #333;\n}\n\n.sign-count {\n  font-size: 12px;\n  color: #999;\n  margin-left: auto;\n  margin-right: 10px;\n}\n\n.menu-item i:last-child {\n  font-size: 14px;\n  color: #ccc;\n}\n\n/* 退出登录 */\n.logout-section {\n  padding: 20px;\n}\n\n.logout-section .el-button {\n  width: 100%;\n  height: 45px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .user-header {\n    padding: 15px;\n  }\n  \n  .user-avatar {\n    width: 50px;\n    height: 50px;\n  }\n  \n  .user-name {\n    font-size: 16px;\n  }\n  \n  .menu-item {\n    padding: 12px 15px;\n  }\n}\n</style>\n"], "mappings": ";AAkFA,OAAAA,SAAA;AACA,OAAAC,SAAA;AACA,OAAAC,cAAA;AACA,SAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL,SAAA;IACAC,SAAA;IACAC;EACA;EACAI,KAAA;IACA;MACAC,IAAA;MACAC,SAAA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACA;IACA,MAAAF,aAAA;MACA,KAAAF,OAAA;MACA;QACA,MAAAK,QAAA,SAAAX,OAAA,CAAAY,cAAA;QACA,KAAAR,IAAA,GAAAO,QAAA,CAAAR,IAAA;MACA,SAAAU,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;QACA,KAAAP,OAAA;MACA;IACA;IAEA;IACA,MAAAG,cAAA;MACA;QACA,MAAAE,QAAA,SAAAX,OAAA,CAAAgB,YAAA;QACA,KAAAX,SAAA,GAAAM,QAAA,CAAAR,IAAA;MACA,SAAAU,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IAEA;IACAI,YAAAC,KAAA;MACA,KAAAA,KAAA;MACA,OAAAA,KAAA,CAAAC,OAAA;IACA;IAEA;IACAC,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IAEA;IACAC,WAAA;MACA,KAAAF,OAAA,CAAAG,IAAA;IACA;IAEA;IACAC,UAAA;MACA,KAAAV,QAAA,CAAAW,IAAA;IACA;IAEA;IACAC,gBAAA;MACA,KAAAZ,QAAA,CAAAW,IAAA;IACA;IAEA;IACAE,YAAA;MACA,KAAAb,QAAA,CAAAW,IAAA;IACA;IAEA;IACA,MAAAG,OAAA;MACA;QACA,MAAA7B,OAAA,CAAA8B,IAAA;QACA,KAAAf,QAAA,CAAAgB,OAAA;QACA,KAAAtB,aAAA;MACA,SAAAI,KAAA;QACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;IACA;IAEA;IACAmB,aAAA;MACA,KAAAjB,QAAA,CAAAW,IAAA;IACA;IAEA;IACAO,UAAA;MACA,KAAAlB,QAAA,CAAAW,IAAA;IACA;IAEA;IACAQ,aAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACA,KAAAC,MAAA,CAAAC,QAAA;QACA,KAAA1B,QAAA,CAAAgB,OAAA;QACA,KAAAV,OAAA,CAAAG,IAAA;MACA,GAAAkB,KAAA;QACA;MAAA,CACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}