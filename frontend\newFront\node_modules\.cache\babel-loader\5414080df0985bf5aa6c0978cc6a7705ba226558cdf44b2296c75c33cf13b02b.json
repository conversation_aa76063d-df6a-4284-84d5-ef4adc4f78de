{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"footer-bar\"\n  }, [_c(\"div\", {\n    staticClass: \"footer-item\",\n    class: {\n      active: _vm.activeBtn === 1\n    },\n    on: {\n      click: function ($event) {\n        return _vm.handleNavClick(1);\n      }\n    }\n  }, [_vm._m(0), _c(\"div\", {\n    staticClass: \"footer-text\"\n  }, [_vm._v(\"首页\")])]), _c(\"div\", {\n    staticClass: \"footer-item\",\n    class: {\n      active: _vm.activeBtn === 2\n    },\n    on: {\n      click: function ($event) {\n        return _vm.handleNavClick(2);\n      }\n    }\n  }, [_vm._m(1), _c(\"div\", {\n    staticClass: \"footer-text\"\n  }, [_vm._v(\"地图\")])]), _c(\"div\", {\n    staticClass: \"footer-item\",\n    on: {\n      click: function ($event) {\n        return _vm.handleNavClick(0);\n      }\n    }\n  }, [_vm._m(2)]), _c(\"div\", {\n    staticClass: \"footer-item\",\n    class: {\n      active: _vm.activeBtn === 3\n    },\n    on: {\n      click: function ($event) {\n        return _vm.handleNavClick(3);\n      }\n    }\n  }, [_vm._m(3), _c(\"div\", {\n    staticClass: \"footer-text\"\n  }, [_vm._v(\"消息\")])]), _c(\"div\", {\n    staticClass: \"footer-item\",\n    class: {\n      active: _vm.activeBtn === 4\n    },\n    on: {\n      click: function ($event) {\n        return _vm.handleNavClick(4);\n      }\n    }\n  }, [_vm._m(4), _c(\"div\", {\n    staticClass: \"footer-text\"\n  }, [_vm._v(\"我的\")])])]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"footer-icon\"\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-s-home\"\n  })]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"footer-icon\"\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-map-location\"\n  })]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"add-button\"\n  }, [_c(\"img\", {\n    staticClass: \"add-icon\",\n    attrs: {\n      src: \"/imgs/add.png\",\n      alt: \"发布\"\n    }\n  })]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"footer-icon\"\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-chat-dot-round\"\n  })]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"footer-icon\"\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-user\"\n  })]);\n}];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "class", "active", "activeBtn", "on", "click", "$event", "handleNavClick", "_m", "_v", "staticRenderFns", "attrs", "src", "alt", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/src/components/FooterBar.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"footer-bar\" }, [\n    _c(\n      \"div\",\n      {\n        staticClass: \"footer-item\",\n        class: { active: _vm.activeBtn === 1 },\n        on: {\n          click: function ($event) {\n            return _vm.handleNavClick(1)\n          },\n        },\n      },\n      [_vm._m(0), _c(\"div\", { staticClass: \"footer-text\" }, [_vm._v(\"首页\")])]\n    ),\n    _c(\n      \"div\",\n      {\n        staticClass: \"footer-item\",\n        class: { active: _vm.activeBtn === 2 },\n        on: {\n          click: function ($event) {\n            return _vm.handleNavClick(2)\n          },\n        },\n      },\n      [_vm._m(1), _c(\"div\", { staticClass: \"footer-text\" }, [_vm._v(\"地图\")])]\n    ),\n    _c(\n      \"div\",\n      {\n        staticClass: \"footer-item\",\n        on: {\n          click: function ($event) {\n            return _vm.handleNavClick(0)\n          },\n        },\n      },\n      [_vm._m(2)]\n    ),\n    _c(\n      \"div\",\n      {\n        staticClass: \"footer-item\",\n        class: { active: _vm.activeBtn === 3 },\n        on: {\n          click: function ($event) {\n            return _vm.handleNavClick(3)\n          },\n        },\n      },\n      [_vm._m(3), _c(\"div\", { staticClass: \"footer-text\" }, [_vm._v(\"消息\")])]\n    ),\n    _c(\n      \"div\",\n      {\n        staticClass: \"footer-item\",\n        class: { active: _vm.activeBtn === 4 },\n        on: {\n          click: function ($event) {\n            return _vm.handleNavClick(4)\n          },\n        },\n      },\n      [_vm._m(4), _c(\"div\", { staticClass: \"footer-text\" }, [_vm._v(\"我的\")])]\n    ),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"footer-icon\" }, [\n      _c(\"i\", { staticClass: \"el-icon-s-home\" }),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"footer-icon\" }, [\n      _c(\"i\", { staticClass: \"el-icon-map-location\" }),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"add-button\" }, [\n      _c(\"img\", {\n        staticClass: \"add-icon\",\n        attrs: { src: \"/imgs/add.png\", alt: \"发布\" },\n      }),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"footer-icon\" }, [\n      _c(\"i\", { staticClass: \"el-icon-chat-dot-round\" }),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"footer-icon\" }, [\n      _c(\"i\", { staticClass: \"el-icon-user\" }),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAC9CF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,aAAa;IAC1BC,KAAK,EAAE;MAAEC,MAAM,EAAEL,GAAG,CAACM,SAAS,KAAK;IAAE,CAAC;IACtCC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOT,GAAG,CAACU,cAAc,CAAC,CAAC,CAAC;MAC9B;IACF;EACF,CAAC,EACD,CAACV,GAAG,CAACW,EAAE,CAAC,CAAC,CAAC,EAAEV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACY,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CACvE,CAAC,EACDX,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,aAAa;IAC1BC,KAAK,EAAE;MAAEC,MAAM,EAAEL,GAAG,CAACM,SAAS,KAAK;IAAE,CAAC;IACtCC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOT,GAAG,CAACU,cAAc,CAAC,CAAC,CAAC;MAC9B;IACF;EACF,CAAC,EACD,CAACV,GAAG,CAACW,EAAE,CAAC,CAAC,CAAC,EAAEV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACY,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CACvE,CAAC,EACDX,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,aAAa;IAC1BI,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOT,GAAG,CAACU,cAAc,CAAC,CAAC,CAAC;MAC9B;IACF;EACF,CAAC,EACD,CAACV,GAAG,CAACW,EAAE,CAAC,CAAC,CAAC,CACZ,CAAC,EACDV,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,aAAa;IAC1BC,KAAK,EAAE;MAAEC,MAAM,EAAEL,GAAG,CAACM,SAAS,KAAK;IAAE,CAAC;IACtCC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOT,GAAG,CAACU,cAAc,CAAC,CAAC,CAAC;MAC9B;IACF;EACF,CAAC,EACD,CAACV,GAAG,CAACW,EAAE,CAAC,CAAC,CAAC,EAAEV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACY,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CACvE,CAAC,EACDX,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,aAAa;IAC1BC,KAAK,EAAE;MAAEC,MAAM,EAAEL,GAAG,CAACM,SAAS,KAAK;IAAE,CAAC;IACtCC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOT,GAAG,CAACU,cAAc,CAAC,CAAC,CAAC;MAC9B;IACF;EACF,CAAC,EACD,CAACV,GAAG,CAACW,EAAE,CAAC,CAAC,CAAC,EAAEV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACY,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CACvE,CAAC,CACF,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,CACpB,YAAY;EACV,IAAIb,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAC/CF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,CAAC,CAC3C,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIH,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAC/CF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAuB,CAAC,CAAC,CACjD,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIH,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAC9CF,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,UAAU;IACvBW,KAAK,EAAE;MAAEC,GAAG,EAAE,eAAe;MAAEC,GAAG,EAAE;IAAK;EAC3C,CAAC,CAAC,CACH,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIhB,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAC/CF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAyB,CAAC,CAAC,CACnD,CAAC;AACJ,CAAC,EACD,YAAY;EACV,IAAIH,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAC/CF,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,CAAC,CACzC,CAAC;AACJ,CAAC,CACF;AACDJ,MAAM,CAACkB,aAAa,GAAG,IAAI;AAE3B,SAASlB,MAAM,EAAEc,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}