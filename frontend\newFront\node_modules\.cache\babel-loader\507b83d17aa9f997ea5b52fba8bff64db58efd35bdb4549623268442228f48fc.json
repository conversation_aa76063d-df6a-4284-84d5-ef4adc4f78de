{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"other-user-info-page\"\n  }, [_c(\"HeaderBar\", {\n    attrs: {\n      title: \"用户信息\",\n      \"show-back\": true\n    },\n    on: {\n      back: _vm.goBack\n    }\n  }), _vm.user ? _c(\"div\", {\n    staticClass: \"user-profile\"\n  }, [_c(\"div\", {\n    staticClass: \"user-header\"\n  }, [_c(\"div\", {\n    staticClass: \"user-avatar\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: _vm.user.icon || \"/imgs/icons/default-icon.png\",\n      alt: _vm.user.nickName\n    }\n  })]), _c(\"div\", {\n    staticClass: \"user-info\"\n  }, [_c(\"div\", {\n    staticClass: \"user-name\"\n  }, [_vm._v(_vm._s(_vm.user.nickName || \"未设置昵称\"))]), _c(\"div\", {\n    staticClass: \"user-intro\"\n  }, [_vm._v(_vm._s(_vm.user.intro || \"这个人很懒，什么都没有留下\"))])]), _c(\"div\", {\n    staticClass: \"follow-button\"\n  }, [_c(\"el-button\", {\n    attrs: {\n      type: \"primary\"\n    },\n    on: {\n      click: _vm.followUser\n    }\n  }, [_vm._v(\"关注\")])], 1)]), _c(\"div\", {\n    staticClass: \"user-stats\"\n  }, [_c(\"div\", {\n    staticClass: \"stat-item\"\n  }, [_c(\"div\", {\n    staticClass: \"stat-number\"\n  }, [_vm._v(_vm._s(_vm.user.blogCount || 0))]), _c(\"div\", {\n    staticClass: \"stat-label\"\n  }, [_vm._v(\"博客\")])]), _c(\"div\", {\n    staticClass: \"stat-item\"\n  }, [_c(\"div\", {\n    staticClass: \"stat-number\"\n  }, [_vm._v(_vm._s(_vm.user.followCount || 0))]), _c(\"div\", {\n    staticClass: \"stat-label\"\n  }, [_vm._v(\"关注\")])]), _c(\"div\", {\n    staticClass: \"stat-item\"\n  }, [_c(\"div\", {\n    staticClass: \"stat-number\"\n  }, [_vm._v(_vm._s(_vm.user.fansCount || 0))]), _c(\"div\", {\n    staticClass: \"stat-label\"\n  }, [_vm._v(\"粉丝\")])])]), _c(\"div\", {\n    staticClass: \"user-blogs\"\n  }, [_c(\"div\", {\n    staticClass: \"section-title\"\n  }, [_vm._v(\"TA的博客\")]), _vm.blogs.length > 0 ? _c(\"div\", {\n    staticClass: \"blog-list\"\n  }, _vm._l(_vm.blogs, function (blog) {\n    return _c(\"div\", {\n      key: blog.id,\n      staticClass: \"blog-item\",\n      on: {\n        click: function ($event) {\n          return _vm.toBlogDetail(blog.id);\n        }\n      }\n    }, [_c(\"div\", {\n      staticClass: \"blog-image\"\n    }, [_c(\"img\", {\n      attrs: {\n        src: blog.img,\n        alt: blog.title\n      }\n    })]), _c(\"div\", {\n      staticClass: \"blog-content\"\n    }, [_c(\"div\", {\n      staticClass: \"blog-title\"\n    }, [_vm._v(_vm._s(blog.title))]), _c(\"div\", {\n      staticClass: \"blog-time\"\n    }, [_vm._v(_vm._s(_vm.formatTime(blog.createTime)))])])]);\n  }), 0) : _c(\"div\", {\n    staticClass: \"no-blogs\"\n  }, [_vm._v(\" 暂无博客 \")])])]) : _vm._e(), _vm.loading ? _c(\"LoadingSpinner\", {\n    attrs: {\n      \"full-screen\": true,\n      text: \"加载中...\"\n    }\n  }) : _vm._e(), !_vm.loading && !_vm.user ? _c(\"EmptyState\", {\n    attrs: {\n      text: \"用户不存在\",\n      icon: \"el-icon-warning\",\n      \"show-action\": true,\n      \"action-text\": \"返回\"\n    },\n    on: {\n      action: _vm.goBack\n    }\n  }) : _vm._e()], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "title", "on", "back", "goBack", "user", "src", "icon", "alt", "nick<PERSON><PERSON>", "_v", "_s", "intro", "type", "click", "followUser", "blogCount", "followCount", "fansCount", "blogs", "length", "_l", "blog", "key", "id", "$event", "toBlogDetail", "img", "formatTime", "createTime", "_e", "loading", "text", "action", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/src/views/OtherUserInfo.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"other-user-info-page\" },\n    [\n      _c(\"HeaderBar\", {\n        attrs: { title: \"用户信息\", \"show-back\": true },\n        on: { back: _vm.goBack },\n      }),\n      _vm.user\n        ? _c(\"div\", { staticClass: \"user-profile\" }, [\n            _c(\"div\", { staticClass: \"user-header\" }, [\n              _c(\"div\", { staticClass: \"user-avatar\" }, [\n                _c(\"img\", {\n                  attrs: {\n                    src: _vm.user.icon || \"/imgs/icons/default-icon.png\",\n                    alt: _vm.user.nickName,\n                  },\n                }),\n              ]),\n              _c(\"div\", { staticClass: \"user-info\" }, [\n                _c(\"div\", { staticClass: \"user-name\" }, [\n                  _vm._v(_vm._s(_vm.user.nickName || \"未设置昵称\")),\n                ]),\n                _c(\"div\", { staticClass: \"user-intro\" }, [\n                  _vm._v(\n                    _vm._s(_vm.user.intro || \"这个人很懒，什么都没有留下\")\n                  ),\n                ]),\n              ]),\n              _c(\n                \"div\",\n                { staticClass: \"follow-button\" },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { type: \"primary\" },\n                      on: { click: _vm.followUser },\n                    },\n                    [_vm._v(\"关注\")]\n                  ),\n                ],\n                1\n              ),\n            ]),\n            _c(\"div\", { staticClass: \"user-stats\" }, [\n              _c(\"div\", { staticClass: \"stat-item\" }, [\n                _c(\"div\", { staticClass: \"stat-number\" }, [\n                  _vm._v(_vm._s(_vm.user.blogCount || 0)),\n                ]),\n                _c(\"div\", { staticClass: \"stat-label\" }, [_vm._v(\"博客\")]),\n              ]),\n              _c(\"div\", { staticClass: \"stat-item\" }, [\n                _c(\"div\", { staticClass: \"stat-number\" }, [\n                  _vm._v(_vm._s(_vm.user.followCount || 0)),\n                ]),\n                _c(\"div\", { staticClass: \"stat-label\" }, [_vm._v(\"关注\")]),\n              ]),\n              _c(\"div\", { staticClass: \"stat-item\" }, [\n                _c(\"div\", { staticClass: \"stat-number\" }, [\n                  _vm._v(_vm._s(_vm.user.fansCount || 0)),\n                ]),\n                _c(\"div\", { staticClass: \"stat-label\" }, [_vm._v(\"粉丝\")]),\n              ]),\n            ]),\n            _c(\"div\", { staticClass: \"user-blogs\" }, [\n              _c(\"div\", { staticClass: \"section-title\" }, [_vm._v(\"TA的博客\")]),\n              _vm.blogs.length > 0\n                ? _c(\n                    \"div\",\n                    { staticClass: \"blog-list\" },\n                    _vm._l(_vm.blogs, function (blog) {\n                      return _c(\n                        \"div\",\n                        {\n                          key: blog.id,\n                          staticClass: \"blog-item\",\n                          on: {\n                            click: function ($event) {\n                              return _vm.toBlogDetail(blog.id)\n                            },\n                          },\n                        },\n                        [\n                          _c(\"div\", { staticClass: \"blog-image\" }, [\n                            _c(\"img\", {\n                              attrs: { src: blog.img, alt: blog.title },\n                            }),\n                          ]),\n                          _c(\"div\", { staticClass: \"blog-content\" }, [\n                            _c(\"div\", { staticClass: \"blog-title\" }, [\n                              _vm._v(_vm._s(blog.title)),\n                            ]),\n                            _c(\"div\", { staticClass: \"blog-time\" }, [\n                              _vm._v(_vm._s(_vm.formatTime(blog.createTime))),\n                            ]),\n                          ]),\n                        ]\n                      )\n                    }),\n                    0\n                  )\n                : _c(\"div\", { staticClass: \"no-blogs\" }, [\n                    _vm._v(\" 暂无博客 \"),\n                  ]),\n            ]),\n          ])\n        : _vm._e(),\n      _vm.loading\n        ? _c(\"LoadingSpinner\", {\n            attrs: { \"full-screen\": true, text: \"加载中...\" },\n          })\n        : _vm._e(),\n      !_vm.loading && !_vm.user\n        ? _c(\"EmptyState\", {\n            attrs: {\n              text: \"用户不存在\",\n              icon: \"el-icon-warning\",\n              \"show-action\": true,\n              \"action-text\": \"返回\",\n            },\n            on: { action: _vm.goBack },\n          })\n        : _vm._e(),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAuB,CAAC,EACvC,CACEF,EAAE,CAAC,WAAW,EAAE;IACdG,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAE,WAAW,EAAE;IAAK,CAAC;IAC3CC,EAAE,EAAE;MAAEC,IAAI,EAAEP,GAAG,CAACQ;IAAO;EACzB,CAAC,CAAC,EACFR,GAAG,CAACS,IAAI,GACJR,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IACRG,KAAK,EAAE;MACLM,GAAG,EAAEV,GAAG,CAACS,IAAI,CAACE,IAAI,IAAI,8BAA8B;MACpDC,GAAG,EAAEZ,GAAG,CAACS,IAAI,CAACI;IAChB;EACF,CAAC,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAACc,EAAE,CAACd,GAAG,CAACe,EAAE,CAACf,GAAG,CAACS,IAAI,CAACI,QAAQ,IAAI,OAAO,CAAC,CAAC,CAC7C,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCH,GAAG,CAACc,EAAE,CACJd,GAAG,CAACe,EAAE,CAACf,GAAG,CAACS,IAAI,CAACO,KAAK,IAAI,eAAe,CAC1C,CAAC,CACF,CAAC,CACH,CAAC,EACFf,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEF,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MAAEa,IAAI,EAAE;IAAU,CAAC;IAC1BX,EAAE,EAAE;MAAEY,KAAK,EAAElB,GAAG,CAACmB;IAAW;EAC9B,CAAC,EACD,CAACnB,GAAG,CAACc,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,EACFb,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCH,GAAG,CAACc,EAAE,CAACd,GAAG,CAACe,EAAE,CAACf,GAAG,CAACS,IAAI,CAACW,SAAS,IAAI,CAAC,CAAC,CAAC,CACxC,CAAC,EACFnB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CACzD,CAAC,EACFb,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCH,GAAG,CAACc,EAAE,CAACd,GAAG,CAACe,EAAE,CAACf,GAAG,CAACS,IAAI,CAACY,WAAW,IAAI,CAAC,CAAC,CAAC,CAC1C,CAAC,EACFpB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CACzD,CAAC,EACFb,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCH,GAAG,CAACc,EAAE,CAACd,GAAG,CAACe,EAAE,CAACf,GAAG,CAACS,IAAI,CAACa,SAAS,IAAI,CAAC,CAAC,CAAC,CACxC,CAAC,EACFrB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CACzD,CAAC,CACH,CAAC,EACFb,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAACH,GAAG,CAACc,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC9Dd,GAAG,CAACuB,KAAK,CAACC,MAAM,GAAG,CAAC,GAChBvB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAY,CAAC,EAC5BH,GAAG,CAACyB,EAAE,CAACzB,GAAG,CAACuB,KAAK,EAAE,UAAUG,IAAI,EAAE;IAChC,OAAOzB,EAAE,CACP,KAAK,EACL;MACE0B,GAAG,EAAED,IAAI,CAACE,EAAE;MACZzB,WAAW,EAAE,WAAW;MACxBG,EAAE,EAAE;QACFY,KAAK,EAAE,SAAAA,CAAUW,MAAM,EAAE;UACvB,OAAO7B,GAAG,CAAC8B,YAAY,CAACJ,IAAI,CAACE,EAAE,CAAC;QAClC;MACF;IACF,CAAC,EACD,CACE3B,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,KAAK,EAAE;MACRG,KAAK,EAAE;QAAEM,GAAG,EAAEgB,IAAI,CAACK,GAAG;QAAEnB,GAAG,EAAEc,IAAI,CAACrB;MAAM;IAC1C,CAAC,CAAC,CACH,CAAC,EACFJ,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACvCH,GAAG,CAACc,EAAE,CAACd,GAAG,CAACe,EAAE,CAACW,IAAI,CAACrB,KAAK,CAAC,CAAC,CAC3B,CAAC,EACFJ,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCH,GAAG,CAACc,EAAE,CAACd,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgC,UAAU,CAACN,IAAI,CAACO,UAAU,CAAC,CAAC,CAAC,CAChD,CAAC,CACH,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,GACDhC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCH,GAAG,CAACc,EAAE,CAAC,QAAQ,CAAC,CACjB,CAAC,CACP,CAAC,CACH,CAAC,GACFd,GAAG,CAACkC,EAAE,CAAC,CAAC,EACZlC,GAAG,CAACmC,OAAO,GACPlC,EAAE,CAAC,gBAAgB,EAAE;IACnBG,KAAK,EAAE;MAAE,aAAa,EAAE,IAAI;MAAEgC,IAAI,EAAE;IAAS;EAC/C,CAAC,CAAC,GACFpC,GAAG,CAACkC,EAAE,CAAC,CAAC,EACZ,CAAClC,GAAG,CAACmC,OAAO,IAAI,CAACnC,GAAG,CAACS,IAAI,GACrBR,EAAE,CAAC,YAAY,EAAE;IACfG,KAAK,EAAE;MACLgC,IAAI,EAAE,OAAO;MACbzB,IAAI,EAAE,iBAAiB;MACvB,aAAa,EAAE,IAAI;MACnB,aAAa,EAAE;IACjB,CAAC;IACDL,EAAE,EAAE;MAAE+B,MAAM,EAAErC,GAAG,CAACQ;IAAO;EAC3B,CAAC,CAAC,GACFR,GAAG,CAACkC,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAII,eAAe,GAAG,EAAE;AACxBvC,MAAM,CAACwC,aAAa,GAAG,IAAI;AAE3B,SAASxC,MAAM,EAAEuC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}