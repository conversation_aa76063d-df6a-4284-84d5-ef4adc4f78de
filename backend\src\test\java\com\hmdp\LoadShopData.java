package com.hmdp;

import com.hmdp.entity.Shop;
import com.hmdp.service.IShopService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.geo.Point;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest
public class LoadShopData {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IShopService shopService;

    @Test
    public void LoadShopData() {
        // 1. 查询店铺 id
        List<Shop> shopList = shopService.list();

        // 2. 把店铺分组，按照 type 分组；typeId 相同的放到同一个组
        Map<Long, List<Shop>> map = shopList.stream().collect(Collectors.groupingBy(Shop::getTypeId));
        // 3. 分批写入 redis
        for (Map.Entry<Long, List<Shop>> longListEntry : map.entrySet()) {
            // 3.1 获取类型 ID
            Long typeId = longListEntry.getKey();
            // 3.2 获取店铺
            List<Shop> value = longListEntry.getValue();

            String key = "shop:geo:" + typeId;

            // 3.3
            List<RedisGeoCommands.GeoLocation<String>> locations = new ArrayList<>();

            for (Shop shop : value) {
//                stringRedisTemplate.opsForGeo().add(key, new Point(shop.getX(), shop.getY()), shop.getId().toString());
                locations.add(new RedisGeoCommands.GeoLocation<>(shop.getId().toString(), new Point(shop.getX(), shop.getY())));
            }

            stringRedisTemplate.opsForGeo().add(key, locations);
        }
    }
}
