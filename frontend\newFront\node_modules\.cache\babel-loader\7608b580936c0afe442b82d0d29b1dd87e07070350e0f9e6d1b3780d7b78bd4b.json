{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: 'HeaderBar',\n  props: {\n    title: {\n      type: String,\n      default: '黑马点评'\n    },\n    showBack: {\n      type: Boolean,\n      default: false\n    },\n    showSearch: {\n      type: Boolean,\n      default: false\n    },\n    showUser: {\n      type: Boolean,\n      default: false\n    }\n  },\n  methods: {\n    goBack() {\n      this.$emit('back');\n      // 使用nextTick确保事件已经被处理\n      this.$nextTick(() => {\n        // 如果父组件没有处理back事件，则默认返回上一页\n        if (!this.$attrs.onBack && !this.$attrs['onBack']) {\n          this.$router.go(-1);\n        }\n      });\n    },\n    handleSearch() {\n      this.$emit('search');\n    },\n    toUserInfo() {\n      this.$emit('user-click');\n      // 使用nextTick确保事件已经被处理\n      this.$nextTick(() => {\n        // 如果父组件没有处理user-click事件，则默认跳转到用户信息页\n        if (!this.$attrs.onUserClick && !this.$attrs['onUser-click']) {\n          this.$router.push('/info');\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "title", "type", "String", "default", "showBack", "Boolean", "showSearch", "showUser", "methods", "goBack", "$emit", "$nextTick", "$attrs", "onBack", "$router", "go", "handleSearch", "toUserInfo", "onUserClick", "push"], "sources": ["src/components/HeaderBar.vue"], "sourcesContent": ["<template>\n  <div class=\"header-bar\">\n    <!-- 返回按钮 -->\n    <div v-if=\"showBack\" class=\"header-back\" @click=\"goBack\">\n      <i class=\"el-icon-arrow-left\"></i>\n    </div>\n    \n    <!-- 标题 -->\n    <div class=\"header-title\">\n      {{ title }}\n    </div>\n    \n    <!-- 右侧操作区 -->\n    <div class=\"header-actions\">\n      <slot name=\"actions\">\n        <!-- 默认搜索图标 -->\n        <div v-if=\"showSearch\" class=\"header-action\" @click=\"handleSearch\">\n          <i class=\"el-icon-search\"></i>\n        </div>\n        \n        <!-- 用户头像 -->\n        <div v-if=\"showUser\" class=\"header-action\" @click=\"toUserInfo\">\n          <i class=\"el-icon-user\"></i>\n        </div>\n      </slot>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HeaderBar',\n  props: {\n    title: {\n      type: String,\n      default: '黑马点评'\n    },\n    showBack: {\n      type: Boolean,\n      default: false\n    },\n    showSearch: {\n      type: Boolean,\n      default: false\n    },\n    showUser: {\n      type: Boolean,\n      default: false\n    }\n  },\n  methods: {\n    goBack() {\n      this.$emit('back')\n      // 使用nextTick确保事件已经被处理\n      this.$nextTick(() => {\n        // 如果父组件没有处理back事件，则默认返回上一页\n        if (!this.$attrs.onBack && !this.$attrs['onBack']) {\n          this.$router.go(-1)\n        }\n      })\n    },\n\n    handleSearch() {\n      this.$emit('search')\n    },\n\n    toUserInfo() {\n      this.$emit('user-click')\n      // 使用nextTick确保事件已经被处理\n      this.$nextTick(() => {\n        // 如果父组件没有处理user-click事件，则默认跳转到用户信息页\n        if (!this.$attrs.onUserClick && !this.$attrs['onUser-click']) {\n          this.$router.push('/info')\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.header-bar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 50px;\n  background-color: #fff;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 15px;\n  z-index: 1000;\n}\n\n.header-back {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  border-radius: 50%;\n  transition: background-color 0.3s ease;\n}\n\n.header-back:hover {\n  background-color: #f5f5f5;\n}\n\n.header-back i {\n  font-size: 18px;\n  color: #333;\n}\n\n.header-title {\n  flex: 1;\n  text-align: center;\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  /* 如果有返回按钮，标题需要左偏移 */\n  margin-left: -40px;\n}\n\n.header-bar:not(.has-back) .header-title {\n  margin-left: 0;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.header-action {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  border-radius: 50%;\n  transition: background-color 0.3s ease;\n}\n\n.header-action:hover {\n  background-color: #f5f5f5;\n}\n\n.header-action i {\n  font-size: 18px;\n  color: #333;\n}\n\n/* 当有返回按钮时，调整标题位置 */\n.header-bar:has(.header-back) .header-title {\n  margin-left: -40px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .header-bar {\n    height: 45px;\n    padding: 0 10px;\n  }\n  \n  .header-title {\n    font-size: 14px;\n  }\n  \n  .header-back,\n  .header-action {\n    width: 35px;\n    height: 35px;\n  }\n  \n  .header-back i,\n  .header-action i {\n    font-size: 16px;\n  }\n}\n</style>\n"], "mappings": ";AA8BA;EACAA,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;IACAG,UAAA;MACAL,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;IACAI,QAAA;MACAN,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;EACA;EACAK,OAAA;IACAC,OAAA;MACA,KAAAC,KAAA;MACA;MACA,KAAAC,SAAA;QACA;QACA,UAAAC,MAAA,CAAAC,MAAA,UAAAD,MAAA;UACA,KAAAE,OAAA,CAAAC,EAAA;QACA;MACA;IACA;IAEAC,aAAA;MACA,KAAAN,KAAA;IACA;IAEAO,WAAA;MACA,KAAAP,KAAA;MACA;MACA,KAAAC,SAAA;QACA;QACA,UAAAC,MAAA,CAAAM,WAAA,UAAAN,MAAA;UACA,KAAAE,OAAA,CAAAK,IAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}