# 阿里云OSS博客上传功能集成文档

## 概述

本文档详细介绍了如何将博客图片上传功能集成阿里云OSS存储。该实现支持本地存储和阿里云OSS两种方式，可通过配置文件灵活切换，完全兼容原有系统。

## 功能特性

### ✨ 核心特性
- **双存储支持**: 支持本地存储和阿里云OSS存储
- **配置化切换**: 通过配置文件一键切换存储方式
- **安全可靠**: 完整的文件验证和异常处理
- **分层存储**: 使用哈希算法创建分层目录结构
- **向后兼容**: 保留原有上传方式，确保平滑升级

### 🔧 技术特点
- **模块化设计**: 清晰的代码结构，易于维护
- **依赖注入**: 使用Spring Boot自动配置
- **条件装配**: 根据配置动态创建Bean
- **异常处理**: 完善的错误处理和日志记录

## 配置说明

### 1. 阿里云OSS配置

在 `application.yaml` 中添加阿里云OSS配置：

```yaml
# 阿里云OSS配置
sky:
  alioss:
    endpoint: oss-cn-beijing.aliyuncs.com
    access-key-id: LTAI5tMtDM6tVFiKAbuB2wZN
    access-key-secret: ******************************
    bucket-name: sky-take-out-01luck
```

### 2. 博客上传配置

```yaml
# HMDP 博客上传配置
hmdp:
  blog:
    upload:
      # 上传方式：local(本地存储) 或 oss(阿里云OSS)
      type: oss
      # 是否启用博客图片上传功能
      enabled: true
      # 本地存储配置（当type=local时使用）
      local:
        upload-dir: C:\Users\<USER>\Desktop\interview\hmqp\nginx-1.18.0\html\hmdp\imgs
        url-prefix: http://localhost:8080/hmdp/imgs
      # OSS存储配置（当type=oss时使用）
      oss:
        path-prefix: hmdp/blog/images
      # 通用配置
      max-file-size: 5242880  # 5MB
      allowed-extensions:
        - jpg
        - jpeg
        - png
        - gif
        - bmp
        - webp
```

### 3. Maven依赖

在 `pom.xml` 中添加阿里云OSS SDK依赖：

```xml
<!-- 阿里云OSS SDK -->
<dependency>
    <groupId>com.aliyun.oss</groupId>
    <artifactId>aliyun-sdk-oss</artifactId>
    <version>3.15.1</version>
</dependency>
```

## 核心组件

### 1. AliOssUtil - 阿里云OSS工具类

```java
@Data
@AllArgsConstructor
@Slf4j
public class AliOssUtil {
    private String endpoint;
    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;

    // 上传文件到OSS
    public String upload(byte[] bytes, String objectName) {
        // OSS客户端操作
        // 返回文件访问URL
    }

    // 从OSS删除文件
    public boolean delete(String objectName) {
        // OSS删除操作
        // 返回删除结果
    }
}
```

### 2. BlogUploadService - 统一上传服务

```java
@Slf4j
public class BlogUploadService {
    private final BlogUploadProperties properties;
    private final AliOssUtil aliOssUtil;

    // 根据配置选择上传方式
    public String uploadBlogImage(MultipartFile file) {
        if ("oss".equalsIgnoreCase(properties.getType())) {
            return uploadToOss(file, originalFilename);
        } else {
            return uploadToLocal(file, originalFilename);
        }
    }

    // 根据配置选择删除方式
    public boolean deleteBlogImage(String filename) {
        if ("oss".equalsIgnoreCase(properties.getType())) {
            return deleteFromOss(filename);
        } else {
            return deleteFromLocal(filename);
        }
    }
}
```

### 3. 配置类自动装配

```java
@Configuration
@Slf4j
public class BlogUploadConfiguration {
    
    @Bean
    @ConditionalOnMissingBean
    public AliOssUtil aliOssUtil(AliOssProperties aliOssProperties) {
        // 创建阿里云OSS工具类
    }

    @Bean
    @ConditionalOnProperty(prefix = "hmdp.blog.upload", name = "enabled", havingValue = "true")
    public BlogUploadService blogUploadService(BlogUploadProperties properties, 
                                               AliOssUtil aliOssUtil) {
        // 创建博客上传服务
    }
}
```

## 使用方式

### 1. 上传图片

**请求方式**: `POST /upload/blog`

**请求参数**:
- `file`: 图片文件 (multipart/form-data)

**OSS上传响应示例**:
```json
{
    "success": true,
    "data": "https://sky-take-out-01luck.oss-cn-beijing.aliyuncs.com/hmdp/blog/images/5/12/a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg",
    "errorMsg": null
}
```

**本地上传响应示例**:
```json
{
    "success": true,
    "data": "http://localhost:8080/hmdp/imgs/blogs/5/12/a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg",
    "errorMsg": null
}
```

### 2. 删除图片

**请求方式**: `GET /upload/blog/delete?name=文件名`

**OSS删除参数**:
- `name`: OSS对象名或完整URL

**本地删除参数**:
- `name`: 相对路径文件名

**响应示例**:
```json
{
    "success": true,
    "data": "文件删除成功",
    "errorMsg": null
}
```

## 文件存储结构

### OSS存储结构
```
Bucket: sky-take-out-01luck
├── hmdp/blog/images/
    ├── 0/
    │   ├── 0/
    │   │   └── uuid1.jpg
    │   ├── 1/
    │   │   └── uuid2.png
    │   └── ...
    ├── 1/
    │   ├── 0/
    │   └── ...
    └── ...
```

### 本地存储结构
```
C:\...\imgs\
└── blogs\
    ├── 0\
    │   ├── 0\
    │   ├── 1\
    │   └── ...
    ├── 1\
    └── ...
```

## 安全特性

### 1. 文件验证
- **文件大小**: 默认限制5MB
- **文件类型**: 仅允许图片格式
- **文件名**: 防止路径遍历攻击

### 2. 访问控制
- **OSS权限**: 使用AccessKey进行身份验证
- **本地权限**: 依赖服务器文件系统权限

### 3. 异常处理
- **网络异常**: OSS连接失败处理
- **存储异常**: 磁盘空间不足处理
- **权限异常**: 访问权限不足处理

## 切换存储方式

### 从本地存储切换到OSS
1. 修改配置文件中的 `type` 为 `oss`
2. 确保阿里云OSS配置正确
3. 重启应用

### 从OSS切换到本地存储
1. 修改配置文件中的 `type` 为 `local`
2. 确保本地存储目录配置正确
3. 重启应用

## 监控和日志

### 关键日志
- 上传方式选择日志
- 文件上传成功/失败日志
- OSS操作异常日志
- 配置验证日志

### 监控指标
- 上传成功率
- 平均上传时间
- 存储空间使用情况
- 异常发生频率

## 最佳实践

### 1. 生产环境建议
- 使用阿里云OSS存储，提高可靠性
- 配置CDN加速访问
- 定期备份重要文件
- 监控存储空间使用情况

### 2. 开发环境建议
- 使用本地存储，降低成本
- 配置合适的文件大小限制
- 定期清理测试文件

### 3. 安全建议
- 定期更换AccessKey
- 使用RAM子账号，限制权限范围
- 启用OSS访问日志
- 配置防盗链保护

## 故障排除

### 常见问题
1. **OSS连接失败**: 检查网络和AccessKey配置
2. **文件上传失败**: 检查文件大小和类型限制
3. **Bean创建失败**: 检查配置文件格式和必填项
4. **权限不足**: 检查OSS权限或本地目录权限

### 调试方法
1. 查看应用启动日志
2. 检查配置文件语法
3. 验证阿里云OSS控制台设置
4. 测试网络连接性
