package com.hmdp.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.hmdp.properties.BlogUploadProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.UUID;

/**
 * 博客图片上传服务类
 * 支持本地存储和阿里云OSS两种上传方式
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class BlogUploadService {

    private final BlogUploadProperties properties;
    private final AliOssUtil aliOssUtil;
    
    /**
     * 允许的图片文件扩展名
     */
    private static final String[] ALLOWED_EXTENSIONS = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
    
    /**
     * 最大文件大小（5MB）
     */
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024;

    public BlogUploadService(BlogUploadProperties properties, AliOssUtil aliOssUtil) {
        this.properties = properties;
        this.aliOssUtil = aliOssUtil;
    }

    /**
     * 上传博客图片
     * 根据配置自动选择本地存储或OSS存储
     * 
     * @param file 上传的文件
     * @return 返回图片的访问路径
     * @throws RuntimeException 当文件上传失败时抛出异常
     */
    public String uploadBlogImage(MultipartFile file) {
        try {
            // 1. 验证文件
            validateFile(file);
            
            // 2. 获取原始文件名
            String originalFilename = file.getOriginalFilename();
            log.info("开始上传文件: {}, 上传方式: {}", originalFilename, properties.getType());
            
            // 3. 根据配置选择上传方式
            String accessUrl;
            if ("oss".equalsIgnoreCase(properties.getType())) {
                accessUrl = uploadToOss(file, originalFilename);
            } else {
                accessUrl = uploadToLocal(file, originalFilename);
            }
            
            log.info("文件上传成功: {} -> {}", originalFilename, accessUrl);
            return accessUrl;
            
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除博客图片
     * 根据配置自动选择本地删除或OSS删除
     * 
     * @param filename 要删除的文件名（相对路径或OSS对象名）
     * @return 删除是否成功
     */
    public boolean deleteBlogImage(String filename) {
        try {
            // 验证文件名，防止路径遍历攻击
            if (StrUtil.isBlank(filename) || filename.contains("..")) {
                log.warn("非法的文件名: {}", filename);
                return false;
            }
            
            log.info("开始删除文件: {}, 删除方式: {}", filename, properties.getType());
            
            boolean deleted;
            if ("oss".equalsIgnoreCase(properties.getType())) {
                deleted = deleteFromOss(filename);
            } else {
                deleted = deleteFromLocal(filename);
            }
            
            if (deleted) {
                log.info("文件删除成功: {}", filename);
            } else {
                log.warn("文件删除失败: {}", filename);
            }
            
            return deleted;
            
        } catch (Exception e) {
            log.error("删除文件时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 上传文件到阿里云OSS
     */
    private String uploadToOss(MultipartFile file, String originalFilename) throws IOException {
        if (aliOssUtil == null) {
            throw new RuntimeException("阿里云OSS工具未配置，无法使用OSS上传");
        }
        
        // 生成OSS对象名
        String objectName = generateOssObjectName(originalFilename);
        
        // 上传到OSS
        return aliOssUtil.upload(file.getBytes(), objectName);
    }

    /**
     * 上传文件到本地
     */
    private String uploadToLocal(MultipartFile file, String originalFilename) throws IOException {
        // 生成新的文件名和路径
        String relativePath = generateLocalFileName(originalFilename);
        
        // 创建完整的文件路径
        String uploadDir = properties.getLocal().getUploadDir();
        if (StrUtil.isBlank(uploadDir)) {
            throw new RuntimeException("本地上传目录未配置，请配置 hmdp.blog.upload.local.upload-dir");
        }
        
        File targetFile = new File(uploadDir, relativePath);
        
        // 确保目录存在
        File parentDir = targetFile.getParentFile();
        if (!parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            if (!created) {
                throw new RuntimeException("创建目录失败: " + parentDir.getAbsolutePath());
            }
            log.info("创建目录: {}", parentDir.getAbsolutePath());
        }
        
        // 保存文件
        file.transferTo(targetFile);
        
        // 构建访问URL
        return buildLocalAccessUrl(relativePath);
    }

    /**
     * 从阿里云OSS删除文件
     */
    private boolean deleteFromOss(String objectName) {
        if (aliOssUtil == null) {
            log.warn("阿里云OSS工具未配置，无法删除OSS文件");
            return false;
        }
        
        // 如果传入的是完整URL，需要提取对象名
        String actualObjectName = extractOssObjectName(objectName);
        return aliOssUtil.delete(actualObjectName);
    }

    /**
     * 从本地删除文件
     */
    private boolean deleteFromLocal(String filename) {
        String uploadDir = properties.getLocal().getUploadDir();
        if (StrUtil.isBlank(uploadDir)) {
            log.warn("本地上传目录未配置，无法删除本地文件");
            return false;
        }
        
        File file = new File(uploadDir, filename);
        
        // 检查文件是否存在且不是目录
        if (!file.exists()) {
            log.warn("文件不存在: {}", file.getAbsolutePath());
            return false;
        }
        
        if (file.isDirectory()) {
            log.warn("不能删除目录: {}", file.getAbsolutePath());
            return false;
        }
        
        // 删除文件
        return FileUtil.del(file);
    }

    /**
     * 验证上传的文件
     */
    private void validateFile(MultipartFile file) {
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("上传文件不能为空");
        }
        
        // 检查文件大小
        long maxSize = properties.getMaxFileSize() != null ? properties.getMaxFileSize() : MAX_FILE_SIZE;
        if (file.getSize() > maxSize) {
            throw new RuntimeException("文件大小不能超过" + (maxSize / 1024 / 1024) + "MB");
        }
        
        // 检查文件名
        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            throw new RuntimeException("文件名不能为空");
        }
        
        // 检查文件扩展名
        String extension = StrUtil.subAfter(originalFilename, ".", true);
        if (StrUtil.isBlank(extension)) {
            throw new RuntimeException("文件必须有扩展名");
        }
        
        String[] allowedExts = properties.getAllowedExtensions() != null ? 
            properties.getAllowedExtensions() : ALLOWED_EXTENSIONS;
        
        boolean isAllowed = false;
        for (String allowedExt : allowedExts) {
            if (allowedExt.equalsIgnoreCase(extension)) {
                isAllowed = true;
                break;
            }
        }
        
        if (!isAllowed) {
            throw new RuntimeException("不支持的文件类型: " + extension + 
                "，支持的类型: " + String.join(", ", allowedExts));
        }
    }

    /**
     * 生成OSS对象名
     */
    private String generateOssObjectName(String originalFilename) {
        // 获取文件扩展名
        String extension = StrUtil.subAfter(originalFilename, ".", true);
        
        // 生成UUID作为文件名
        String uuid = UUID.randomUUID().toString();
        
        // 使用哈希值创建两级目录
        int hash = uuid.hashCode();
        int dir1 = hash & 0xF;        // 取低4位，范围0-15
        int dir2 = (hash >> 4) & 0xF; // 取第5-8位，范围0-15
        
        // 构建OSS对象名：pathPrefix/dir1/dir2/uuid.extension
        String pathPrefix = properties.getOss().getPathPrefix();
        return StrUtil.format("{}/{}/{}/{}.{}", pathPrefix, dir1, dir2, uuid, extension);
    }

    /**
     * 生成本地文件名
     */
    private String generateLocalFileName(String originalFilename) {
        // 获取文件扩展名
        String extension = StrUtil.subAfter(originalFilename, ".", true);
        
        // 生成UUID作为文件名
        String uuid = UUID.randomUUID().toString();
        
        // 使用哈希值创建两级目录
        int hash = uuid.hashCode();
        int dir1 = hash & 0xF;        // 取低4位，范围0-15
        int dir2 = (hash >> 4) & 0xF; // 取第5-8位，范围0-15
        
        // 构建相对路径：/blogs/dir1/dir2/uuid.extension
        return StrUtil.format("/blogs/{}/{}/{}.{}", dir1, dir2, uuid, extension);
    }

    /**
     * 构建本地文件访问URL
     */
    private String buildLocalAccessUrl(String relativePath) {
        String urlPrefix = properties.getLocal().getUrlPrefix();
        if (StrUtil.isBlank(urlPrefix)) {
            // 如果没有配置URL前缀，直接返回相对路径
            return relativePath;
        }
        
        // 确保URL前缀以/结尾，相对路径以/开头
        String prefix = urlPrefix.endsWith("/") ? urlPrefix : urlPrefix + "/";
        String path = relativePath.startsWith("/") ? relativePath.substring(1) : relativePath;
        
        return prefix + path;
    }

    /**
     * 从完整URL中提取OSS对象名
     */
    private String extractOssObjectName(String urlOrObjectName) {
        // 如果是完整的URL，提取对象名部分
        if (urlOrObjectName.startsWith("http")) {
            // 找到域名后的第一个/，后面的就是对象名
            int index = urlOrObjectName.indexOf("/", 8); // 跳过 https://
            if (index > 0) {
                return urlOrObjectName.substring(index + 1);
            }
        }
        
        // 如果不是URL，直接返回
        return urlOrObjectName;
    }
}
