package com.hmdp.config;

import com.hmdp.properties.AliOssProperties;
import com.hmdp.properties.BlogUploadProperties;
import com.hmdp.utils.AliOssUtil;
import com.hmdp.utils.BlogUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 博客上传配置类
 * 用于创建和配置博客图片上传相关的Bean
 * 支持本地存储和阿里云OSS两种上传方式
 *
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
@Slf4j
public class BlogUploadConfiguration {

    /**
     * 创建阿里云OSS工具类Bean
     *
     * @param aliOssProperties 阿里云OSS配置属性
     * @return AliOssUtil实例
     */
    @Bean
    @ConditionalOnMissingBean
    public AliOssUtil aliOssUtil(AliOssProperties aliOssProperties) {
        log.info("开始创建阿里云OSS工具类对象");
        log.info("OSS Endpoint: {}", aliOssProperties.getEndpoint());
        log.info("OSS Bucket: {}", aliOssProperties.getBucketName());
        log.info("OSS AccessKeyId: {}", aliOssProperties.getAccessKeyId());

        return new AliOssUtil(
                aliOssProperties.getEndpoint(),
                aliOssProperties.getAccessKeyId(),
                aliOssProperties.getAccessKeySecret(),
                aliOssProperties.getBucketName()
        );
    }

    /**
     * 创建博客上传服务Bean
     * 只有在配置了hmdp.blog.upload.enabled=true时才会创建
     *
     * @param blogUploadProperties 博客上传配置属性
     * @param aliOssUtil 阿里云OSS工具类（可选）
     * @return BlogUploadService实例
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "hmdp.blog.upload", name = "enabled", havingValue = "true", matchIfMissing = true)
    public BlogUploadService blogUploadService(BlogUploadProperties blogUploadProperties,
                                               AliOssUtil aliOssUtil) {
        log.info("开始创建博客图片上传服务对象");
        log.info("上传方式: {}", blogUploadProperties.getType());
        log.info("最大文件大小: {} bytes", blogUploadProperties.getMaxFileSize());
        log.info("允许的扩展名: {}", String.join(", ", blogUploadProperties.getAllowedExtensions()));

        // 根据上传方式验证配置
        if ("oss".equalsIgnoreCase(blogUploadProperties.getType())) {
            if (aliOssUtil == null) {
                throw new IllegalArgumentException("使用OSS上传方式时，阿里云OSS配置不能为空");
            }
            log.info("OSS路径前缀: {}", blogUploadProperties.getOss().getPathPrefix());
        } else {
            String uploadDir = blogUploadProperties.getLocal().getUploadDir();
            if (uploadDir == null || uploadDir.trim().isEmpty()) {
                throw new IllegalArgumentException("使用本地上传方式时，上传目录不能为空，请配置 hmdp.blog.upload.local.upload-dir");
            }
            log.info("本地上传目录: {}", uploadDir);
            log.info("本地URL前缀: {}", blogUploadProperties.getLocal().getUrlPrefix());
        }

        return new BlogUploadService(blogUploadProperties, aliOssUtil);
    }
}
