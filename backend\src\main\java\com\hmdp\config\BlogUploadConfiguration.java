package com.hmdp.config;

import com.hmdp.properties.BlogUploadProperties;
import com.hmdp.utils.BlogUploadUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 博客上传配置类
 * 用于创建和配置博客图片上传相关的Bean
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
@Slf4j
public class BlogUploadConfiguration {

    /**
     * 创建博客上传工具类Bean
     * 只有在配置了hmdp.blog.upload.enabled=true时才会创建
     * 
     * @param blogUploadProperties 博客上传配置属性
     * @return BlogUploadUtil实例
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "hmdp.blog.upload", name = "enabled", havingValue = "true", matchIfMissing = true)
    public BlogUploadUtil blogUploadUtil(BlogUploadProperties blogUploadProperties) {
        log.info("开始创建博客图片上传工具类对象");
        log.info("上传目录: {}", blogUploadProperties.getUploadDir());
        log.info("URL前缀: {}", blogUploadProperties.getUrlPrefix());
        log.info("最大文件大小: {} bytes", blogUploadProperties.getMaxFileSize());
        log.info("允许的扩展名: {}", String.join(", ", blogUploadProperties.getAllowedExtensions()));
        
        // 验证配置
        if (blogUploadProperties.getUploadDir() == null || blogUploadProperties.getUploadDir().trim().isEmpty()) {
            throw new IllegalArgumentException("博客上传目录不能为空，请配置 hmdp.blog.upload.upload-dir");
        }
        
        return new BlogUploadUtil(
                blogUploadProperties.getUploadDir(),
                blogUploadProperties.getUrlPrefix()
        );
    }
}
