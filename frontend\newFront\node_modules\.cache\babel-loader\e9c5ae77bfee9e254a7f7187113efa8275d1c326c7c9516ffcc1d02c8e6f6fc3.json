{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport HeaderBar from '@/components/HeaderBar.vue';\nimport LoadingSpinner from '@/components/LoadingSpinner.vue';\nimport EmptyState from '@/components/EmptyState.vue';\nimport { userApi, blogApi } from '@/api';\nexport default {\n  name: 'OtherUserInfo',\n  components: {\n    HeaderBar,\n    LoadingSpinner,\n    EmptyState\n  },\n  data() {\n    return {\n      user: null,\n      blogs: [],\n      loading: false\n    };\n  },\n  created() {\n    this.loadUserInfo();\n    this.loadUserBlogs();\n  },\n  methods: {\n    // 加载用户信息\n    async loadUserInfo() {\n      const userId = this.$route.params.id;\n      if (!userId) {\n        this.$message.error('用户ID不存在');\n        this.goBack();\n        return;\n      }\n      this.loading = true;\n      try {\n        const response = await userApi.getUserById(userId);\n        this.user = response.data;\n      } catch (error) {\n        console.error('加载用户信息失败:', error);\n        this.$message.error('加载用户信息失败');\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 加载用户博客\n    async loadUserBlogs() {\n      const userId = this.$route.params.id;\n      try {\n        const response = await blogApi.getBlogsByUserId({\n          id: userId,\n          current: 1\n        });\n        const blogs = response.data || [];\n        blogs.forEach(blog => {\n          if (blog.images) {\n            blog.img = blog.images.split(',')[0];\n          }\n        });\n        this.blogs = blogs;\n      } catch (error) {\n        console.error('加载用户博客失败:', error);\n      }\n    },\n    // 返回上一页\n    goBack() {\n      this.$router.go(-1);\n    },\n    // 关注用户\n    followUser() {\n      this.$message.info('关注功能开发中');\n    },\n    // 跳转到博客详情\n    toBlogDetail(blogId) {\n      this.$router.push(`/blog-detail/${blogId}`);\n    },\n    // 格式化时间\n    formatTime(time) {\n      if (!time) return '';\n      const date = new Date(time);\n      return date.toLocaleDateString();\n    }\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "LoadingSpinner", "EmptyState", "userApi", "blogApi", "name", "components", "data", "user", "blogs", "loading", "created", "loadUserInfo", "loadUserBlogs", "methods", "userId", "$route", "params", "id", "$message", "error", "goBack", "response", "getUserById", "console", "getBlogsByUserId", "current", "for<PERSON>ach", "blog", "images", "img", "split", "$router", "go", "followUser", "info", "toBlogDetail", "blogId", "push", "formatTime", "time", "date", "Date", "toLocaleDateString"], "sources": ["src/views/OtherUserInfo.vue"], "sourcesContent": ["<template>\n  <div class=\"other-user-info-page\">\n    <!-- 头部 -->\n    <HeaderBar \n      title=\"用户信息\" \n      :show-back=\"true\" \n      @back=\"goBack\"\n    />\n    \n    <!-- 用户信息 -->\n    <div v-if=\"user\" class=\"user-profile\">\n      <!-- 用户头像和基本信息 -->\n      <div class=\"user-header\">\n        <div class=\"user-avatar\">\n          <img :src=\"user.icon || '/imgs/icons/default-icon.png'\" :alt=\"user.nickName\">\n        </div>\n        <div class=\"user-info\">\n          <div class=\"user-name\">{{ user.nickName || '未设置昵称' }}</div>\n          <div class=\"user-intro\">{{ user.intro || '这个人很懒，什么都没有留下' }}</div>\n        </div>\n        <div class=\"follow-button\">\n          <el-button type=\"primary\" @click=\"followUser\">关注</el-button>\n        </div>\n      </div>\n      \n      <!-- 用户统计 -->\n      <div class=\"user-stats\">\n        <div class=\"stat-item\">\n          <div class=\"stat-number\">{{ user.blogCount || 0 }}</div>\n          <div class=\"stat-label\">博客</div>\n        </div>\n        <div class=\"stat-item\">\n          <div class=\"stat-number\">{{ user.followCount || 0 }}</div>\n          <div class=\"stat-label\">关注</div>\n        </div>\n        <div class=\"stat-item\">\n          <div class=\"stat-number\">{{ user.fansCount || 0 }}</div>\n          <div class=\"stat-label\">粉丝</div>\n        </div>\n      </div>\n      \n      <!-- 用户博客列表 -->\n      <div class=\"user-blogs\">\n        <div class=\"section-title\">TA的博客</div>\n        <div v-if=\"blogs.length > 0\" class=\"blog-list\">\n          <div \n            v-for=\"blog in blogs\" \n            :key=\"blog.id\" \n            class=\"blog-item\" \n            @click=\"toBlogDetail(blog.id)\"\n          >\n            <div class=\"blog-image\">\n              <img :src=\"blog.img\" :alt=\"blog.title\">\n            </div>\n            <div class=\"blog-content\">\n              <div class=\"blog-title\">{{ blog.title }}</div>\n              <div class=\"blog-time\">{{ formatTime(blog.createTime) }}</div>\n            </div>\n          </div>\n        </div>\n        <div v-else class=\"no-blogs\">\n          暂无博客\n        </div>\n      </div>\n    </div>\n    \n    <!-- 加载状态 -->\n    <LoadingSpinner v-if=\"loading\" :full-screen=\"true\" text=\"加载中...\" />\n    \n    <!-- 错误状态 -->\n    <EmptyState \n      v-if=\"!loading && !user\" \n      text=\"用户不存在\"\n      icon=\"el-icon-warning\"\n      :show-action=\"true\"\n      action-text=\"返回\"\n      @action=\"goBack\"\n    />\n  </div>\n</template>\n\n<script>\nimport HeaderBar from '@/components/HeaderBar.vue'\nimport LoadingSpinner from '@/components/LoadingSpinner.vue'\nimport EmptyState from '@/components/EmptyState.vue'\nimport { userApi, blogApi } from '@/api'\n\nexport default {\n  name: 'OtherUserInfo',\n  components: {\n    HeaderBar,\n    LoadingSpinner,\n    EmptyState\n  },\n  data() {\n    return {\n      user: null,\n      blogs: [],\n      loading: false\n    }\n  },\n  created() {\n    this.loadUserInfo()\n    this.loadUserBlogs()\n  },\n  methods: {\n    // 加载用户信息\n    async loadUserInfo() {\n      const userId = this.$route.params.id\n      if (!userId) {\n        this.$message.error('用户ID不存在')\n        this.goBack()\n        return\n      }\n      \n      this.loading = true\n      try {\n        const response = await userApi.getUserById(userId)\n        this.user = response.data\n      } catch (error) {\n        console.error('加载用户信息失败:', error)\n        this.$message.error('加载用户信息失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 加载用户博客\n    async loadUserBlogs() {\n      const userId = this.$route.params.id\n      try {\n        const response = await blogApi.getBlogsByUserId({\n          id: userId,\n          current: 1\n        })\n        const blogs = response.data || []\n        blogs.forEach(blog => {\n          if (blog.images) {\n            blog.img = blog.images.split(',')[0]\n          }\n        })\n        this.blogs = blogs\n      } catch (error) {\n        console.error('加载用户博客失败:', error)\n      }\n    },\n    \n    // 返回上一页\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    // 关注用户\n    followUser() {\n      this.$message.info('关注功能开发中')\n    },\n    \n    // 跳转到博客详情\n    toBlogDetail(blogId) {\n      this.$router.push(`/blog-detail/${blogId}`)\n    },\n    \n    // 格式化时间\n    formatTime(time) {\n      if (!time) return ''\n      const date = new Date(time)\n      return date.toLocaleDateString()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.other-user-info-page {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.user-profile {\n  padding-top: 50px;\n}\n\n/* 用户头部 */\n.user-header {\n  background-color: #fff;\n  padding: 20px;\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.user-avatar {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  overflow: hidden;\n  margin-right: 15px;\n}\n\n.user-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.user-info {\n  flex: 1;\n}\n\n.user-name {\n  font-size: 18px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.user-intro {\n  font-size: 12px;\n  color: #666;\n  line-height: 1.4;\n}\n\n/* 用户统计 */\n.user-stats {\n  background-color: #fff;\n  padding: 20px;\n  display: flex;\n  justify-content: space-around;\n  margin-bottom: 10px;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-number {\n  font-size: 20px;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 5px;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n}\n\n/* 博客列表 */\n.user-blogs {\n  background-color: #fff;\n  padding: 20px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 15px;\n}\n\n.blog-item {\n  display: flex;\n  padding: 10px 0;\n  border-bottom: 1px solid #f0f0f0;\n  cursor: pointer;\n}\n\n.blog-item:last-child {\n  border-bottom: none;\n}\n\n.blog-image {\n  width: 60px;\n  height: 60px;\n  margin-right: 12px;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.blog-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.blog-content {\n  flex: 1;\n}\n\n.blog-title {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 5px;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n}\n\n.blog-time {\n  font-size: 12px;\n  color: #999;\n}\n\n.no-blogs {\n  text-align: center;\n  color: #999;\n  padding: 40px 0;\n}\n</style>\n"], "mappings": ";;;AAkFA,OAAAA,SAAA;AACA,OAAAC,cAAA;AACA,OAAAC,UAAA;AACA,SAAAC,OAAA,EAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAN,SAAA;IACAC,cAAA;IACAC;EACA;EACAK,KAAA;IACA;MACAC,IAAA;MACAC,KAAA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACA;IACA,MAAAF,aAAA;MACA,MAAAG,MAAA,QAAAC,MAAA,CAAAC,MAAA,CAAAC,EAAA;MACA,KAAAH,MAAA;QACA,KAAAI,QAAA,CAAAC,KAAA;QACA,KAAAC,MAAA;QACA;MACA;MAEA,KAAAX,OAAA;MACA;QACA,MAAAY,QAAA,SAAAnB,OAAA,CAAAoB,WAAA,CAAAR,MAAA;QACA,KAAAP,IAAA,GAAAc,QAAA,CAAAf,IAAA;MACA,SAAAa,KAAA;QACAI,OAAA,CAAAJ,KAAA,cAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;QACA,KAAAV,OAAA;MACA;IACA;IAEA;IACA,MAAAG,cAAA;MACA,MAAAE,MAAA,QAAAC,MAAA,CAAAC,MAAA,CAAAC,EAAA;MACA;QACA,MAAAI,QAAA,SAAAlB,OAAA,CAAAqB,gBAAA;UACAP,EAAA,EAAAH,MAAA;UACAW,OAAA;QACA;QACA,MAAAjB,KAAA,GAAAa,QAAA,CAAAf,IAAA;QACAE,KAAA,CAAAkB,OAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,MAAA;YACAD,IAAA,CAAAE,GAAA,GAAAF,IAAA,CAAAC,MAAA,CAAAE,KAAA;UACA;QACA;QACA,KAAAtB,KAAA,GAAAA,KAAA;MACA,SAAAW,KAAA;QACAI,OAAA,CAAAJ,KAAA,cAAAA,KAAA;MACA;IACA;IAEA;IACAC,OAAA;MACA,KAAAW,OAAA,CAAAC,EAAA;IACA;IAEA;IACAC,WAAA;MACA,KAAAf,QAAA,CAAAgB,IAAA;IACA;IAEA;IACAC,aAAAC,MAAA;MACA,KAAAL,OAAA,CAAAM,IAAA,iBAAAD,MAAA;IACA;IAEA;IACAE,WAAAC,IAAA;MACA,KAAAA,IAAA;MACA,MAAAC,IAAA,OAAAC,IAAA,CAAAF,IAAA;MACA,OAAAC,IAAA,CAAAE,kBAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}