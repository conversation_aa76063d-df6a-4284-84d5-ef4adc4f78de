{"ast": null, "code": "import HeaderBar from '@/components/HeaderBar.vue';\nimport { userApi } from '@/api';\nexport default {\n  name: 'UserInfoEdit',\n  components: {\n    HeaderBar\n  },\n  data() {\n    return {\n      form: {\n        icon: '',\n        nickName: '',\n        gender: 1,\n        birthday: '',\n        intro: ''\n      },\n      saving: false\n    };\n  },\n  created() {\n    this.loadUserInfo();\n  },\n  methods: {\n    // 加载用户信息\n    async loadUserInfo() {\n      try {\n        const response = await userApi.getCurrentUser();\n        const user = response.data;\n        this.form = {\n          icon: user.icon || '',\n          nickName: user.nickName || '',\n          gender: user.gender || 1,\n          birthday: user.birthday || '',\n          intro: user.intro || ''\n        };\n      } catch (error) {\n        console.error('加载用户信息失败:', error);\n        this.$message.error('加载用户信息失败');\n      }\n    },\n    // 头像上传成功\n    handleAvatarSuccess(response) {\n      if (response.success) {\n        this.form.icon = response.data;\n        this.$message.success('头像上传成功');\n      } else {\n        this.$message.error('头像上传失败');\n      }\n    },\n    // 保存用户信息\n    async saveUserInfo() {\n      if (!this.validateForm()) {\n        return;\n      }\n      this.saving = true;\n      try {\n        // 这里应该调用更新用户信息的API\n        // await userApi.updateUserInfo(this.form)\n        this.$message.success('保存成功');\n        this.$router.go(-1);\n      } catch (error) {\n        console.error('保存失败:', error);\n        this.$message.error('保存失败');\n      } finally {\n        this.saving = false;\n      }\n    },\n    // 表单验证\n    validateForm() {\n      if (!this.form.nickName.trim()) {\n        this.$message.error('请输入昵称');\n        return false;\n      }\n      return true;\n    },\n    // 返回上一页\n    goBack() {\n      this.$router.go(-1);\n    }\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "userApi", "name", "components", "data", "form", "icon", "nick<PERSON><PERSON>", "gender", "birthday", "intro", "saving", "created", "loadUserInfo", "methods", "response", "getCurrentUser", "user", "error", "console", "$message", "handleAvatarSuccess", "success", "saveUserInfo", "validateForm", "$router", "go", "trim", "goBack"], "sources": ["src/views/UserInfoEdit.vue"], "sourcesContent": ["<template>\n  <div class=\"user-info-edit-page\">\n    <!-- 头部 -->\n    <HeaderBar \n      title=\"编辑资料\" \n      :show-back=\"true\" \n      @back=\"goBack\"\n    >\n      <template #actions>\n        <el-button \n          type=\"primary\" \n          size=\"small\" \n          :loading=\"saving\"\n          @click=\"saveUserInfo\"\n        >\n          保存\n        </el-button>\n      </template>\n    </HeaderBar>\n    \n    <!-- 编辑表单 -->\n    <div class=\"edit-form\">\n      <!-- 头像 -->\n      <div class=\"form-item\">\n        <label>头像</label>\n        <div class=\"avatar-upload\">\n          <el-upload\n            action=\"/api/upload\"\n            :show-file-list=\"false\"\n            :on-success=\"handleAvatarSuccess\"\n            accept=\"image/*\"\n          >\n            <img v-if=\"form.icon\" :src=\"form.icon\" class=\"avatar\">\n            <i v-else class=\"el-icon-plus avatar-uploader-icon\"></i>\n          </el-upload>\n        </div>\n      </div>\n      \n      <!-- 昵称 -->\n      <div class=\"form-item\">\n        <label>昵称</label>\n        <el-input\n          v-model=\"form.nickName\"\n          placeholder=\"请输入昵称\"\n          maxlength=\"20\"\n        />\n      </div>\n      \n      <!-- 性别 -->\n      <div class=\"form-item\">\n        <label>性别</label>\n        <el-radio-group v-model=\"form.gender\">\n          <el-radio :label=\"1\">男</el-radio>\n          <el-radio :label=\"0\">女</el-radio>\n        </el-radio-group>\n      </div>\n      \n      <!-- 生日 -->\n      <div class=\"form-item\">\n        <label>生日</label>\n        <el-date-picker\n          v-model=\"form.birthday\"\n          type=\"date\"\n          placeholder=\"选择日期\"\n          format=\"yyyy-MM-dd\"\n          value-format=\"yyyy-MM-dd\"\n        />\n      </div>\n      \n      <!-- 个人介绍 -->\n      <div class=\"form-item\">\n        <label>个人介绍</label>\n        <el-input\n          v-model=\"form.intro\"\n          type=\"textarea\"\n          placeholder=\"介绍一下自己吧\"\n          :rows=\"4\"\n          maxlength=\"200\"\n          show-word-limit\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport HeaderBar from '@/components/HeaderBar.vue'\nimport { userApi } from '@/api'\n\nexport default {\n  name: 'UserInfoEdit',\n  components: {\n    HeaderBar\n  },\n  data() {\n    return {\n      form: {\n        icon: '',\n        nickName: '',\n        gender: 1,\n        birthday: '',\n        intro: ''\n      },\n      saving: false\n    }\n  },\n  created() {\n    this.loadUserInfo()\n  },\n  methods: {\n    // 加载用户信息\n    async loadUserInfo() {\n      try {\n        const response = await userApi.getCurrentUser()\n        const user = response.data\n        this.form = {\n          icon: user.icon || '',\n          nickName: user.nickName || '',\n          gender: user.gender || 1,\n          birthday: user.birthday || '',\n          intro: user.intro || ''\n        }\n      } catch (error) {\n        console.error('加载用户信息失败:', error)\n        this.$message.error('加载用户信息失败')\n      }\n    },\n    \n    // 头像上传成功\n    handleAvatarSuccess(response) {\n      if (response.success) {\n        this.form.icon = response.data\n        this.$message.success('头像上传成功')\n      } else {\n        this.$message.error('头像上传失败')\n      }\n    },\n    \n    // 保存用户信息\n    async saveUserInfo() {\n      if (!this.validateForm()) {\n        return\n      }\n      \n      this.saving = true\n      try {\n        // 这里应该调用更新用户信息的API\n        // await userApi.updateUserInfo(this.form)\n        this.$message.success('保存成功')\n        this.$router.go(-1)\n      } catch (error) {\n        console.error('保存失败:', error)\n        this.$message.error('保存失败')\n      } finally {\n        this.saving = false\n      }\n    },\n    \n    // 表单验证\n    validateForm() {\n      if (!this.form.nickName.trim()) {\n        this.$message.error('请输入昵称')\n        return false\n      }\n      \n      return true\n    },\n    \n    // 返回上一页\n    goBack() {\n      this.$router.go(-1)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.user-info-edit-page {\n  height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n}\n\n.edit-form {\n  flex: 1;\n  padding: 60px 15px 20px;\n  overflow-y: auto;\n}\n\n.form-item {\n  background-color: #fff;\n  padding: 15px;\n  margin-bottom: 10px;\n  border-radius: 8px;\n}\n\n.form-item label {\n  display: block;\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.avatar-upload {\n  display: flex;\n  justify-content: center;\n}\n\n.avatar {\n  width: 80px;\n  height: 80px;\n  border-radius: 50%;\n  object-fit: cover;\n}\n\n.avatar-uploader-icon {\n  font-size: 28px;\n  color: #8c939d;\n  width: 80px;\n  height: 80px;\n  line-height: 80px;\n  text-align: center;\n  border: 1px dashed #d9d9d9;\n  border-radius: 50%;\n}\n</style>\n"], "mappings": "AAsFA,OAAAA,SAAA;AACA,SAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAH;EACA;EACAI,KAAA;IACA;MACAC,IAAA;QACAC,IAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACAC,MAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,YAAA;EACA;EACAC,OAAA;IACA;IACA,MAAAD,aAAA;MACA;QACA,MAAAE,QAAA,SAAAd,OAAA,CAAAe,cAAA;QACA,MAAAC,IAAA,GAAAF,QAAA,CAAAX,IAAA;QACA,KAAAC,IAAA;UACAC,IAAA,EAAAW,IAAA,CAAAX,IAAA;UACAC,QAAA,EAAAU,IAAA,CAAAV,QAAA;UACAC,MAAA,EAAAS,IAAA,CAAAT,MAAA;UACAC,QAAA,EAAAQ,IAAA,CAAAR,QAAA;UACAC,KAAA,EAAAO,IAAA,CAAAP,KAAA;QACA;MACA,SAAAQ,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;IACA;IAEA;IACAG,oBAAAN,QAAA;MACA,IAAAA,QAAA,CAAAO,OAAA;QACA,KAAAjB,IAAA,CAAAC,IAAA,GAAAS,QAAA,CAAAX,IAAA;QACA,KAAAgB,QAAA,CAAAE,OAAA;MACA;QACA,KAAAF,QAAA,CAAAF,KAAA;MACA;IACA;IAEA;IACA,MAAAK,aAAA;MACA,UAAAC,YAAA;QACA;MACA;MAEA,KAAAb,MAAA;MACA;QACA;QACA;QACA,KAAAS,QAAA,CAAAE,OAAA;QACA,KAAAG,OAAA,CAAAC,EAAA;MACA,SAAAR,KAAA;QACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;QACA,KAAAP,MAAA;MACA;IACA;IAEA;IACAa,aAAA;MACA,UAAAnB,IAAA,CAAAE,QAAA,CAAAoB,IAAA;QACA,KAAAP,QAAA,CAAAF,KAAA;QACA;MACA;MAEA;IACA;IAEA;IACAU,OAAA;MACA,KAAAH,OAAA,CAAAC,EAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}