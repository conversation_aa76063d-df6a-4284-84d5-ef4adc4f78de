package com.hmdp.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.UUID;

/**
 * 博客图片上传工具类
 * 提供本地文件上传功能，支持自动目录创建和文件名生成
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@AllArgsConstructor
@Slf4j
public class BlogUploadUtil {

    /**
     * 图片上传根目录
     */
    private String uploadDir;
    
    /**
     * 图片访问URL前缀
     */
    private String urlPrefix;
    
    /**
     * 允许的图片文件扩展名
     */
    private static final String[] ALLOWED_EXTENSIONS = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
    
    /**
     * 最大文件大小（5MB）
     */
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024;

    /**
     * 上传博客图片
     * 
     * @param file 上传的文件
     * @return 返回图片的访问路径
     * @throws RuntimeException 当文件上传失败时抛出异常
     */
    public String uploadBlogImage(MultipartFile file) {
        try {
            // 1. 验证文件
            validateFile(file);
            
            // 2. 获取原始文件名
            String originalFilename = file.getOriginalFilename();
            log.info("开始上传文件: {}", originalFilename);
            
            // 3. 生成新的文件名和路径
            String relativePath = generateFileName(originalFilename);
            
            // 4. 创建完整的文件路径
            File targetFile = new File(uploadDir, relativePath);
            
            // 5. 确保目录存在
            File parentDir = targetFile.getParentFile();
            if (!parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                if (!created) {
                    throw new RuntimeException("创建目录失败: " + parentDir.getAbsolutePath());
                }
                log.info("创建目录: {}", parentDir.getAbsolutePath());
            }
            
            // 6. 保存文件
            file.transferTo(targetFile);
            
            // 7. 构建访问URL
            String accessUrl = buildAccessUrl(relativePath);
            
            log.info("文件上传成功: {} -> {}", originalFilename, accessUrl);
            return accessUrl;
            
        } catch (IOException e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("文件上传过程中发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除博客图片
     * 
     * @param filename 要删除的文件名（相对路径）
     * @return 删除是否成功
     */
    public boolean deleteBlogImage(String filename) {
        try {
            // 验证文件名，防止路径遍历攻击
            if (StrUtil.isBlank(filename) || filename.contains("..")) {
                log.warn("非法的文件名: {}", filename);
                return false;
            }
            
            File file = new File(uploadDir, filename);
            
            // 检查文件是否存在且不是目录
            if (!file.exists()) {
                log.warn("文件不存在: {}", file.getAbsolutePath());
                return false;
            }
            
            if (file.isDirectory()) {
                log.warn("不能删除目录: {}", file.getAbsolutePath());
                return false;
            }
            
            // 删除文件
            boolean deleted = FileUtil.del(file);
            if (deleted) {
                log.info("文件删除成功: {}", file.getAbsolutePath());
            } else {
                log.warn("文件删除失败: {}", file.getAbsolutePath());
            }
            
            return deleted;
            
        } catch (Exception e) {
            log.error("删除文件时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 验证上传的文件
     * 
     * @param file 要验证的文件
     * @throws RuntimeException 当文件不符合要求时抛出异常
     */
    private void validateFile(MultipartFile file) {
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("上传文件不能为空");
        }
        
        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new RuntimeException("文件大小不能超过5MB");
        }
        
        // 检查文件名
        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            throw new RuntimeException("文件名不能为空");
        }
        
        // 检查文件扩展名
        String extension = StrUtil.subAfter(originalFilename, ".", true);
        if (StrUtil.isBlank(extension)) {
            throw new RuntimeException("文件必须有扩展名");
        }
        
        boolean isAllowed = false;
        for (String allowedExt : ALLOWED_EXTENSIONS) {
            if (allowedExt.equalsIgnoreCase(extension)) {
                isAllowed = true;
                break;
            }
        }
        
        if (!isAllowed) {
            throw new RuntimeException("不支持的文件类型: " + extension + 
                "，支持的类型: " + String.join(", ", ALLOWED_EXTENSIONS));
        }
    }

    /**
     * 生成新的文件名
     * 使用UUID和哈希值创建分层目录结构，避免单个目录文件过多
     * 
     * @param originalFilename 原始文件名
     * @return 生成的相对路径
     */
    private String generateFileName(String originalFilename) {
        // 获取文件扩展名
        String extension = StrUtil.subAfter(originalFilename, ".", true);
        
        // 生成UUID作为文件名
        String uuid = UUID.randomUUID().toString();
        
        // 使用哈希值创建两级目录
        int hash = uuid.hashCode();
        int dir1 = hash & 0xF;        // 取低4位，范围0-15
        int dir2 = (hash >> 4) & 0xF; // 取第5-8位，范围0-15
        
        // 构建相对路径：/blogs/dir1/dir2/uuid.extension
        return StrUtil.format("/blogs/{}/{}/{}.{}", dir1, dir2, uuid, extension);
    }

    /**
     * 构建文件访问URL
     * 
     * @param relativePath 文件相对路径
     * @return 完整的访问URL
     */
    private String buildAccessUrl(String relativePath) {
        if (StrUtil.isBlank(urlPrefix)) {
            // 如果没有配置URL前缀，直接返回相对路径
            return relativePath;
        }
        
        // 确保URL前缀以/结尾，相对路径以/开头
        String prefix = urlPrefix.endsWith("/") ? urlPrefix : urlPrefix + "/";
        String path = relativePath.startsWith("/") ? relativePath.substring(1) : relativePath;
        
        return prefix + path;
    }
}
