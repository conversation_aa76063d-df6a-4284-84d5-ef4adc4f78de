{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"user-info-page\"\n  }, [_c(\"HeaderBar\", {\n    attrs: {\n      title: \"个人信息\",\n      \"show-back\": true\n    },\n    on: {\n      back: _vm.goBack\n    }\n  }), _vm.user ? _c(\"div\", {\n    staticClass: \"user-profile\"\n  }, [_c(\"div\", {\n    staticClass: \"user-header\"\n  }, [_c(\"div\", {\n    staticClass: \"user-avatar\"\n  }, [_c(\"img\", {\n    attrs: {\n      src: _vm.user.icon || \"/imgs/icons/default-icon.png\",\n      alt: _vm.user.nickName\n    }\n  })]), _c(\"div\", {\n    staticClass: \"user-info\"\n  }, [_c(\"div\", {\n    staticClass: \"user-name\"\n  }, [_vm._v(_vm._s(_vm.user.nickName || \"未设置昵称\"))]), _c(\"div\", {\n    staticClass: \"user-phone\"\n  }, [_vm._v(_vm._s(_vm.formatPhone(_vm.user.phone)))])]), _c(\"div\", {\n    staticClass: \"edit-button\"\n  }, [_c(\"el-button\", {\n    attrs: {\n      type: \"text\"\n    },\n    on: {\n      click: _vm.toEditInfo\n    }\n  }, [_vm._v(\"编辑\")])], 1)]), _c(\"div\", {\n    staticClass: \"menu-list\"\n  }, [_c(\"div\", {\n    staticClass: \"menu-item\",\n    on: {\n      click: _vm.toMyBlogs\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-document\"\n  }), _c(\"span\", [_vm._v(\"我的博客\")]), _c(\"i\", {\n    staticClass: \"el-icon-arrow-right\"\n  })]), _c(\"div\", {\n    staticClass: \"menu-item\",\n    on: {\n      click: _vm.toMyCollections\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-star-off\"\n  }), _c(\"span\", [_vm._v(\"我的收藏\")]), _c(\"i\", {\n    staticClass: \"el-icon-arrow-right\"\n  })]), _c(\"div\", {\n    staticClass: \"menu-item\",\n    on: {\n      click: _vm.toMyFollows\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-user\"\n  }), _c(\"span\", [_vm._v(\"我的关注\")]), _c(\"i\", {\n    staticClass: \"el-icon-arrow-right\"\n  })]), _c(\"div\", {\n    staticClass: \"menu-item\",\n    on: {\n      click: _vm.signIn\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-calendar\"\n  }), _c(\"span\", [_vm._v(\"签到\")]), _c(\"span\", {\n    staticClass: \"sign-count\"\n  }, [_vm._v(\"已连续签到\" + _vm._s(_vm.signCount) + \"天\")])])]), _c(\"div\", {\n    staticClass: \"menu-list\"\n  }, [_c(\"div\", {\n    staticClass: \"menu-item\",\n    on: {\n      click: _vm.showSettings\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-setting\"\n  }), _c(\"span\", [_vm._v(\"设置\")]), _c(\"i\", {\n    staticClass: \"el-icon-arrow-right\"\n  })]), _c(\"div\", {\n    staticClass: \"menu-item\",\n    on: {\n      click: _vm.showAbout\n    }\n  }, [_c(\"i\", {\n    staticClass: \"el-icon-info\"\n  }), _c(\"span\", [_vm._v(\"关于我们\")]), _c(\"i\", {\n    staticClass: \"el-icon-arrow-right\"\n  })])]), _c(\"div\", {\n    staticClass: \"logout-section\"\n  }, [_c(\"el-button\", {\n    attrs: {\n      type: \"danger\",\n      plain: \"\"\n    },\n    on: {\n      click: _vm.handleLogout\n    }\n  }, [_vm._v(\"退出登录\")])], 1)]) : _vm._e(), _c(\"FooterBar\", {\n    attrs: {\n      \"active-btn\": 4\n    }\n  }), _vm.loading ? _c(\"LoadingSpinner\", {\n    attrs: {\n      \"full-screen\": true,\n      text: \"加载中...\"\n    }\n  }) : _vm._e()], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "title", "on", "back", "goBack", "user", "src", "icon", "alt", "nick<PERSON><PERSON>", "_v", "_s", "formatPhone", "phone", "type", "click", "toEditInfo", "toMyBlogs", "toMyCollections", "toMyFollows", "signIn", "signCount", "showSettings", "showAbout", "plain", "handleLogout", "_e", "loading", "text", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/src/views/UserInfo.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"user-info-page\" },\n    [\n      _c(\"HeaderBar\", {\n        attrs: { title: \"个人信息\", \"show-back\": true },\n        on: { back: _vm.goBack },\n      }),\n      _vm.user\n        ? _c(\"div\", { staticClass: \"user-profile\" }, [\n            _c(\"div\", { staticClass: \"user-header\" }, [\n              _c(\"div\", { staticClass: \"user-avatar\" }, [\n                _c(\"img\", {\n                  attrs: {\n                    src: _vm.user.icon || \"/imgs/icons/default-icon.png\",\n                    alt: _vm.user.nickName,\n                  },\n                }),\n              ]),\n              _c(\"div\", { staticClass: \"user-info\" }, [\n                _c(\"div\", { staticClass: \"user-name\" }, [\n                  _vm._v(_vm._s(_vm.user.nickName || \"未设置昵称\")),\n                ]),\n                _c(\"div\", { staticClass: \"user-phone\" }, [\n                  _vm._v(_vm._s(_vm.formatPhone(_vm.user.phone))),\n                ]),\n              ]),\n              _c(\n                \"div\",\n                { staticClass: \"edit-button\" },\n                [\n                  _c(\n                    \"el-button\",\n                    { attrs: { type: \"text\" }, on: { click: _vm.toEditInfo } },\n                    [_vm._v(\"编辑\")]\n                  ),\n                ],\n                1\n              ),\n            ]),\n            _c(\"div\", { staticClass: \"menu-list\" }, [\n              _c(\n                \"div\",\n                { staticClass: \"menu-item\", on: { click: _vm.toMyBlogs } },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-document\" }),\n                  _c(\"span\", [_vm._v(\"我的博客\")]),\n                  _c(\"i\", { staticClass: \"el-icon-arrow-right\" }),\n                ]\n              ),\n              _c(\n                \"div\",\n                {\n                  staticClass: \"menu-item\",\n                  on: { click: _vm.toMyCollections },\n                },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-star-off\" }),\n                  _c(\"span\", [_vm._v(\"我的收藏\")]),\n                  _c(\"i\", { staticClass: \"el-icon-arrow-right\" }),\n                ]\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"menu-item\", on: { click: _vm.toMyFollows } },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-user\" }),\n                  _c(\"span\", [_vm._v(\"我的关注\")]),\n                  _c(\"i\", { staticClass: \"el-icon-arrow-right\" }),\n                ]\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"menu-item\", on: { click: _vm.signIn } },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-calendar\" }),\n                  _c(\"span\", [_vm._v(\"签到\")]),\n                  _c(\"span\", { staticClass: \"sign-count\" }, [\n                    _vm._v(\"已连续签到\" + _vm._s(_vm.signCount) + \"天\"),\n                  ]),\n                ]\n              ),\n            ]),\n            _c(\"div\", { staticClass: \"menu-list\" }, [\n              _c(\n                \"div\",\n                { staticClass: \"menu-item\", on: { click: _vm.showSettings } },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-setting\" }),\n                  _c(\"span\", [_vm._v(\"设置\")]),\n                  _c(\"i\", { staticClass: \"el-icon-arrow-right\" }),\n                ]\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"menu-item\", on: { click: _vm.showAbout } },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-info\" }),\n                  _c(\"span\", [_vm._v(\"关于我们\")]),\n                  _c(\"i\", { staticClass: \"el-icon-arrow-right\" }),\n                ]\n              ),\n            ]),\n            _c(\n              \"div\",\n              { staticClass: \"logout-section\" },\n              [\n                _c(\n                  \"el-button\",\n                  {\n                    attrs: { type: \"danger\", plain: \"\" },\n                    on: { click: _vm.handleLogout },\n                  },\n                  [_vm._v(\"退出登录\")]\n                ),\n              ],\n              1\n            ),\n          ])\n        : _vm._e(),\n      _c(\"FooterBar\", { attrs: { \"active-btn\": 4 } }),\n      _vm.loading\n        ? _c(\"LoadingSpinner\", {\n            attrs: { \"full-screen\": true, text: \"加载中...\" },\n          })\n        : _vm._e(),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEF,EAAE,CAAC,WAAW,EAAE;IACdG,KAAK,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAE,WAAW,EAAE;IAAK,CAAC;IAC3CC,EAAE,EAAE;MAAEC,IAAI,EAAEP,GAAG,CAACQ;IAAO;EACzB,CAAC,CAAC,EACFR,GAAG,CAACS,IAAI,GACJR,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IACRG,KAAK,EAAE;MACLM,GAAG,EAAEV,GAAG,CAACS,IAAI,CAACE,IAAI,IAAI,8BAA8B;MACpDC,GAAG,EAAEZ,GAAG,CAACS,IAAI,CAACI;IAChB;EACF,CAAC,CAAC,CACH,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCH,GAAG,CAACc,EAAE,CAACd,GAAG,CAACe,EAAE,CAACf,GAAG,CAACS,IAAI,CAACI,QAAQ,IAAI,OAAO,CAAC,CAAC,CAC7C,CAAC,EACFZ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCH,GAAG,CAACc,EAAE,CAACd,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgB,WAAW,CAAChB,GAAG,CAACS,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAChD,CAAC,CACH,CAAC,EACFhB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAc,CAAC,EAC9B,CACEF,EAAE,CACA,WAAW,EACX;IAAEG,KAAK,EAAE;MAAEc,IAAI,EAAE;IAAO,CAAC;IAAEZ,EAAE,EAAE;MAAEa,KAAK,EAAEnB,GAAG,CAACoB;IAAW;EAAE,CAAC,EAC1D,CAACpB,GAAG,CAACc,EAAE,CAAC,IAAI,CAAC,CACf,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,EACFb,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,WAAW;IAAEG,EAAE,EAAE;MAAEa,KAAK,EAAEnB,GAAG,CAACqB;IAAU;EAAE,CAAC,EAC1D,CACEpB,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,CAAC,EAC5CF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5Bb,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,CAAC,CAEnD,CAAC,EACDF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,WAAW;IACxBG,EAAE,EAAE;MAAEa,KAAK,EAAEnB,GAAG,CAACsB;IAAgB;EACnC,CAAC,EACD,CACErB,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,CAAC,EAC5CF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5Bb,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,CAAC,CAEnD,CAAC,EACDF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,WAAW;IAAEG,EAAE,EAAE;MAAEa,KAAK,EAAEnB,GAAG,CAACuB;IAAY;EAAE,CAAC,EAC5D,CACEtB,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,CAAC,EACxCF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5Bb,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,CAAC,CAEnD,CAAC,EACDF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,WAAW;IAAEG,EAAE,EAAE;MAAEa,KAAK,EAAEnB,GAAG,CAACwB;IAAO;EAAE,CAAC,EACvD,CACEvB,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,CAAC,EAC5CF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAC1Bb,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACxCH,GAAG,CAACc,EAAE,CAAC,OAAO,GAAGd,GAAG,CAACe,EAAE,CAACf,GAAG,CAACyB,SAAS,CAAC,GAAG,GAAG,CAAC,CAC9C,CAAC,CAEN,CAAC,CACF,CAAC,EACFxB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,WAAW;IAAEG,EAAE,EAAE;MAAEa,KAAK,EAAEnB,GAAG,CAAC0B;IAAa;EAAE,CAAC,EAC7D,CACEzB,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,CAAC,EAC3CF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACc,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAC1Bb,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,CAAC,CAEnD,CAAC,EACDF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE,WAAW;IAAEG,EAAE,EAAE;MAAEa,KAAK,EAAEnB,GAAG,CAAC2B;IAAU;EAAE,CAAC,EAC1D,CACE1B,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,CAAC,EACxCF,EAAE,CAAC,MAAM,EAAE,CAACD,GAAG,CAACc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC5Bb,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAsB,CAAC,CAAC,CAEnD,CAAC,CACF,CAAC,EACFF,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEF,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MAAEc,IAAI,EAAE,QAAQ;MAAEU,KAAK,EAAE;IAAG,CAAC;IACpCtB,EAAE,EAAE;MAAEa,KAAK,EAAEnB,GAAG,CAAC6B;IAAa;EAChC,CAAC,EACD,CAAC7B,GAAG,CAACc,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,CACF,EACD,CACF,CAAC,CACF,CAAC,GACFd,GAAG,CAAC8B,EAAE,CAAC,CAAC,EACZ7B,EAAE,CAAC,WAAW,EAAE;IAAEG,KAAK,EAAE;MAAE,YAAY,EAAE;IAAE;EAAE,CAAC,CAAC,EAC/CJ,GAAG,CAAC+B,OAAO,GACP9B,EAAE,CAAC,gBAAgB,EAAE;IACnBG,KAAK,EAAE;MAAE,aAAa,EAAE,IAAI;MAAE4B,IAAI,EAAE;IAAS;EAC/C,CAAC,CAAC,GACFhC,GAAG,CAAC8B,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIG,eAAe,GAAG,EAAE;AACxBlC,MAAM,CAACmC,aAAa,GAAG,IAAI;AAE3B,SAASnC,MAAM,EAAEkC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}