<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="09e2ef6a-b6ec-46b4-8399-c008b428007d" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/backend/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/backend/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/hmdp/service/impl/BlogServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/hmdp/service/impl/BlogServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/hmdp/service/impl/ShopServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/hmdp/service/impl/ShopServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/hmdp/service/impl/ShopTypeServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/hmdp/service/impl/ShopTypeServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/hmdp/service/impl/VoucherOrderServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/hmdp/service/impl/VoucherOrderServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/HmDianPingApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/HmDianPingApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/config/MVCConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/config/MVCConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/config/MybatisConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/config/MybatisConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/config/RedisConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/config/RedisConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/config/RedissonConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/config/RedissonConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/config/WebExceptionAdvice.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/config/WebExceptionAdvice.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/BlogCommentsController.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/BlogCommentsController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/BlogController.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/BlogController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/FollowController.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/FollowController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/ShopController.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/ShopController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/ShopTypeController.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/ShopTypeController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/UploadController.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/UploadController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/UserController.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/UserController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/VoucherController.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/VoucherController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/VoucherOrderController.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/controller/VoucherOrderController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/dto/LoginFormDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/dto/LoginFormDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/dto/Result.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/dto/Result.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/dto/ScrollResult.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/dto/ScrollResult.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/dto/UserDTO.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/dto/UserDTO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/Blog.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/Blog.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/BlogComments.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/BlogComments.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/Follow.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/Follow.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/SeckillVoucher.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/SeckillVoucher.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/Shop.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/Shop.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/ShopType.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/ShopType.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/User.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/User.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/UserInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/UserInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/Voucher.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/Voucher.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/VoucherOrder.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/entity/VoucherOrder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/BlogCommentsMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/BlogCommentsMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/BlogMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/BlogMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/FollowMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/FollowMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/SeckillVoucherMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/SeckillVoucherMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/ShopMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/ShopMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/ShopTypeMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/ShopTypeMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/UserInfoMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/UserInfoMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/UserMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/UserMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/VoucherMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/VoucherMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/VoucherOrderMapper.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/mapper/VoucherOrderMapper.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IBlogCommentsService.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IBlogCommentsService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IBlogService.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IBlogService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IFollowService.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IFollowService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/ILock.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/ILock.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/ISeckillVoucherService.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/ISeckillVoucherService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IShopService.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IShopService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IShopTypeService.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IShopTypeService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IUserInfoService.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IUserInfoService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IUserService.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IUserService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IVoucherOrderService.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IVoucherOrderService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IVoucherService.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/IVoucherService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/BlogCommentsServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/BlogCommentsServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/BlogServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/BlogServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/FollowServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/FollowServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/SeckillVoucherServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/SeckillVoucherServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/ShopServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/ShopServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/ShopTypeServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/ShopTypeServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/UserInfoServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/UserInfoServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/UserServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/UserServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/VoucherOrderServiceImpl$VoucherOrderHandle.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/VoucherOrderServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/VoucherOrderServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/VoucherServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/service/impl/VoucherServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/CacheClient.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/CacheClient.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/LoginInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/LoginInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/PasswordEncoder.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/PasswordEncoder.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/RedisConstants.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/RedisConstants.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/RedisData.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/RedisData.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/RedisIdWork.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/RedisIdWork.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/RefreshTokenInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/RefreshTokenInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/RegexPatterns.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/RegexPatterns.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/RegexUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/RegexUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/SimpleRedisLock.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/SimpleRedisLock.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/SystemConstants.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/SystemConstants.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/UserHolder.class" beforeDir="false" afterPath="$PROJECT_DIR$/backend/target/classes/com/hmdp/utils/UserHolder.class" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:/Java/Maven/apache-maven-3.8.8" />
        <option name="localRepository" value="D:\Java\Maven\apache-maven-3.8.8\maven-repo" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\Java\Maven\apache-maven-3.8.8\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 5
}]]></component>
  <component name="ProjectId" id="30WgNWj1ByVoJ8Prf61HCY5H6TH" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
    <option name="showMembers" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.HmDianPingApplication.executor": "Run",
    "kotlin-language-version-configured": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="HmDianPingApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="hm-dianping" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.hmdp.HmDianPingApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="09e2ef6a-b6ec-46b4-8399-c008b428007d" name="Changes" comment="" />
      <created>1753748705250</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753748705250</updated>
      <workItem from="1753748708113" duration="798000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>