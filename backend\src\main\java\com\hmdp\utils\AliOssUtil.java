package com.hmdp.utils;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;

/**
 * 阿里云OSS文件上传工具类
 * 提供文件上传和删除功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Data
@AllArgsConstructor
@Slf4j
public class AliOssUtil {

    private String endpoint;
    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;

    /**
     * 文件上传到阿里云OSS
     *
     * @param bytes 文件字节数组
     * @param objectName OSS中的对象名称（文件路径）
     * @return 文件访问URL
     * @throws RuntimeException 当上传失败时抛出异常
     */
    public String upload(byte[] bytes, String objectName) {
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            // 创建PutObject请求，上传文件
            ossClient.putObject(bucketName, objectName, new ByteArrayInputStream(bytes));
            
            // 构建文件访问URL
            String fileUrl = buildFileUrl(objectName);
            log.info("文件上传到OSS成功: {}", fileUrl);
            return fileUrl;
            
        } catch (OSSException oe) {
            log.error("OSS服务异常: ErrorCode={}, ErrorMessage={}, RequestId={}", 
                oe.getErrorCode(), oe.getErrorMessage(), oe.getRequestId());
            throw new RuntimeException("文件上传失败: " + oe.getErrorMessage(), oe);
        } catch (ClientException ce) {
            log.error("OSS客户端异常: {}", ce.getMessage());
            throw new RuntimeException("文件上传失败: " + ce.getMessage(), ce);
        } catch (Exception e) {
            log.error("文件上传过程中发生未知错误: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        } finally {
            // 关闭OSSClient
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 从阿里云OSS删除文件
     *
     * @param objectName OSS中的对象名称（文件路径）
     * @return 删除是否成功
     */
    public boolean delete(String objectName) {
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            // 检查文件是否存在
            if (!ossClient.doesObjectExist(bucketName, objectName)) {
                log.warn("要删除的文件不存在: {}", objectName);
                return false;
            }

            // 删除文件
            ossClient.deleteObject(bucketName, objectName);
            log.info("文件从OSS删除成功: {}", objectName);
            return true;
            
        } catch (OSSException oe) {
            log.error("OSS服务异常: ErrorCode={}, ErrorMessage={}, RequestId={}", 
                oe.getErrorCode(), oe.getErrorMessage(), oe.getRequestId());
            return false;
        } catch (ClientException ce) {
            log.error("OSS客户端异常: {}", ce.getMessage());
            return false;
        } catch (Exception e) {
            log.error("文件删除过程中发生未知错误: {}", e.getMessage(), e);
            return false;
        } finally {
            // 关闭OSSClient
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 构建文件访问URL
     * 文件访问路径规则: https://BucketName.Endpoint/ObjectName
     *
     * @param objectName OSS中的对象名称
     * @return 完整的文件访问URL
     */
    private String buildFileUrl(String objectName) {
        StringBuilder stringBuilder = new StringBuilder("https://");
        stringBuilder
                .append(bucketName)
                .append(".")
                .append(endpoint)
                .append("/")
                .append(objectName);

        return stringBuilder.toString();
    }
}
