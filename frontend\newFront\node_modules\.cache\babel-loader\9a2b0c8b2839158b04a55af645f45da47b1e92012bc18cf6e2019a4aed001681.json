{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  name: 'SearchBar',\n  props: {\n    placeholder: {\n      type: String,\n      default: '请输入商户名、地点'\n    },\n    value: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      searchKeyword: this.value,\n      currentCity: '杭州'\n    };\n  },\n  watch: {\n    value(newVal) {\n      this.searchKeyword = newVal;\n    }\n  },\n  methods: {\n    handleSearch() {\n      this.$emit('search', this.searchKeyword);\n    },\n    handleInput(value) {\n      this.$emit('input', value);\n      // 实时搜索（可选）\n      if (this.realTimeSearch) {\n        this.debounceSearch();\n      }\n    },\n    selectCity() {\n      this.$emit('city-select');\n      // 这里可以打开城市选择弹窗\n      this.$message.info('城市选择功能暂未开放');\n    },\n    toUserInfo() {\n      this.$emit('user-click');\n      this.$router.push('/info');\n    },\n    // 防抖搜索\n    debounceSearch() {\n      if (this.searchTimer) {\n        clearTimeout(this.searchTimer);\n      }\n      this.searchTimer = setTimeout(() => {\n        this.handleSearch();\n      }, 500);\n    }\n  },\n  beforeDestroy() {\n    if (this.searchTimer) {\n      clearTimeout(this.searchTimer);\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "placeholder", "type", "String", "default", "value", "data", "searchKeyword", "currentCity", "watch", "newVal", "methods", "handleSearch", "$emit", "handleInput", "realTimeSearch", "debounceSearch", "selectCity", "$message", "info", "toUserInfo", "$router", "push", "searchTimer", "clearTimeout", "setTimeout", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/components/SearchBar.vue"], "sourcesContent": ["<template>\n  <div class=\"search-bar\">\n    <!-- 城市选择 -->\n    <div class=\"city-selector\" @click=\"selectCity\">\n      <span>{{ currentCity }}</span>\n      <i class=\"el-icon-arrow-down\"></i>\n    </div>\n    \n    <!-- 搜索输入框 -->\n    <div class=\"search-input\">\n      <el-input\n        v-model=\"searchKeyword\"\n        size=\"mini\"\n        :placeholder=\"placeholder\"\n        @keyup.enter=\"handleSearch\"\n        @input=\"handleInput\"\n        clearable\n      >\n        <i slot=\"prefix\" class=\"el-input__icon el-icon-search\"></i>\n      </el-input>\n    </div>\n    \n    <!-- 用户头像 -->\n    <div class=\"user-avatar\" @click=\"toUserInfo\">\n      <i class=\"el-icon-user\"></i>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'SearchBar',\n  props: {\n    placeholder: {\n      type: String,\n      default: '请输入商户名、地点'\n    },\n    value: {\n      type: String,\n      default: ''\n    }\n  },\n  data() {\n    return {\n      searchKeyword: this.value,\n      currentCity: '杭州'\n    }\n  },\n  watch: {\n    value(newVal) {\n      this.searchKeyword = newVal\n    }\n  },\n  methods: {\n    handleSearch() {\n      this.$emit('search', this.searchKeyword)\n    },\n    \n    handleInput(value) {\n      this.$emit('input', value)\n      // 实时搜索（可选）\n      if (this.realTimeSearch) {\n        this.debounceSearch()\n      }\n    },\n    \n    selectCity() {\n      this.$emit('city-select')\n      // 这里可以打开城市选择弹窗\n      this.$message.info('城市选择功能暂未开放')\n    },\n    \n    toUserInfo() {\n      this.$emit('user-click')\n      this.$router.push('/info')\n    },\n    \n    // 防抖搜索\n    debounceSearch() {\n      if (this.searchTimer) {\n        clearTimeout(this.searchTimer)\n      }\n      this.searchTimer = setTimeout(() => {\n        this.handleSearch()\n      }, 500)\n    }\n  },\n  beforeDestroy() {\n    if (this.searchTimer) {\n      clearTimeout(this.searchTimer)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.search-bar {\n  display: flex;\n  align-items: center;\n  padding: 10px 15px;\n  background-color: #fff;\n  border-bottom: 1px solid #e8e8e8;\n  gap: 10px;\n}\n\n.city-selector {\n  display: flex;\n  align-items: center;\n  padding: 8px 12px;\n  background-color: #f5f5f5;\n  border-radius: 20px;\n  cursor: pointer;\n  white-space: nowrap;\n  transition: background-color 0.3s ease;\n}\n\n.city-selector:hover {\n  background-color: #e8e8e8;\n}\n\n.city-selector span {\n  font-size: 14px;\n  color: #333;\n  margin-right: 4px;\n}\n\n.city-selector i {\n  font-size: 12px;\n  color: #666;\n}\n\n.search-input {\n  flex: 1;\n  min-width: 0;\n}\n\n.search-input .el-input {\n  width: 100%;\n}\n\n.search-input .el-input__inner {\n  border-radius: 20px;\n  border: 1px solid #e8e8e8;\n  background-color: #f5f5f5;\n  padding-left: 35px;\n}\n\n.search-input .el-input__inner:focus {\n  border-color: #ff6633;\n  background-color: #fff;\n}\n\n.user-avatar {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #f5f5f5;\n  border-radius: 50%;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n}\n\n.user-avatar:hover {\n  background-color: #e8e8e8;\n}\n\n.user-avatar i {\n  font-size: 18px;\n  color: #666;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .search-bar {\n    padding: 8px 10px;\n    gap: 8px;\n  }\n  \n  .city-selector {\n    padding: 6px 10px;\n  }\n  \n  .city-selector span {\n    font-size: 12px;\n  }\n  \n  .user-avatar {\n    width: 32px;\n    height: 32px;\n  }\n  \n  .user-avatar i {\n    font-size: 16px;\n  }\n}\n</style>\n"], "mappings": ";AA8BA;EACAA,IAAA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAE,KAAA;IACA;MACAC,aAAA,OAAAF,KAAA;MACAG,WAAA;IACA;EACA;EACAC,KAAA;IACAJ,MAAAK,MAAA;MACA,KAAAH,aAAA,GAAAG,MAAA;IACA;EACA;EACAC,OAAA;IACAC,aAAA;MACA,KAAAC,KAAA,gBAAAN,aAAA;IACA;IAEAO,YAAAT,KAAA;MACA,KAAAQ,KAAA,UAAAR,KAAA;MACA;MACA,SAAAU,cAAA;QACA,KAAAC,cAAA;MACA;IACA;IAEAC,WAAA;MACA,KAAAJ,KAAA;MACA;MACA,KAAAK,QAAA,CAAAC,IAAA;IACA;IAEAC,WAAA;MACA,KAAAP,KAAA;MACA,KAAAQ,OAAA,CAAAC,IAAA;IACA;IAEA;IACAN,eAAA;MACA,SAAAO,WAAA;QACAC,YAAA,MAAAD,WAAA;MACA;MACA,KAAAA,WAAA,GAAAE,UAAA;QACA,KAAAb,YAAA;MACA;IACA;EACA;EACAc,cAAA;IACA,SAAAH,WAAA;MACAC,YAAA,MAAAD,WAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}