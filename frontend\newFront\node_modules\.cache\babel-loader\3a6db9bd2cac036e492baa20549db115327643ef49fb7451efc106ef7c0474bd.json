{"ast": null, "code": "/**\n * API统一导出文件\n */\n\nimport userApi from './user';\nimport shopApi from './shop';\nimport blogApi from './blog';\nexport { userApi, shopApi, blogApi };\n\n// 默认导出\nexport default {\n  user: userApi,\n  shop: shopApi,\n  blog: blogApi\n};", "map": {"version": 3, "names": ["userApi", "shopApi", "blogApi", "user", "shop", "blog"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/src/api/index.js"], "sourcesContent": ["/**\n * API统一导出文件\n */\n\nimport userApi from './user'\nimport shopApi from './shop'\nimport blogApi from './blog'\n\nexport {\n  userApi,\n  shopApi,\n  blogApi\n}\n\n// 默认导出\nexport default {\n  user: userApi,\n  shop: shopApi,\n  blog: blogApi\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,OAAOA,OAAO,MAAM,QAAQ;AAC5B,OAAOC,OAAO,MAAM,QAAQ;AAC5B,OAAOC,OAAO,MAAM,QAAQ;AAE5B,SACEF,OAAO,EACPC,OAAO,EACPC,OAAO;;AAGT;AACA,eAAe;EACbC,IAAI,EAAEH,OAAO;EACbI,IAAI,EAAEH,OAAO;EACbI,IAAI,EAAEH;AACR,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}