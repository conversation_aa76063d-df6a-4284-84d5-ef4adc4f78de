package com.hmdp.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 博客上传配置属性类
 * 用于从配置文件中读取博客图片上传相关的配置信息
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Component
@ConfigurationProperties(prefix = "hmdp.blog.upload")
@Data
public class BlogUploadProperties {

    /**
     * 图片上传的本地存储目录
     * 例如: C:/upload/images 或 /var/www/upload/images
     */
    private String uploadDir;

    /**
     * 图片访问的URL前缀
     * 例如: http://localhost:8080/images 或 https://cdn.example.com/images
     * 如果为空，则直接返回相对路径
     */
    private String urlPrefix;

    /**
     * 最大文件大小（字节）
     * 默认5MB
     */
    private Long maxFileSize = 5 * 1024 * 1024L;

    /**
     * 允许的文件扩展名
     * 默认支持常见的图片格式
     */
    private String[] allowedExtensions = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};

    /**
     * 是否启用文件上传功能
     * 默认启用
     */
    private Boolean enabled = true;
}
