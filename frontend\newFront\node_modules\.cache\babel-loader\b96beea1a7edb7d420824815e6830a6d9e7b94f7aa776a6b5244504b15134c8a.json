{"ast": null, "code": "import Vue from 'vue';\nimport VueRouter from 'vue-router';\nVue.use(VueRouter);\nconst routes = [{\n  path: '/',\n  name: 'Home',\n  component: () => import('@/views/Home.vue'),\n  meta: {\n    title: '首页'\n  }\n}, {\n  path: '/login',\n  name: 'Login',\n  component: () => import('@/views/Login.vue'),\n  meta: {\n    title: '登录'\n  }\n}, {\n  path: '/login2',\n  name: 'Login2',\n  component: () => import('@/views/Login2.vue'),\n  meta: {\n    title: '密码登录'\n  }\n}, {\n  path: '/shop-list',\n  name: 'ShopList',\n  component: () => import('@/views/ShopList.vue'),\n  meta: {\n    title: '商户列表'\n  }\n}, {\n  path: '/shop-detail/:id',\n  name: 'ShopDetail',\n  component: () => import('@/views/ShopDetail.vue'),\n  meta: {\n    title: '商户详情'\n  }\n}, {\n  path: '/blog-detail/:id',\n  name: 'BlogDetail',\n  component: () => import('@/views/BlogDetail.vue'),\n  meta: {\n    title: '博客详情'\n  }\n}, {\n  path: '/blog-edit',\n  name: 'BlogEdit',\n  component: () => import('@/views/BlogEdit.vue'),\n  meta: {\n    title: '发布博客'\n  }\n}, {\n  path: '/info',\n  name: 'UserInfo',\n  component: () => import('@/views/UserInfo.vue'),\n  meta: {\n    title: '个人信息'\n  }\n}, {\n  path: '/info-edit',\n  name: 'UserInfoEdit',\n  component: () => import('@/views/UserInfoEdit.vue'),\n  meta: {\n    title: '编辑信息'\n  }\n}, {\n  path: '/other-info/:id',\n  name: 'OtherUserInfo',\n  component: () => import('@/views/OtherUserInfo.vue'),\n  meta: {\n    title: '用户信息'\n  }\n}];\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n});\n\n// 路由守卫\nrouter.beforeEach((to, from, next) => {\n  // 设置页面标题\n  if (to.meta.title) {\n    document.title = to.meta.title + ' - 黑马点评';\n  }\n\n  // 检查登录状态\n  const token = sessionStorage.getItem('token');\n  const needAuth = ['UserInfo', 'UserInfoEdit', 'BlogEdit'];\n  if (needAuth.includes(to.name) && !token) {\n    next('/login');\n  } else {\n    next();\n  }\n});\nexport default router;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "use", "routes", "path", "name", "component", "meta", "title", "router", "mode", "base", "process", "env", "BASE_URL", "beforeEach", "to", "from", "next", "document", "token", "sessionStorage", "getItem", "needAuth", "includes"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport VueRouter from 'vue-router'\n\nVue.use(VueRouter)\n\nconst routes = [\n  {\n    path: '/',\n    name: 'Home',\n    component: () => import('@/views/Home.vue'),\n    meta: { title: '首页' }\n  },\n  {\n    path: '/login',\n    name: 'Login',\n    component: () => import('@/views/Login.vue'),\n    meta: { title: '登录' }\n  },\n  {\n    path: '/login2',\n    name: 'Login2',\n    component: () => import('@/views/Login2.vue'),\n    meta: { title: '密码登录' }\n  },\n  {\n    path: '/shop-list',\n    name: 'ShopList',\n    component: () => import('@/views/ShopList.vue'),\n    meta: { title: '商户列表' }\n  },\n  {\n    path: '/shop-detail/:id',\n    name: 'ShopDetail',\n    component: () => import('@/views/ShopDetail.vue'),\n    meta: { title: '商户详情' }\n  },\n  {\n    path: '/blog-detail/:id',\n    name: 'BlogDetail',\n    component: () => import('@/views/BlogDetail.vue'),\n    meta: { title: '博客详情' }\n  },\n  {\n    path: '/blog-edit',\n    name: 'BlogEdit',\n    component: () => import('@/views/BlogEdit.vue'),\n    meta: { title: '发布博客' }\n  },\n  {\n    path: '/info',\n    name: 'UserInfo',\n    component: () => import('@/views/UserInfo.vue'),\n    meta: { title: '个人信息' }\n  },\n  {\n    path: '/info-edit',\n    name: 'UserInfoEdit',\n    component: () => import('@/views/UserInfoEdit.vue'),\n    meta: { title: '编辑信息' }\n  },\n  {\n    path: '/other-info/:id',\n    name: 'OtherUserInfo',\n    component: () => import('@/views/OtherUserInfo.vue'),\n    meta: { title: '用户信息' }\n  }\n]\n\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n})\n\n// 路由守卫\nrouter.beforeEach((to, from, next) => {\n  // 设置页面标题\n  if (to.meta.title) {\n    document.title = to.meta.title + ' - 黑马点评'\n  }\n  \n  // 检查登录状态\n  const token = sessionStorage.getItem('token')\n  const needAuth = ['UserInfo', 'UserInfoEdit', 'BlogEdit']\n  \n  if (needAuth.includes(to.name) && !token) {\n    next('/login')\n  } else {\n    next()\n  }\n})\n\nexport default router\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAElCD,GAAG,CAACE,GAAG,CAACD,SAAS,CAAC;AAElB,MAAME,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kBAAkB,CAAC;EAC3CC,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAK;AACtB,CAAC,EACD;EACEJ,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC;EAC5CC,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAK;AACtB,CAAC,EACD;EACEJ,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC;EAC7CC,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAO;AACxB,CAAC,EACD;EACEJ,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC;EAC/CC,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAO;AACxB,CAAC,EACD;EACEJ,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC;EACjDC,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAO;AACxB,CAAC,EACD;EACEJ,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC;EACjDC,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAO;AACxB,CAAC,EACD;EACEJ,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC;EAC/CC,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAO;AACxB,CAAC,EACD;EACEJ,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC;EAC/CC,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAO;AACxB,CAAC,EACD;EACEJ,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC;EACnDC,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAO;AACxB,CAAC,EACD;EACEJ,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;EACpDC,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAO;AACxB,CAAC,CACF;AAED,MAAMC,MAAM,GAAG,IAAIR,SAAS,CAAC;EAC3BS,IAAI,EAAE,SAAS;EACfC,IAAI,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ;EAC1BX;AACF,CAAC,CAAC;;AAEF;AACAM,MAAM,CAACM,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC;EACA,IAAIF,EAAE,CAACT,IAAI,CAACC,KAAK,EAAE;IACjBW,QAAQ,CAACX,KAAK,GAAGQ,EAAE,CAACT,IAAI,CAACC,KAAK,GAAG,SAAS;EAC5C;;EAEA;EACA,MAAMY,KAAK,GAAGC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;EAC7C,MAAMC,QAAQ,GAAG,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC;EAEzD,IAAIA,QAAQ,CAACC,QAAQ,CAACR,EAAE,CAACX,IAAI,CAAC,IAAI,CAACe,KAAK,EAAE;IACxCF,IAAI,CAAC,QAAQ,CAAC;EAChB,CAAC,MAAM;IACLA,IAAI,CAAC,CAAC;EACR;AACF,CAAC,CAAC;AAEF,eAAeT,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}