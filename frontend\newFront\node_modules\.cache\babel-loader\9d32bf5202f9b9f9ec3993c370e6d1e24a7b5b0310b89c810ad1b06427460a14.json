{"ast": null, "code": "import _ElementUI2 from \"element-ui/lib/theme-chalk/index.css\";\nimport _ElementUI from \"element-ui/lib\";\nimport Vue from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport store from './store';\n\n// Element UI\n\nimport 'element-ui/lib/theme-chalk/index.css';\n\n// 全局样式\nimport './assets/css/main.css';\n\n// API配置\nimport './utils/request';\nVue.use(_ElementUI);\nVue.config.productionTip = false;\n\n// 全局工具函数\nVue.prototype.$util = {\n  // 获取URL参数\n  getUrlParam(name) {\n    let reg = new RegExp(\"(^|&)\" + name + \"=([^&]*)(&|$)\", \"i\");\n    let r = window.location.search.substr(1).match(reg);\n    if (r != null) {\n      return decodeURI(r[2]);\n    }\n    return \"\";\n  },\n  // 价格格式化\n  formatPrice(val) {\n    if (typeof val === 'string') {\n      if (isNaN(val)) {\n        return null;\n      }\n      const index = val.lastIndexOf(\".\");\n      let p = \"\";\n      if (index < 0) {\n        p = val + \"00\";\n      } else if (index === val.length - 2) {\n        p = val.replace(\".\", \"\") + \"0\";\n      } else {\n        p = val.replace(\".\", \"\");\n      }\n      return parseInt(p);\n    } else if (typeof val === 'number') {\n      if (!val) {\n        return null;\n      }\n      const s = val + '';\n      if (s.length === 0) {\n        return \"0.00\";\n      }\n      if (s.length === 1) {\n        return \"0.0\" + val;\n      }\n      if (s.length === 2) {\n        return \"0.\" + val;\n      }\n      const i = s.indexOf(\".\");\n      if (i < 0) {\n        return s.substring(0, s.length - 2) + \".\" + s.substring(s.length - 2);\n      }\n      const num = s.substring(0, i) + s.substring(i + 1);\n      if (i === 1) {\n        return \"0.0\" + num;\n      }\n      if (i === 2) {\n        return \"0.\" + num;\n      }\n      if (i > 2) {\n        return num.substring(0, i - 2) + \".\" + num.substring(i - 2);\n      }\n    }\n  }\n};\nnew Vue({\n  router,\n  store,\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "store", "use", "_ElementUI", "config", "productionTip", "prototype", "$util", "getUrlParam", "name", "reg", "RegExp", "r", "window", "location", "search", "substr", "match", "decodeURI", "formatPrice", "val", "isNaN", "index", "lastIndexOf", "p", "length", "replace", "parseInt", "s", "i", "indexOf", "substring", "num", "render", "h", "$mount"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/src/main.js"], "sourcesContent": ["import Vue from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport store from './store'\n\n// Element UI\nimport ElementUI from 'element-ui'\nimport 'element-ui/lib/theme-chalk/index.css'\n\n// 全局样式\nimport './assets/css/main.css'\n\n// API配置\nimport './utils/request'\n\nVue.use(ElementUI)\n\nVue.config.productionTip = false\n\n// 全局工具函数\nVue.prototype.$util = {\n  // 获取URL参数\n  getUrlParam(name) {\n    let reg = new RegExp(\"(^|&)\" + name + \"=([^&]*)(&|$)\", \"i\");\n    let r = window.location.search.substr(1).match(reg);\n    if (r != null) {\n      return decodeURI(r[2]);\n    }\n    return \"\";\n  },\n  \n  // 价格格式化\n  formatPrice(val) {\n    if (typeof val === 'string') {\n      if (isNaN(val)) {\n        return null;\n      }\n      const index = val.lastIndexOf(\".\");\n      let p = \"\";\n      if (index < 0) {\n        p = val + \"00\";\n      } else if (index === val.length - 2) {\n        p = val.replace(\".\", \"\") + \"0\";\n      } else {\n        p = val.replace(\".\", \"\")\n      }\n      return parseInt(p);\n    } else if (typeof val === 'number') {\n      if (!val) {\n        return null;\n      }\n      const s = val + '';\n      if (s.length === 0) {\n        return \"0.00\";\n      }\n      if (s.length === 1) {\n        return \"0.0\" + val;\n      }\n      if (s.length === 2) {\n        return \"0.\" + val;\n      }\n      const i = s.indexOf(\".\");\n      if (i < 0) {\n        return s.substring(0, s.length - 2) + \".\" + s.substring(s.length - 2)\n      }\n      const num = s.substring(0, i) + s.substring(i + 1);\n      if (i === 1) {\n        return \"0.0\" + num;\n      }\n      if (i === 2) {\n        return \"0.\" + num;\n      }\n      if (i > 2) {\n        return num.substring(0, i - 2) + \".\" + num.substring(i - 2)\n      }\n    }\n  }\n}\n\nnew Vue({\n  router,\n  store,\n  render: h => h(App)\n}).$mount('#app')\n"], "mappings": ";;AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;;AAE3B;;AAEA,OAAO,sCAAsC;;AAE7C;AACA,OAAO,uBAAuB;;AAE9B;AACA,OAAO,iBAAiB;AAExBH,GAAG,CAACI,GAAG,CAAAC,UAAU,CAAC;AAElBL,GAAG,CAACM,MAAM,CAACC,aAAa,GAAG,KAAK;;AAEhC;AACAP,GAAG,CAACQ,SAAS,CAACC,KAAK,GAAG;EACpB;EACAC,WAAWA,CAACC,IAAI,EAAE;IAChB,IAAIC,GAAG,GAAG,IAAIC,MAAM,CAAC,OAAO,GAAGF,IAAI,GAAG,eAAe,EAAE,GAAG,CAAC;IAC3D,IAAIG,CAAC,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,KAAK,CAACP,GAAG,CAAC;IACnD,IAAIE,CAAC,IAAI,IAAI,EAAE;MACb,OAAOM,SAAS,CAACN,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;IACA,OAAO,EAAE;EACX,CAAC;EAED;EACAO,WAAWA,CAACC,GAAG,EAAE;IACf,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAC3B,IAAIC,KAAK,CAACD,GAAG,CAAC,EAAE;QACd,OAAO,IAAI;MACb;MACA,MAAME,KAAK,GAAGF,GAAG,CAACG,WAAW,CAAC,GAAG,CAAC;MAClC,IAAIC,CAAC,GAAG,EAAE;MACV,IAAIF,KAAK,GAAG,CAAC,EAAE;QACbE,CAAC,GAAGJ,GAAG,GAAG,IAAI;MAChB,CAAC,MAAM,IAAIE,KAAK,KAAKF,GAAG,CAACK,MAAM,GAAG,CAAC,EAAE;QACnCD,CAAC,GAAGJ,GAAG,CAACM,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG;MAChC,CAAC,MAAM;QACLF,CAAC,GAAGJ,GAAG,CAACM,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;MAC1B;MACA,OAAOC,QAAQ,CAACH,CAAC,CAAC;IACpB,CAAC,MAAM,IAAI,OAAOJ,GAAG,KAAK,QAAQ,EAAE;MAClC,IAAI,CAACA,GAAG,EAAE;QACR,OAAO,IAAI;MACb;MACA,MAAMQ,CAAC,GAAGR,GAAG,GAAG,EAAE;MAClB,IAAIQ,CAAC,CAACH,MAAM,KAAK,CAAC,EAAE;QAClB,OAAO,MAAM;MACf;MACA,IAAIG,CAAC,CAACH,MAAM,KAAK,CAAC,EAAE;QAClB,OAAO,KAAK,GAAGL,GAAG;MACpB;MACA,IAAIQ,CAAC,CAACH,MAAM,KAAK,CAAC,EAAE;QAClB,OAAO,IAAI,GAAGL,GAAG;MACnB;MACA,MAAMS,CAAC,GAAGD,CAAC,CAACE,OAAO,CAAC,GAAG,CAAC;MACxB,IAAID,CAAC,GAAG,CAAC,EAAE;QACT,OAAOD,CAAC,CAACG,SAAS,CAAC,CAAC,EAAEH,CAAC,CAACH,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGG,CAAC,CAACG,SAAS,CAACH,CAAC,CAACH,MAAM,GAAG,CAAC,CAAC;MACvE;MACA,MAAMO,GAAG,GAAGJ,CAAC,CAACG,SAAS,CAAC,CAAC,EAAEF,CAAC,CAAC,GAAGD,CAAC,CAACG,SAAS,CAACF,CAAC,GAAG,CAAC,CAAC;MAClD,IAAIA,CAAC,KAAK,CAAC,EAAE;QACX,OAAO,KAAK,GAAGG,GAAG;MACpB;MACA,IAAIH,CAAC,KAAK,CAAC,EAAE;QACX,OAAO,IAAI,GAAGG,GAAG;MACnB;MACA,IAAIH,CAAC,GAAG,CAAC,EAAE;QACT,OAAOG,GAAG,CAACD,SAAS,CAAC,CAAC,EAAEF,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGG,GAAG,CAACD,SAAS,CAACF,CAAC,GAAG,CAAC,CAAC;MAC7D;IACF;EACF;AACF,CAAC;AAED,IAAI/B,GAAG,CAAC;EACNE,MAAM;EACNC,KAAK;EACLgC,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACnC,GAAG;AACpB,CAAC,CAAC,CAACoC,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}