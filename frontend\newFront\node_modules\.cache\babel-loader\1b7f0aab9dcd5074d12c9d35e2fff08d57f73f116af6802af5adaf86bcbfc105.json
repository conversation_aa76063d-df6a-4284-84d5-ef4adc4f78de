{"ast": null, "code": "'use strict';\n\nvar AxiosError = require('./AxiosError');\n\n/**\n * Resolve or reject a Promise based on response status.\n *\n * @param {Function} resolve A function that resolves the promise.\n * @param {Function} reject A function that rejects the promise.\n * @param {object} response The response.\n */\nmodule.exports = function settle(resolve, reject, response) {\n  var validateStatus = response.config.validateStatus;\n  if (!response.status || !validateStatus || validateStatus(response.status)) {\n    resolve(response);\n  } else {\n    reject(new AxiosError('Request failed with status code ' + response.status, [AxiosError.ERR_BAD_REQUEST, AxiosError.ERR_BAD_RESPONSE][Math.floor(response.status / 100) - 4], response.config, response.request, response));\n  }\n};", "map": {"version": 3, "names": ["AxiosError", "require", "module", "exports", "settle", "resolve", "reject", "response", "validateStatus", "config", "status", "ERR_BAD_REQUEST", "ERR_BAD_RESPONSE", "Math", "floor", "request"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/node_modules/axios/lib/core/settle.js"], "sourcesContent": ["'use strict';\n\nvar AxiosError = require('./AxiosError');\n\n/**\n * Resolve or reject a Promise based on response status.\n *\n * @param {Function} resolve A function that resolves the promise.\n * @param {Function} reject A function that rejects the promise.\n * @param {object} response The response.\n */\nmodule.exports = function settle(resolve, reject, response) {\n  var validateStatus = response.config.validateStatus;\n  if (!response.status || !validateStatus || validateStatus(response.status)) {\n    resolve(response);\n  } else {\n    reject(new AxiosError(\n      'Request failed with status code ' + response.status,\n      [AxiosError.ERR_BAD_REQUEST, AxiosError.ERR_BAD_RESPONSE][Math.floor(response.status / 100) - 4],\n      response.config,\n      response.request,\n      response\n    ));\n  }\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,cAAc,CAAC;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,MAAMA,CAACC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EAC1D,IAAIC,cAAc,GAAGD,QAAQ,CAACE,MAAM,CAACD,cAAc;EACnD,IAAI,CAACD,QAAQ,CAACG,MAAM,IAAI,CAACF,cAAc,IAAIA,cAAc,CAACD,QAAQ,CAACG,MAAM,CAAC,EAAE;IAC1EL,OAAO,CAACE,QAAQ,CAAC;EACnB,CAAC,MAAM;IACLD,MAAM,CAAC,IAAIN,UAAU,CACnB,kCAAkC,GAAGO,QAAQ,CAACG,MAAM,EACpD,CAACV,UAAU,CAACW,eAAe,EAAEX,UAAU,CAACY,gBAAgB,CAAC,CAACC,IAAI,CAACC,KAAK,CAACP,QAAQ,CAACG,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,EAChGH,QAAQ,CAACE,MAAM,EACfF,QAAQ,CAACQ,OAAO,EAChBR,QACF,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}