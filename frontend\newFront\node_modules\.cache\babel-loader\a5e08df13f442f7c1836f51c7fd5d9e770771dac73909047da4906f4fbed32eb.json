{"ast": null, "code": "export default {\n  name: 'LoadingSpinner',\n  props: {\n    text: {\n      type: String,\n      default: '加载中...'\n    },\n    fullScreen: {\n      type: Boolean,\n      default: false\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "text", "type", "String", "default", "fullScreen", "Boolean"], "sources": ["src/components/LoadingSpinner.vue"], "sourcesContent": ["<template>\n  <div class=\"loading-container\" :class=\"{ 'full-screen': fullScreen }\">\n    <div class=\"loading-spinner\">\n      <div class=\"spinner\"></div>\n      <div v-if=\"text\" class=\"loading-text\">{{ text }}</div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'LoadingSpinner',\n  props: {\n    text: {\n      type: String,\n      default: '加载中...'\n    },\n    fullScreen: {\n      type: Boolean,\n      default: false\n    }\n  }\n}\n</script>\n\n<style scoped>\n.loading-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.loading-container.full-screen {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(255, 255, 255, 0.9);\n  z-index: 9999;\n}\n\n.loading-spinner {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 10px;\n}\n\n.spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid #f3f3f3;\n  border-top: 3px solid #ff6633;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.loading-text {\n  font-size: 14px;\n  color: #666;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n</style>\n"], "mappings": "AAUA;EACAA,IAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,UAAA;MACAH,IAAA,EAAAI,OAAA;MACAF,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}