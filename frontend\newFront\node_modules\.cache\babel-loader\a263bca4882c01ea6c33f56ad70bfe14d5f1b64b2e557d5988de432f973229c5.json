{"ast": null, "code": "export default {\n  name: 'App'\n};", "map": {"version": 3, "names": ["name"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view/>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'App'\n}\n</script>\n\n<style>\n#app {\n  height: 100vh;\n  overflow: hidden;\n}\n</style>\n"], "mappings": "AAOA;EACAA,IAAA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}