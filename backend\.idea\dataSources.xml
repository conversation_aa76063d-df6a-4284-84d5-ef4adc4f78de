<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="hmdp@127.0.0.1" uuid="7497d3c1-e560-47f0-a3fd-e7551c26748c">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <imported>true</imported>
      <remarks>$PROJECT_DIR$/src/main/resources/application.yaml</remarks>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>*****************************************************************************************************</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.host.port" />
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
        <property name="com.intellij.clouds.kubernetes.db.container.port" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>