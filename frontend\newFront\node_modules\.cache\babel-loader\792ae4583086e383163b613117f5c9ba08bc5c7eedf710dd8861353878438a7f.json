{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport HeaderBar from '@/components/HeaderBar.vue';\nimport { userApi } from '@/api';\nexport default {\n  name: 'Login',\n  components: {\n    HeaderBar\n  },\n  data() {\n    return {\n      form: {\n        phone: '',\n        code: ''\n      },\n      agreed: false,\n      loginLoading: false,\n      codeDisabled: false,\n      countdown: 0,\n      codeBtnText: '发送验证码',\n      timer: null\n    };\n  },\n  computed: {\n    isPhoneValid() {\n      return /^1[3-9]\\d{9}$/.test(this.form.phone);\n    }\n  },\n  beforeDestroy() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n  },\n  methods: {\n    // 返回上一页\n    goBack() {\n      this.$router.go(-1);\n    },\n    // 验证手机号\n    validatePhone() {\n      // 只保留数字\n      this.form.phone = this.form.phone.replace(/\\D/g, '');\n    },\n    // 发送验证码\n    async sendCode() {\n      if (!this.isPhoneValid) {\n        this.$message.error('请输入正确的手机号');\n        return;\n      }\n      try {\n        await userApi.sendCode(this.form.phone);\n        this.$message.success('验证码发送成功');\n        this.startCountdown();\n      } catch (error) {\n        console.error('发送验证码失败:', error);\n        this.$message.error('发送验证码失败');\n      }\n    },\n    // 开始倒计时\n    startCountdown() {\n      this.countdown = 60;\n      this.codeDisabled = true;\n      this.codeBtnText = `${this.countdown}s`;\n      this.timer = setInterval(() => {\n        this.countdown--;\n        this.codeBtnText = `${this.countdown}s`;\n        if (this.countdown <= 0) {\n          this.stopCountdown();\n        }\n      }, 1000);\n    },\n    // 停止倒计时\n    stopCountdown() {\n      if (this.timer) {\n        clearInterval(this.timer);\n        this.timer = null;\n      }\n      this.countdown = 0;\n      this.codeDisabled = false;\n      this.codeBtnText = '发送验证码';\n    },\n    // 处理登录\n    async handleLogin() {\n      if (!this.validateForm()) {\n        return;\n      }\n      this.loginLoading = true;\n      try {\n        const response = await userApi.login(this.form);\n\n        // 保存用户信息和token\n        this.$store.dispatch('login', {\n          user: response.data,\n          token: response.data.token\n        });\n        this.$message.success('登录成功');\n\n        // 跳转到首页或返回之前的页面\n        const redirect = this.$route.query.redirect || '/';\n        this.$router.push(redirect);\n      } catch (error) {\n        console.error('登录失败:', error);\n        this.$message.error('登录失败，请检查手机号和验证码');\n      } finally {\n        this.loginLoading = false;\n      }\n    },\n    // 表单验证\n    validateForm() {\n      if (!this.isPhoneValid) {\n        this.$message.error('请输入正确的手机号');\n        return false;\n      }\n      if (!this.form.code || this.form.code.length !== 6) {\n        this.$message.error('请输入6位验证码');\n        return false;\n      }\n      if (!this.agreed) {\n        this.$message.error('请先同意用户协议');\n        return false;\n      }\n      return true;\n    },\n    // 显示协议\n    showAgreement(type) {\n      if (type === 'service') {\n        this.$message.info('用户服务协议');\n      } else if (type === 'privacy') {\n        this.$message.info('隐私政策');\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "userApi", "name", "components", "data", "form", "phone", "code", "agreed", "loginLoading", "codeDisabled", "countdown", "codeBtnText", "timer", "computed", "isPhoneValid", "test", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "goBack", "$router", "go", "validatePhone", "replace", "sendCode", "$message", "error", "success", "startCountdown", "console", "setInterval", "stopCountdown", "handleLogin", "validateForm", "response", "login", "$store", "dispatch", "user", "token", "redirect", "$route", "query", "push", "length", "showAgreement", "type", "info"], "sources": ["src/views/Login.vue"], "sourcesContent": ["<template>\n  <div class=\"login-page\">\n    <!-- 头部 -->\n    <HeaderBar \n      title=\"手机号码快捷登录\" \n      :show-back=\"true\" \n      @back=\"goBack\"\n    />\n    \n    <!-- 登录表单 -->\n    <div class=\"login-container\">\n      <div class=\"login-form\">\n        <!-- 手机号和验证码 -->\n        <div class=\"phone-code-row\">\n          <el-input\n            v-model=\"form.phone\"\n            placeholder=\"请输入手机号\"\n            class=\"phone-input\"\n            maxlength=\"11\"\n            @input=\"validatePhone\"\n          />\n          <el-button\n            type=\"success\"\n            class=\"code-button\"\n            :disabled=\"codeDisabled\"\n            @click=\"sendCode\"\n          >\n            {{ codeBtnText }}\n          </el-button>\n        </div>\n        \n        <!-- 验证码输入 -->\n        <el-input\n          v-model=\"form.code\"\n          placeholder=\"请输入验证码\"\n          class=\"code-input\"\n          maxlength=\"6\"\n        />\n        \n        <!-- 提示文字 -->\n        <div class=\"login-tip\">\n          未注册的手机号码验证后自动创建账户\n        </div>\n        \n        <!-- 登录按钮 -->\n        <el-button\n          type=\"primary\"\n          class=\"login-button\"\n          :loading=\"loginLoading\"\n          @click=\"handleLogin\"\n        >\n          登录\n        </el-button>\n        \n        <!-- 密码登录链接 -->\n        <div class=\"password-login-link\">\n          <router-link to=\"/login2\">密码登录</router-link>\n        </div>\n      </div>\n      \n      <!-- 用户协议 -->\n      <div class=\"agreement\">\n        <div class=\"agreement-checkbox\">\n          <el-checkbox v-model=\"agreed\">\n            我已阅读并同意\n            <a href=\"javascript:void(0)\" @click=\"showAgreement('service')\">\n              《黑马点评用户服务协议》\n            </a>、\n            <a href=\"javascript:void(0)\" @click=\"showAgreement('privacy')\">\n              《隐私政策》\n            </a>\n            等，接受免除或者限制责任、诉讼管辖约定等粗体标示条款\n          </el-checkbox>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport HeaderBar from '@/components/HeaderBar.vue'\nimport { userApi } from '@/api'\n\nexport default {\n  name: 'Login',\n  components: {\n    HeaderBar\n  },\n  data() {\n    return {\n      form: {\n        phone: '',\n        code: ''\n      },\n      agreed: false,\n      loginLoading: false,\n      codeDisabled: false,\n      countdown: 0,\n      codeBtnText: '发送验证码',\n      timer: null\n    }\n  },\n  computed: {\n    isPhoneValid() {\n      return /^1[3-9]\\d{9}$/.test(this.form.phone)\n    }\n  },\n  beforeDestroy() {\n    if (this.timer) {\n      clearInterval(this.timer)\n    }\n  },\n  methods: {\n    // 返回上一页\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    // 验证手机号\n    validatePhone() {\n      // 只保留数字\n      this.form.phone = this.form.phone.replace(/\\D/g, '')\n    },\n    \n    // 发送验证码\n    async sendCode() {\n      if (!this.isPhoneValid) {\n        this.$message.error('请输入正确的手机号')\n        return\n      }\n      \n      try {\n        await userApi.sendCode(this.form.phone)\n        this.$message.success('验证码发送成功')\n        this.startCountdown()\n      } catch (error) {\n        console.error('发送验证码失败:', error)\n        this.$message.error('发送验证码失败')\n      }\n    },\n    \n    // 开始倒计时\n    startCountdown() {\n      this.countdown = 60\n      this.codeDisabled = true\n      this.codeBtnText = `${this.countdown}s`\n      \n      this.timer = setInterval(() => {\n        this.countdown--\n        this.codeBtnText = `${this.countdown}s`\n        \n        if (this.countdown <= 0) {\n          this.stopCountdown()\n        }\n      }, 1000)\n    },\n    \n    // 停止倒计时\n    stopCountdown() {\n      if (this.timer) {\n        clearInterval(this.timer)\n        this.timer = null\n      }\n      this.countdown = 0\n      this.codeDisabled = false\n      this.codeBtnText = '发送验证码'\n    },\n    \n    // 处理登录\n    async handleLogin() {\n      if (!this.validateForm()) {\n        return\n      }\n      \n      this.loginLoading = true\n      try {\n        const response = await userApi.login(this.form)\n        \n        // 保存用户信息和token\n        this.$store.dispatch('login', {\n          user: response.data,\n          token: response.data.token\n        })\n        \n        this.$message.success('登录成功')\n        \n        // 跳转到首页或返回之前的页面\n        const redirect = this.$route.query.redirect || '/'\n        this.$router.push(redirect)\n        \n      } catch (error) {\n        console.error('登录失败:', error)\n        this.$message.error('登录失败，请检查手机号和验证码')\n      } finally {\n        this.loginLoading = false\n      }\n    },\n    \n    // 表单验证\n    validateForm() {\n      if (!this.isPhoneValid) {\n        this.$message.error('请输入正确的手机号')\n        return false\n      }\n      \n      if (!this.form.code || this.form.code.length !== 6) {\n        this.$message.error('请输入6位验证码')\n        return false\n      }\n      \n      if (!this.agreed) {\n        this.$message.error('请先同意用户协议')\n        return false\n      }\n      \n      return true\n    },\n    \n    // 显示协议\n    showAgreement(type) {\n      if (type === 'service') {\n        this.$message.info('用户服务协议')\n      } else if (type === 'privacy') {\n        this.$message.info('隐私政策')\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.login-page {\n  height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n}\n\n.login-container {\n  flex: 1;\n  padding: 30px 20px;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.login-form {\n  margin-top: 50px;\n}\n\n.phone-code-row {\n  display: flex;\n  gap: 10px;\n  margin-bottom: 15px;\n}\n\n.phone-input {\n  flex: 1;\n}\n\n.code-button {\n  width: 120px;\n  border-radius: 20px;\n}\n\n.code-input {\n  margin-bottom: 10px;\n}\n\n.code-input .el-input__inner,\n.phone-input .el-input__inner {\n  border-radius: 20px;\n  height: 45px;\n  font-size: 16px;\n}\n\n.login-tip {\n  text-align: center;\n  color: #8c939d;\n  font-size: 12px;\n  margin: 10px 0 20px;\n}\n\n.login-button {\n  width: 100%;\n  height: 45px;\n  background-color: #ff6633;\n  border-color: #ff6633;\n  border-radius: 20px;\n  font-size: 16px;\n  margin-bottom: 15px;\n}\n\n.login-button:hover {\n  background-color: #e55a2b;\n  border-color: #e55a2b;\n}\n\n.password-login-link {\n  text-align: right;\n  margin-bottom: 30px;\n}\n\n.password-login-link a {\n  color: #333;\n  text-decoration: none;\n  font-size: 14px;\n}\n\n.password-login-link a:hover {\n  color: #ff6633;\n}\n\n.agreement {\n  margin-top: auto;\n}\n\n.agreement-checkbox {\n  font-size: 12px;\n  line-height: 1.5;\n}\n\n.agreement-checkbox a {\n  color: #409EFF;\n  text-decoration: none;\n}\n\n.agreement-checkbox a:hover {\n  text-decoration: underline;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .login-container {\n    padding: 20px 15px;\n  }\n  \n  .code-button {\n    width: 100px;\n    font-size: 12px;\n  }\n  \n  .phone-input .el-input__inner,\n  .code-input .el-input__inner {\n    height: 40px;\n    font-size: 14px;\n  }\n  \n  .login-button {\n    height: 40px;\n    font-size: 14px;\n  }\n}\n</style>\n"], "mappings": ";AAgFA,OAAAA,SAAA;AACA,SAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAH;EACA;EACAI,KAAA;IACA;MACAC,IAAA;QACAC,KAAA;QACAC,IAAA;MACA;MACAC,MAAA;MACAC,YAAA;MACAC,YAAA;MACAC,SAAA;MACAC,WAAA;MACAC,KAAA;IACA;EACA;EACAC,QAAA;IACAC,aAAA;MACA,uBAAAC,IAAA,MAAAX,IAAA,CAAAC,KAAA;IACA;EACA;EACAW,cAAA;IACA,SAAAJ,KAAA;MACAK,aAAA,MAAAL,KAAA;IACA;EACA;EACAM,OAAA;IACA;IACAC,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IAEA;IACAC,cAAA;MACA;MACA,KAAAlB,IAAA,CAAAC,KAAA,QAAAD,IAAA,CAAAC,KAAA,CAAAkB,OAAA;IACA;IAEA;IACA,MAAAC,SAAA;MACA,UAAAV,YAAA;QACA,KAAAW,QAAA,CAAAC,KAAA;QACA;MACA;MAEA;QACA,MAAA1B,OAAA,CAAAwB,QAAA,MAAApB,IAAA,CAAAC,KAAA;QACA,KAAAoB,QAAA,CAAAE,OAAA;QACA,KAAAC,cAAA;MACA,SAAAF,KAAA;QACAG,OAAA,CAAAH,KAAA,aAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAE,eAAA;MACA,KAAAlB,SAAA;MACA,KAAAD,YAAA;MACA,KAAAE,WAAA,WAAAD,SAAA;MAEA,KAAAE,KAAA,GAAAkB,WAAA;QACA,KAAApB,SAAA;QACA,KAAAC,WAAA,WAAAD,SAAA;QAEA,SAAAA,SAAA;UACA,KAAAqB,aAAA;QACA;MACA;IACA;IAEA;IACAA,cAAA;MACA,SAAAnB,KAAA;QACAK,aAAA,MAAAL,KAAA;QACA,KAAAA,KAAA;MACA;MACA,KAAAF,SAAA;MACA,KAAAD,YAAA;MACA,KAAAE,WAAA;IACA;IAEA;IACA,MAAAqB,YAAA;MACA,UAAAC,YAAA;QACA;MACA;MAEA,KAAAzB,YAAA;MACA;QACA,MAAA0B,QAAA,SAAAlC,OAAA,CAAAmC,KAAA,MAAA/B,IAAA;;QAEA;QACA,KAAAgC,MAAA,CAAAC,QAAA;UACAC,IAAA,EAAAJ,QAAA,CAAA/B,IAAA;UACAoC,KAAA,EAAAL,QAAA,CAAA/B,IAAA,CAAAoC;QACA;QAEA,KAAAd,QAAA,CAAAE,OAAA;;QAEA;QACA,MAAAa,QAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,QAAA;QACA,KAAApB,OAAA,CAAAuB,IAAA,CAAAH,QAAA;MAEA,SAAAd,KAAA;QACAG,OAAA,CAAAH,KAAA,UAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;QACA,KAAAlB,YAAA;MACA;IACA;IAEA;IACAyB,aAAA;MACA,UAAAnB,YAAA;QACA,KAAAW,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,UAAAtB,IAAA,CAAAE,IAAA,SAAAF,IAAA,CAAAE,IAAA,CAAAsC,MAAA;QACA,KAAAnB,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,UAAAnB,MAAA;QACA,KAAAkB,QAAA,CAAAC,KAAA;QACA;MACA;MAEA;IACA;IAEA;IACAmB,cAAAC,IAAA;MACA,IAAAA,IAAA;QACA,KAAArB,QAAA,CAAAsB,IAAA;MACA,WAAAD,IAAA;QACA,KAAArB,QAAA,CAAAsB,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}