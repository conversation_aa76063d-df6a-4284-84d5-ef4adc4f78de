{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport HeaderBar from '@/components/HeaderBar.vue';\nimport { userApi } from '@/api';\nexport default {\n  name: 'Login2',\n  components: {\n    HeaderBar\n  },\n  data() {\n    return {\n      form: {\n        phone: '',\n        password: ''\n      },\n      agreed: false,\n      loginLoading: false\n    };\n  },\n  computed: {\n    isPhoneValid() {\n      return /^1[3-9]\\d{9}$/.test(this.form.phone);\n    }\n  },\n  methods: {\n    // 返回上一页\n    goBack() {\n      this.$router.go(-1);\n    },\n    // 验证手机号\n    validatePhone() {\n      // 只保留数字\n      this.form.phone = this.form.phone.replace(/\\D/g, '');\n    },\n    // 处理登录\n    async handleLogin() {\n      if (!this.validateForm()) {\n        return;\n      }\n      this.loginLoading = true;\n      try {\n        const response = await userApi.login(this.form);\n\n        // 保存用户信息和token\n        this.$store.dispatch('login', {\n          user: response.data,\n          token: response.data.token\n        });\n        this.$message.success('登录成功');\n\n        // 跳转到首页或返回之前的页面\n        const redirect = this.$route.query.redirect || '/';\n        this.$router.push(redirect);\n      } catch (error) {\n        console.error('登录失败:', error);\n        this.$message.error('登录失败，请检查手机号和密码');\n      } finally {\n        this.loginLoading = false;\n      }\n    },\n    // 表单验证\n    validateForm() {\n      if (!this.isPhoneValid) {\n        this.$message.error('请输入正确的手机号');\n        return false;\n      }\n      if (!this.form.password || this.form.password.length < 6) {\n        this.$message.error('密码长度不能少于6位');\n        return false;\n      }\n      if (!this.agreed) {\n        this.$message.error('请先同意用户协议');\n        return false;\n      }\n      return true;\n    },\n    // 忘记密码\n    forgotPassword() {\n      this.$message.info('请联系客服重置密码');\n    },\n    // 显示协议\n    showAgreement(type) {\n      if (type === 'service') {\n        this.$message.info('用户服务协议');\n      } else if (type === 'privacy') {\n        this.$message.info('隐私政策');\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "userApi", "name", "components", "data", "form", "phone", "password", "agreed", "loginLoading", "computed", "isPhoneValid", "test", "methods", "goBack", "$router", "go", "validatePhone", "replace", "handleLogin", "validateForm", "response", "login", "$store", "dispatch", "user", "token", "$message", "success", "redirect", "$route", "query", "push", "error", "console", "length", "forgotPassword", "info", "showAgreement", "type"], "sources": ["src/views/Login2.vue"], "sourcesContent": ["<template>\n  <div class=\"login-page\">\n    <!-- 头部 -->\n    <HeaderBar \n      title=\"密码登录\" \n      :show-back=\"true\" \n      @back=\"goBack\"\n    />\n    \n    <!-- 登录表单 -->\n    <div class=\"login-container\">\n      <div class=\"login-form\">\n        <!-- 手机号输入 -->\n        <el-input\n          v-model=\"form.phone\"\n          placeholder=\"请输入手机号\"\n          class=\"form-input\"\n          maxlength=\"11\"\n          @input=\"validatePhone\"\n        >\n          <i slot=\"prefix\" class=\"el-input__icon el-icon-mobile-phone\"></i>\n        </el-input>\n        \n        <!-- 密码输入 -->\n        <el-input\n          v-model=\"form.password\"\n          type=\"password\"\n          placeholder=\"请输入密码\"\n          class=\"form-input\"\n          show-password\n        >\n          <i slot=\"prefix\" class=\"el-input__icon el-icon-lock\"></i>\n        </el-input>\n        \n        <!-- 登录按钮 -->\n        <el-button\n          type=\"primary\"\n          class=\"login-button\"\n          :loading=\"loginLoading\"\n          @click=\"handleLogin\"\n        >\n          登录\n        </el-button>\n        \n        <!-- 验证码登录链接 -->\n        <div class=\"code-login-link\">\n          <router-link to=\"/login\">验证码登录</router-link>\n        </div>\n        \n        <!-- 忘记密码 -->\n        <div class=\"forgot-password\">\n          <a href=\"javascript:void(0)\" @click=\"forgotPassword\">忘记密码？</a>\n        </div>\n      </div>\n      \n      <!-- 用户协议 -->\n      <div class=\"agreement\">\n        <div class=\"agreement-checkbox\">\n          <el-checkbox v-model=\"agreed\">\n            我已阅读并同意\n            <a href=\"javascript:void(0)\" @click=\"showAgreement('service')\">\n              《黑马点评用户服务协议》\n            </a>、\n            <a href=\"javascript:void(0)\" @click=\"showAgreement('privacy')\">\n              《隐私政策》\n            </a>\n            等，接受免除或者限制责任、诉讼管辖约定等粗体标示条款\n          </el-checkbox>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport HeaderBar from '@/components/HeaderBar.vue'\nimport { userApi } from '@/api'\n\nexport default {\n  name: 'Login2',\n  components: {\n    HeaderBar\n  },\n  data() {\n    return {\n      form: {\n        phone: '',\n        password: ''\n      },\n      agreed: false,\n      loginLoading: false\n    }\n  },\n  computed: {\n    isPhoneValid() {\n      return /^1[3-9]\\d{9}$/.test(this.form.phone)\n    }\n  },\n  methods: {\n    // 返回上一页\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    // 验证手机号\n    validatePhone() {\n      // 只保留数字\n      this.form.phone = this.form.phone.replace(/\\D/g, '')\n    },\n    \n    // 处理登录\n    async handleLogin() {\n      if (!this.validateForm()) {\n        return\n      }\n      \n      this.loginLoading = true\n      try {\n        const response = await userApi.login(this.form)\n        \n        // 保存用户信息和token\n        this.$store.dispatch('login', {\n          user: response.data,\n          token: response.data.token\n        })\n        \n        this.$message.success('登录成功')\n        \n        // 跳转到首页或返回之前的页面\n        const redirect = this.$route.query.redirect || '/'\n        this.$router.push(redirect)\n        \n      } catch (error) {\n        console.error('登录失败:', error)\n        this.$message.error('登录失败，请检查手机号和密码')\n      } finally {\n        this.loginLoading = false\n      }\n    },\n    \n    // 表单验证\n    validateForm() {\n      if (!this.isPhoneValid) {\n        this.$message.error('请输入正确的手机号')\n        return false\n      }\n      \n      if (!this.form.password || this.form.password.length < 6) {\n        this.$message.error('密码长度不能少于6位')\n        return false\n      }\n      \n      if (!this.agreed) {\n        this.$message.error('请先同意用户协议')\n        return false\n      }\n      \n      return true\n    },\n    \n    // 忘记密码\n    forgotPassword() {\n      this.$message.info('请联系客服重置密码')\n    },\n    \n    // 显示协议\n    showAgreement(type) {\n      if (type === 'service') {\n        this.$message.info('用户服务协议')\n      } else if (type === 'privacy') {\n        this.$message.info('隐私政策')\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.login-page {\n  height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n}\n\n.login-container {\n  flex: 1;\n  padding: 30px 20px;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n\n.login-form {\n  margin-top: 50px;\n}\n\n.form-input {\n  margin-bottom: 20px;\n}\n\n.form-input .el-input__inner {\n  border-radius: 20px;\n  height: 45px;\n  font-size: 16px;\n  padding-left: 45px;\n}\n\n.form-input .el-input__prefix {\n  left: 15px;\n}\n\n.login-button {\n  width: 100%;\n  height: 45px;\n  background-color: #ff6633;\n  border-color: #ff6633;\n  border-radius: 20px;\n  font-size: 16px;\n  margin-bottom: 15px;\n}\n\n.login-button:hover {\n  background-color: #e55a2b;\n  border-color: #e55a2b;\n}\n\n.code-login-link {\n  text-align: right;\n  margin-bottom: 10px;\n}\n\n.code-login-link a {\n  color: #333;\n  text-decoration: none;\n  font-size: 14px;\n}\n\n.code-login-link a:hover {\n  color: #ff6633;\n}\n\n.forgot-password {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.forgot-password a {\n  color: #666;\n  text-decoration: none;\n  font-size: 14px;\n}\n\n.forgot-password a:hover {\n  color: #ff6633;\n}\n\n.agreement {\n  margin-top: auto;\n}\n\n.agreement-checkbox {\n  font-size: 12px;\n  line-height: 1.5;\n}\n\n.agreement-checkbox a {\n  color: #409EFF;\n  text-decoration: none;\n}\n\n.agreement-checkbox a:hover {\n  text-decoration: underline;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .login-container {\n    padding: 20px 15px;\n  }\n  \n  .form-input .el-input__inner {\n    height: 40px;\n    font-size: 14px;\n  }\n  \n  .login-button {\n    height: 40px;\n    font-size: 14px;\n  }\n}\n</style>\n"], "mappings": ";AA2EA,OAAAA,SAAA;AACA,SAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAH;EACA;EACAI,KAAA;IACA;MACAC,IAAA;QACAC,KAAA;QACAC,QAAA;MACA;MACAC,MAAA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACAC,aAAA;MACA,uBAAAC,IAAA,MAAAP,IAAA,CAAAC,KAAA;IACA;EACA;EACAO,OAAA;IACA;IACAC,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IAEA;IACAC,cAAA;MACA;MACA,KAAAZ,IAAA,CAAAC,KAAA,QAAAD,IAAA,CAAAC,KAAA,CAAAY,OAAA;IACA;IAEA;IACA,MAAAC,YAAA;MACA,UAAAC,YAAA;QACA;MACA;MAEA,KAAAX,YAAA;MACA;QACA,MAAAY,QAAA,SAAApB,OAAA,CAAAqB,KAAA,MAAAjB,IAAA;;QAEA;QACA,KAAAkB,MAAA,CAAAC,QAAA;UACAC,IAAA,EAAAJ,QAAA,CAAAjB,IAAA;UACAsB,KAAA,EAAAL,QAAA,CAAAjB,IAAA,CAAAsB;QACA;QAEA,KAAAC,QAAA,CAAAC,OAAA;;QAEA;QACA,MAAAC,QAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,QAAA;QACA,KAAAd,OAAA,CAAAiB,IAAA,CAAAH,QAAA;MAEA,SAAAI,KAAA;QACAC,OAAA,CAAAD,KAAA,UAAAA,KAAA;QACA,KAAAN,QAAA,CAAAM,KAAA;MACA;QACA,KAAAxB,YAAA;MACA;IACA;IAEA;IACAW,aAAA;MACA,UAAAT,YAAA;QACA,KAAAgB,QAAA,CAAAM,KAAA;QACA;MACA;MAEA,UAAA5B,IAAA,CAAAE,QAAA,SAAAF,IAAA,CAAAE,QAAA,CAAA4B,MAAA;QACA,KAAAR,QAAA,CAAAM,KAAA;QACA;MACA;MAEA,UAAAzB,MAAA;QACA,KAAAmB,QAAA,CAAAM,KAAA;QACA;MACA;MAEA;IACA;IAEA;IACAG,eAAA;MACA,KAAAT,QAAA,CAAAU,IAAA;IACA;IAEA;IACAC,cAAAC,IAAA;MACA,IAAAA,IAAA;QACA,KAAAZ,QAAA,CAAAU,IAAA;MACA,WAAAE,IAAA;QACA,KAAAZ,QAAA,CAAAU,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}