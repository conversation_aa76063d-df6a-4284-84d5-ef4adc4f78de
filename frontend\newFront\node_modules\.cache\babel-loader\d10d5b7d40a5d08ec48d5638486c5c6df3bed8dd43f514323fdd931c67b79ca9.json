{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"shop-list-page\"\n  }, [_c(\"HeaderBar\", {\n    attrs: {\n      title: _vm.typeName,\n      \"show-back\": true,\n      \"show-search\": true\n    },\n    on: {\n      back: _vm.goBack,\n      search: _vm.handleSearch\n    }\n  }), _c(\"div\", {\n    staticClass: \"sort-bar\"\n  }, [_c(\"div\", {\n    staticClass: \"sort-item\"\n  }, [_c(\"el-dropdown\", {\n    attrs: {\n      trigger: \"click\"\n    },\n    on: {\n      command: _vm.handleTypeChange\n    }\n  }, [_c(\"span\", {\n    staticClass: \"dropdown-link\"\n  }, [_vm._v(\" \" + _vm._s(_vm.typeName) + \" \"), _c(\"i\", {\n    staticClass: \"el-icon-arrow-down\"\n  })]), _c(\"el-dropdown-menu\", {\n    attrs: {\n      slot: \"dropdown\"\n    },\n    slot: \"dropdown\"\n  }, _vm._l(_vm.shopTypes, function (type) {\n    return _c(\"el-dropdown-item\", {\n      key: type.id,\n      attrs: {\n        command: type\n      }\n    }, [_vm._v(\" \" + _vm._s(type.name) + \" \")]);\n  }), 1)], 1)], 1), _c(\"div\", {\n    staticClass: \"sort-item\",\n    class: {\n      active: _vm.sortBy === \"distance\"\n    },\n    on: {\n      click: function ($event) {\n        return _vm.handleSort(\"distance\");\n      }\n    }\n  }, [_vm._v(\" 距离 \"), _c(\"i\", {\n    staticClass: \"el-icon-arrow-down\"\n  })]), _c(\"div\", {\n    staticClass: \"sort-item\",\n    class: {\n      active: _vm.sortBy === \"comments\"\n    },\n    on: {\n      click: function ($event) {\n        return _vm.handleSort(\"comments\");\n      }\n    }\n  }, [_vm._v(\" 人气 \"), _c(\"i\", {\n    staticClass: \"el-icon-arrow-down\"\n  })]), _c(\"div\", {\n    staticClass: \"sort-item\",\n    class: {\n      active: _vm.sortBy === \"score\"\n    },\n    on: {\n      click: function ($event) {\n        return _vm.handleSort(\"score\");\n      }\n    }\n  }, [_vm._v(\" 评分 \"), _c(\"i\", {\n    staticClass: \"el-icon-arrow-down\"\n  })])]), _c(\"div\", {\n    ref: \"shopList\",\n    staticClass: \"shop-list\",\n    on: {\n      scroll: _vm.handleScroll\n    }\n  }, [_vm._l(_vm.shops, function (shop) {\n    return _c(\"div\", {\n      key: shop.id,\n      staticClass: \"shop-item\",\n      on: {\n        click: function ($event) {\n          return _vm.toShopDetail(shop.id);\n        }\n      }\n    }, [_c(\"div\", {\n      staticClass: \"shop-image\"\n    }, [_c(\"img\", {\n      attrs: {\n        src: shop.images ? shop.images.split(\",\")[0] : \"/imgs/default-shop.png\",\n        alt: shop.name\n      }\n    })]), _c(\"div\", {\n      staticClass: \"shop-content\"\n    }, [_c(\"div\", {\n      staticClass: \"shop-name\"\n    }, [_vm._v(_vm._s(shop.name))]), _c(\"div\", {\n      staticClass: \"shop-type\"\n    }, [_vm._v(_vm._s(shop.typeName))]), _c(\"div\", {\n      staticClass: \"shop-address\"\n    }, [_vm._v(_vm._s(shop.address))]), _c(\"div\", {\n      staticClass: \"shop-footer\"\n    }, [_c(\"div\", {\n      staticClass: \"shop-score\"\n    }, [_c(\"el-rate\", {\n      attrs: {\n        disabled: \"\",\n        \"show-score\": \"\",\n        \"text-color\": \"#ff9900\",\n        \"score-template\": \"{value}\"\n      },\n      model: {\n        value: shop.score,\n        callback: function ($$v) {\n          _vm.$set(shop, \"score\", $$v);\n        },\n        expression: \"shop.score\"\n      }\n    })], 1), shop.distance ? _c(\"div\", {\n      staticClass: \"shop-distance\"\n    }, [_vm._v(\" \" + _vm._s(_vm.formatDistance(shop.distance)) + \" \")]) : _vm._e()])])]);\n  }), _vm.loading ? _c(\"div\", {\n    staticClass: \"loading-more\"\n  }, [_c(\"LoadingSpinner\", {\n    attrs: {\n      text: \"加载中...\"\n    }\n  })], 1) : _vm._e(), _vm.noMore && _vm.shops.length > 0 ? _c(\"div\", {\n    staticClass: \"no-more\"\n  }, [_vm._v(\" 没有更多商户了 \")]) : _vm._e(), !_vm.loading && _vm.shops.length === 0 ? _c(\"EmptyState\", {\n    attrs: {\n      text: \"暂无商户信息\",\n      icon: \"el-icon-shop\",\n      \"show-action\": true,\n      \"action-text\": \"刷新\"\n    },\n    on: {\n      action: _vm.refreshData\n    }\n  }) : _vm._e()], 2)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "title", "typeName", "on", "back", "goBack", "search", "handleSearch", "trigger", "command", "handleTypeChange", "_v", "_s", "slot", "_l", "shopTypes", "type", "key", "id", "name", "class", "active", "sortBy", "click", "$event", "handleSort", "ref", "scroll", "handleScroll", "shops", "shop", "toShopDetail", "src", "images", "split", "alt", "address", "disabled", "model", "value", "score", "callback", "$$v", "$set", "expression", "distance", "formatDistance", "_e", "loading", "text", "noMore", "length", "icon", "action", "refreshData", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/src/views/ShopList.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"shop-list-page\" },\n    [\n      _c(\"HeaderBar\", {\n        attrs: { title: _vm.typeName, \"show-back\": true, \"show-search\": true },\n        on: { back: _vm.goBack, search: _vm.handleSearch },\n      }),\n      _c(\"div\", { staticClass: \"sort-bar\" }, [\n        _c(\n          \"div\",\n          { staticClass: \"sort-item\" },\n          [\n            _c(\n              \"el-dropdown\",\n              {\n                attrs: { trigger: \"click\" },\n                on: { command: _vm.handleTypeChange },\n              },\n              [\n                _c(\"span\", { staticClass: \"dropdown-link\" }, [\n                  _vm._v(\" \" + _vm._s(_vm.typeName) + \" \"),\n                  _c(\"i\", { staticClass: \"el-icon-arrow-down\" }),\n                ]),\n                _c(\n                  \"el-dropdown-menu\",\n                  { attrs: { slot: \"dropdown\" }, slot: \"dropdown\" },\n                  _vm._l(_vm.shopTypes, function (type) {\n                    return _c(\n                      \"el-dropdown-item\",\n                      { key: type.id, attrs: { command: type } },\n                      [_vm._v(\" \" + _vm._s(type.name) + \" \")]\n                    )\n                  }),\n                  1\n                ),\n              ],\n              1\n            ),\n          ],\n          1\n        ),\n        _c(\n          \"div\",\n          {\n            staticClass: \"sort-item\",\n            class: { active: _vm.sortBy === \"distance\" },\n            on: {\n              click: function ($event) {\n                return _vm.handleSort(\"distance\")\n              },\n            },\n          },\n          [_vm._v(\" 距离 \"), _c(\"i\", { staticClass: \"el-icon-arrow-down\" })]\n        ),\n        _c(\n          \"div\",\n          {\n            staticClass: \"sort-item\",\n            class: { active: _vm.sortBy === \"comments\" },\n            on: {\n              click: function ($event) {\n                return _vm.handleSort(\"comments\")\n              },\n            },\n          },\n          [_vm._v(\" 人气 \"), _c(\"i\", { staticClass: \"el-icon-arrow-down\" })]\n        ),\n        _c(\n          \"div\",\n          {\n            staticClass: \"sort-item\",\n            class: { active: _vm.sortBy === \"score\" },\n            on: {\n              click: function ($event) {\n                return _vm.handleSort(\"score\")\n              },\n            },\n          },\n          [_vm._v(\" 评分 \"), _c(\"i\", { staticClass: \"el-icon-arrow-down\" })]\n        ),\n      ]),\n      _c(\n        \"div\",\n        {\n          ref: \"shopList\",\n          staticClass: \"shop-list\",\n          on: { scroll: _vm.handleScroll },\n        },\n        [\n          _vm._l(_vm.shops, function (shop) {\n            return _c(\n              \"div\",\n              {\n                key: shop.id,\n                staticClass: \"shop-item\",\n                on: {\n                  click: function ($event) {\n                    return _vm.toShopDetail(shop.id)\n                  },\n                },\n              },\n              [\n                _c(\"div\", { staticClass: \"shop-image\" }, [\n                  _c(\"img\", {\n                    attrs: {\n                      src: shop.images\n                        ? shop.images.split(\",\")[0]\n                        : \"/imgs/default-shop.png\",\n                      alt: shop.name,\n                    },\n                  }),\n                ]),\n                _c(\"div\", { staticClass: \"shop-content\" }, [\n                  _c(\"div\", { staticClass: \"shop-name\" }, [\n                    _vm._v(_vm._s(shop.name)),\n                  ]),\n                  _c(\"div\", { staticClass: \"shop-type\" }, [\n                    _vm._v(_vm._s(shop.typeName)),\n                  ]),\n                  _c(\"div\", { staticClass: \"shop-address\" }, [\n                    _vm._v(_vm._s(shop.address)),\n                  ]),\n                  _c(\"div\", { staticClass: \"shop-footer\" }, [\n                    _c(\n                      \"div\",\n                      { staticClass: \"shop-score\" },\n                      [\n                        _c(\"el-rate\", {\n                          attrs: {\n                            disabled: \"\",\n                            \"show-score\": \"\",\n                            \"text-color\": \"#ff9900\",\n                            \"score-template\": \"{value}\",\n                          },\n                          model: {\n                            value: shop.score,\n                            callback: function ($$v) {\n                              _vm.$set(shop, \"score\", $$v)\n                            },\n                            expression: \"shop.score\",\n                          },\n                        }),\n                      ],\n                      1\n                    ),\n                    shop.distance\n                      ? _c(\"div\", { staticClass: \"shop-distance\" }, [\n                          _vm._v(\n                            \" \" +\n                              _vm._s(_vm.formatDistance(shop.distance)) +\n                              \" \"\n                          ),\n                        ])\n                      : _vm._e(),\n                  ]),\n                ]),\n              ]\n            )\n          }),\n          _vm.loading\n            ? _c(\n                \"div\",\n                { staticClass: \"loading-more\" },\n                [_c(\"LoadingSpinner\", { attrs: { text: \"加载中...\" } })],\n                1\n              )\n            : _vm._e(),\n          _vm.noMore && _vm.shops.length > 0\n            ? _c(\"div\", { staticClass: \"no-more\" }, [\n                _vm._v(\" 没有更多商户了 \"),\n              ])\n            : _vm._e(),\n          !_vm.loading && _vm.shops.length === 0\n            ? _c(\"EmptyState\", {\n                attrs: {\n                  text: \"暂无商户信息\",\n                  icon: \"el-icon-shop\",\n                  \"show-action\": true,\n                  \"action-text\": \"刷新\",\n                },\n                on: { action: _vm.refreshData },\n              })\n            : _vm._e(),\n        ],\n        2\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAiB,CAAC,EACjC,CACEF,EAAE,CAAC,WAAW,EAAE;IACdG,KAAK,EAAE;MAAEC,KAAK,EAAEL,GAAG,CAACM,QAAQ;MAAE,WAAW,EAAE,IAAI;MAAE,aAAa,EAAE;IAAK,CAAC;IACtEC,EAAE,EAAE;MAAEC,IAAI,EAAER,GAAG,CAACS,MAAM;MAAEC,MAAM,EAAEV,GAAG,CAACW;IAAa;EACnD,CAAC,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAW,CAAC,EAAE,CACrCF,EAAE,CACA,KAAK,EACL;IAA<PERSON>,WAAW,EAAE;EAAY,CAAC,EAC5B,CACEF,EAAE,CACA,aAAa,EACb;IACEG,KAAK,EAAE;MAAEQ,OAAO,EAAE;IAAQ,CAAC;IAC3BL,EAAE,EAAE;MAAEM,OAAO,EAAEb,GAAG,CAACc;IAAiB;EACtC,CAAC,EACD,CACEb,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACe,EAAE,CAAC,GAAG,GAAGf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACM,QAAQ,CAAC,GAAG,GAAG,CAAC,EACxCL,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,CAAC,CAC/C,CAAC,EACFF,EAAE,CACA,kBAAkB,EAClB;IAAEG,KAAK,EAAE;MAAEa,IAAI,EAAE;IAAW,CAAC;IAAEA,IAAI,EAAE;EAAW,CAAC,EACjDjB,GAAG,CAACkB,EAAE,CAAClB,GAAG,CAACmB,SAAS,EAAE,UAAUC,IAAI,EAAE;IACpC,OAAOnB,EAAE,CACP,kBAAkB,EAClB;MAAEoB,GAAG,EAAED,IAAI,CAACE,EAAE;MAAElB,KAAK,EAAE;QAAES,OAAO,EAAEO;MAAK;IAAE,CAAC,EAC1C,CAACpB,GAAG,CAACe,EAAE,CAAC,GAAG,GAAGf,GAAG,CAACgB,EAAE,CAACI,IAAI,CAACG,IAAI,CAAC,GAAG,GAAG,CAAC,CACxC,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CACF,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDtB,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,WAAW;IACxBqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,MAAM,KAAK;IAAW,CAAC;IAC5CnB,EAAE,EAAE;MACFoB,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAO5B,GAAG,CAAC6B,UAAU,CAAC,UAAU,CAAC;MACnC;IACF;EACF,CAAC,EACD,CAAC7B,GAAG,CAACe,EAAE,CAAC,MAAM,CAAC,EAAEd,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,CAAC,CACjE,CAAC,EACDF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,WAAW;IACxBqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,MAAM,KAAK;IAAW,CAAC;IAC5CnB,EAAE,EAAE;MACFoB,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAO5B,GAAG,CAAC6B,UAAU,CAAC,UAAU,CAAC;MACnC;IACF;EACF,CAAC,EACD,CAAC7B,GAAG,CAACe,EAAE,CAAC,MAAM,CAAC,EAAEd,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,CAAC,CACjE,CAAC,EACDF,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,WAAW;IACxBqB,KAAK,EAAE;MAAEC,MAAM,EAAEzB,GAAG,CAAC0B,MAAM,KAAK;IAAQ,CAAC;IACzCnB,EAAE,EAAE;MACFoB,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAO5B,GAAG,CAAC6B,UAAU,CAAC,OAAO,CAAC;MAChC;IACF;EACF,CAAC,EACD,CAAC7B,GAAG,CAACe,EAAE,CAAC,MAAM,CAAC,EAAEd,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAqB,CAAC,CAAC,CACjE,CAAC,CACF,CAAC,EACFF,EAAE,CACA,KAAK,EACL;IACE6B,GAAG,EAAE,UAAU;IACf3B,WAAW,EAAE,WAAW;IACxBI,EAAE,EAAE;MAAEwB,MAAM,EAAE/B,GAAG,CAACgC;IAAa;EACjC,CAAC,EACD,CACEhC,GAAG,CAACkB,EAAE,CAAClB,GAAG,CAACiC,KAAK,EAAE,UAAUC,IAAI,EAAE;IAChC,OAAOjC,EAAE,CACP,KAAK,EACL;MACEoB,GAAG,EAAEa,IAAI,CAACZ,EAAE;MACZnB,WAAW,EAAE,WAAW;MACxBI,EAAE,EAAE;QACFoB,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;UACvB,OAAO5B,GAAG,CAACmC,YAAY,CAACD,IAAI,CAACZ,EAAE,CAAC;QAClC;MACF;IACF,CAAC,EACD,CACErB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,KAAK,EAAE;MACRG,KAAK,EAAE;QACLgC,GAAG,EAAEF,IAAI,CAACG,MAAM,GACZH,IAAI,CAACG,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACzB,wBAAwB;QAC5BC,GAAG,EAAEL,IAAI,CAACX;MACZ;IACF,CAAC,CAAC,CACH,CAAC,EACFtB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCH,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgB,EAAE,CAACkB,IAAI,CAACX,IAAI,CAAC,CAAC,CAC1B,CAAC,EACFtB,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCH,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgB,EAAE,CAACkB,IAAI,CAAC5B,QAAQ,CAAC,CAAC,CAC9B,CAAC,EACFL,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAe,CAAC,EAAE,CACzCH,GAAG,CAACe,EAAE,CAACf,GAAG,CAACgB,EAAE,CAACkB,IAAI,CAACM,OAAO,CAAC,CAAC,CAC7B,CAAC,EACFvC,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAc,CAAC,EAAE,CACxCF,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAa,CAAC,EAC7B,CACEF,EAAE,CAAC,SAAS,EAAE;MACZG,KAAK,EAAE;QACLqC,QAAQ,EAAE,EAAE;QACZ,YAAY,EAAE,EAAE;QAChB,YAAY,EAAE,SAAS;QACvB,gBAAgB,EAAE;MACpB,CAAC;MACDC,KAAK,EAAE;QACLC,KAAK,EAAET,IAAI,CAACU,KAAK;QACjBC,QAAQ,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACvB9C,GAAG,CAAC+C,IAAI,CAACb,IAAI,EAAE,OAAO,EAAEY,GAAG,CAAC;QAC9B,CAAC;QACDE,UAAU,EAAE;MACd;IACF,CAAC,CAAC,CACH,EACD,CACF,CAAC,EACDd,IAAI,CAACe,QAAQ,GACThD,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAgB,CAAC,EAAE,CAC1CH,GAAG,CAACe,EAAE,CACJ,GAAG,GACDf,GAAG,CAACgB,EAAE,CAAChB,GAAG,CAACkD,cAAc,CAAChB,IAAI,CAACe,QAAQ,CAAC,CAAC,GACzC,GACJ,CAAC,CACF,CAAC,GACFjD,GAAG,CAACmD,EAAE,CAAC,CAAC,CACb,CAAC,CACH,CAAC,CAEN,CAAC;EACH,CAAC,CAAC,EACFnD,GAAG,CAACoD,OAAO,GACPnD,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CAACF,EAAE,CAAC,gBAAgB,EAAE;IAAEG,KAAK,EAAE;MAAEiD,IAAI,EAAE;IAAS;EAAE,CAAC,CAAC,CAAC,EACrD,CACF,CAAC,GACDrD,GAAG,CAACmD,EAAE,CAAC,CAAC,EACZnD,GAAG,CAACsD,MAAM,IAAItD,GAAG,CAACiC,KAAK,CAACsB,MAAM,GAAG,CAAC,GAC9BtD,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,EAAE,CACpCH,GAAG,CAACe,EAAE,CAAC,WAAW,CAAC,CACpB,CAAC,GACFf,GAAG,CAACmD,EAAE,CAAC,CAAC,EACZ,CAACnD,GAAG,CAACoD,OAAO,IAAIpD,GAAG,CAACiC,KAAK,CAACsB,MAAM,KAAK,CAAC,GAClCtD,EAAE,CAAC,YAAY,EAAE;IACfG,KAAK,EAAE;MACLiD,IAAI,EAAE,QAAQ;MACdG,IAAI,EAAE,cAAc;MACpB,aAAa,EAAE,IAAI;MACnB,aAAa,EAAE;IACjB,CAAC;IACDjD,EAAE,EAAE;MAAEkD,MAAM,EAAEzD,GAAG,CAAC0D;IAAY;EAChC,CAAC,CAAC,GACF1D,GAAG,CAACmD,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIQ,eAAe,GAAG,EAAE;AACxB5D,MAAM,CAAC6D,aAAa,GAAG,IAAI;AAE3B,SAAS7D,MAAM,EAAE4D,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}