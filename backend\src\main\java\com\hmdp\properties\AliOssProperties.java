package com.hmdp.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 阿里云OSS配置属性类
 * 用于从配置文件中读取阿里云OSS相关的配置信息
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Component
@ConfigurationProperties(prefix = "sky.alioss")
@Data
public class AliOssProperties {

    /**
     * OSS服务的访问域名
     * 例如：oss-cn-beijing.aliyuncs.com
     */
    private String endpoint;

    /**
     * 访问密钥ID
     */
    private String accessKeyId;

    /**
     * 访问密钥Secret
     */
    private String accessKeySecret;

    /**
     * OSS存储桶名称
     */
    private String bucketName;
}
