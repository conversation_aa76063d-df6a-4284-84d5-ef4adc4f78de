[{"C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\main.js": "1", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\utils\\request.js": "2", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\App.vue": "3", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\router\\index.js": "4", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\store\\index.js": "5", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\BlogEdit.vue": "6", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\OtherUserInfo.vue": "7", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\UserInfo.vue": "8", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\UserInfoEdit.vue": "9", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\ShopDetail.vue": "10", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\BlogDetail.vue": "11", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\ShopList.vue": "12", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\Home.vue": "13", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\Login2.vue": "14", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\Login.vue": "15", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\components\\HeaderBar.vue": "16", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\components\\LoadingSpinner.vue": "17", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\components\\EmptyState.vue": "18", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\components\\SearchBar.vue": "19", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\components\\FooterBar.vue": "20", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\api\\index.js": "21", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\api\\user.js": "22", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\api\\shop.js": "23", "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\api\\blog.js": "24"}, {"size": 1827, "mtime": 1753671041838, "results": "25", "hashOfConfig": "26"}, {"size": 1908, "mtime": 1753671119876, "results": "27", "hashOfConfig": "26"}, {"size": 185, "mtime": 1753671057120, "results": "28", "hashOfConfig": "26"}, {"size": 2039, "mtime": 1753671082855, "results": "29", "hashOfConfig": "26"}, {"size": 2297, "mtime": 1753673629995, "results": "30", "hashOfConfig": "26"}, {"size": 4308, "mtime": 1753671869326, "results": "31", "hashOfConfig": "26"}, {"size": 6401, "mtime": 1753671943751, "results": "32", "hashOfConfig": "26"}, {"size": 7002, "mtime": 1753671777614, "results": "33", "hashOfConfig": "26"}, {"size": 4836, "mtime": 1753671902179, "results": "34", "hashOfConfig": "26"}, {"size": 6885, "mtime": 1753671731441, "results": "35", "hashOfConfig": "26"}, {"size": 8834, "mtime": 1753671835276, "results": "36", "hashOfConfig": "26"}, {"size": 10765, "mtime": 1753709060914, "results": "37", "hashOfConfig": "26"}, {"size": 11719, "mtime": 1753700384194, "results": "38", "hashOfConfig": "26"}, {"size": 5985, "mtime": 1753671564657, "results": "39", "hashOfConfig": "26"}, {"size": 7333, "mtime": 1753671532083, "results": "40", "hashOfConfig": "26"}, {"size": 3056, "mtime": 1753709097018, "results": "41", "hashOfConfig": "26"}, {"size": 1170, "mtime": 1753671320908, "results": "42", "hashOfConfig": "26"}, {"size": 1198, "mtime": 1753671337277, "results": "43", "hashOfConfig": "26"}, {"size": 3508, "mtime": 1753671303298, "results": "44", "hashOfConfig": "26"}, {"size": 3172, "mtime": 1753671247566, "results": "45", "hashOfConfig": "26"}, {"size": 251, "mtime": 1753671176928, "results": "46", "hashOfConfig": "26"}, {"size": 1581, "mtime": 1753671134111, "results": "47", "hashOfConfig": "26"}, {"size": 1546, "mtime": 1753671151047, "results": "48", "hashOfConfig": "26"}, {"size": 1936, "mtime": 1753671165727, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1eje7qw", {"filePath": "52", "messages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\main.js", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\utils\\request.js", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\App.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\router\\index.js", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\store\\index.js", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\BlogEdit.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\OtherUserInfo.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\UserInfo.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\UserInfoEdit.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\ShopDetail.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\BlogDetail.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\ShopList.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\Home.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\Login2.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\views\\Login.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\components\\HeaderBar.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\components\\LoadingSpinner.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\components\\EmptyState.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\components\\SearchBar.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\components\\FooterBar.vue", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\api\\index.js", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\api\\user.js", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\api\\shop.js", [], "C:\\Users\\<USER>\\Desktop\\interview\\project\\HmdpReconstruction\\frontend\\newFront\\src\\api\\blog.js", []]