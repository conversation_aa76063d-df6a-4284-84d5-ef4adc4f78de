package com.hmdp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmdp.dto.LoginFormDTO;
import com.hmdp.dto.Result;
import com.hmdp.dto.UserDTO;
import com.hmdp.entity.User;
import com.hmdp.mapper.UserMapper;
import com.hmdp.service.IUserService;
import com.hmdp.utils.RegexUtils;
import com.hmdp.utils.SystemConstants;
import com.hmdp.utils.UserHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.BitFieldSubCommands;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpSession;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.hmdp.utils.RedisConstants.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 发送短信验证码
     *
     * @param phone
     * @param session
     * @return
     */
    public Result sendCode(String phone, HttpSession session) {
        // 1.先验证手机号是否合法，合法：继续；不合法：返回 fail
        if (RegexUtils.isPhoneInvalid(phone)) {
            return Result.fail("手机号格式错误");
        }
        // 2.设置短信验证码，用 hutool 工具箱中的类
        String code = RandomUtil.randomNumbers(6);

        // 3.将短信验证码存入 session --- 后续，改为 redis 存储
//        session.setAttribute("code", code);
        stringRedisTemplate.opsForValue().set(LOGIN_CODE_KEY + phone, code, LOGIN_CODE_TTL, TimeUnit.MINUTES);

        // 4.发送短信验证码
        log.info("验证码短信发送成功，验证码：{}", code);
        // 5.返回响应成功
        return Result.ok();
    }

    /**
     * 登录功能
     *
     * @param loginForm
     * @param session
     * @return
     */
    @Override
    public Result login(LoginFormDTO loginForm, HttpSession session) {
        String phone = loginForm.getPhone();
        // 1.校验手机号
        if (RegexUtils.isPhoneInvalid(phone)) {
            // 2.不存在，
            return Result.fail("手机号格式错误");
        }

        // 3.存在的话，验证验证码是否相同,
        // 后续：用到 redis 查询去对比
//        String cacheCode = stringRedisTemplate.opsForValue().get(LOGIN_CODE_KEY + phone);
//        String code = loginForm.getCode();
//
//        if (cacheCode == null || !cacheCode.equals(code)) {
//            return Result.fail("验证码错误");
//        }
//
//        // 验证成功，立即删除验证码
//        stringRedisTemplate.delete(LOGIN_CODE_KEY + phone);

        // 4.再看用户在数据库中是否存在
        User user = query().eq("phone", phone).one();

        // 5.存在的话，直接登录

        // 6.不存在，注册用户，将用户信息插入数据库表中；再登录
        if (user == null) {
            user = createUserWithPhone(phone);
        }
        // 7.保存用户到 Redis 中
        // 7.1.随机生成 token 作为登录令牌
        String token = UUID.randomUUID().toString(true);

        // 7.2.将User对象转为Hash存储

        UserDTO userDTO = BeanUtil.copyProperties(user, UserDTO.class);
        Map<String, Object> userMap = BeanUtil.beanToMap(userDTO, new HashMap<>(),
                CopyOptions.create()
                        .setIgnoreNullValue(true)
                        .setFieldValueEditor((filedName, fieldValue) -> fieldValue.toString()));

        // 7.3 存储
        String tokenKey = LOGIN_USER_KEY + token;
        stringRedisTemplate.opsForHash().putAll(tokenKey, userMap);

        // 7.4 设置 token 有效期
        stringRedisTemplate.expire(tokenKey, LOGIN_USER_TTL, TimeUnit.MINUTES);

        // 7.5 返回 token
        return Result.ok(token);
    }

    @Override
    public Result sign() {

        UserDTO user = UserHolder.getUser();
        if (user == null) {
            return Result.fail("用户未登录，无法签到");
        }

        // 1. 获取当前登录用户
        Long userId = user.getId();

        // 2. 获取当前日期
        LocalDateTime dateTimeNow = LocalDateTime.now();

        String keySuffix = dateTimeNow.format(DateTimeFormatter.ofPattern(":yyyyMM"));

        // 3. 拼接 key
        String key = USER_SIGN_KEY + userId + keySuffix;

        // 4. 获取今天是本月的第几天
        int dayOfMonth = dateTimeNow.getDayOfMonth();

        // 5. 将签到信息插入 redis
        Boolean isSigned = stringRedisTemplate.opsForValue().setBit(key, dayOfMonth - 1, true);

        if (isSigned != null && isSigned) {
            // 如果原来的值是 true，说明是重复签到
            return Result.fail("您今天已经签过到了，不能重复签到");
        }

        // 5. 返回成功信息
        return Result.ok();
    }

    @Override
    public Result singCount() {
        UserDTO user = UserHolder.getUser();
        if (user == null) {
            return Result.fail("用户未登录，无法签到");
        }

        // 1. 获取当前登录用户
        Long userId = user.getId();

        // 2. 获取当前日期
        LocalDateTime dateTimeNow = LocalDateTime.now();
        String keySuffix = dateTimeNow.format(DateTimeFormatter.ofPattern(":yyyyMM"));

        // 3. 拼接 key
        String key = USER_SIGN_KEY + userId + keySuffix;

        // 4. 获取今天是本月的第几天
        int dayOfMonth = dateTimeNow.getDayOfMonth();

        // 5. 获取本月截止到今天的所有签到记录，返回的是 一个 十进制数字
        List<Long> result = stringRedisTemplate.opsForValue().bitField(
                key, BitFieldSubCommands.create().get(BitFieldSubCommands.BitFieldType.unsigned(dayOfMonth)).valueAt(0)
        );

        if (result == null || result.isEmpty()) {
            return Result.ok(0);
        }

        Long num = result.get(0);
        if (num == null || num == 0) {
            return Result.ok(0);
        }
        int count = 0;
        // 6. 循环遍历
        while (true) {
            // 6.1 让这个数字和 1 做 与 运算， 得到数字的最后一个 bit 位
            if ((num & 1) == 0) {
                // 如果为 0， 说明未签到，结束
                break;
            }
            // 如果不为 0, 说明已签到，继续
            count++;
            // 把数字右移一位，抛弃最后一位 bit 位，继续下一个 bit 位
            num >>>= 1;
        }

        return Result.ok(count);
    }

    /**
     * 第一次登录时创建用户
     *
     * @param phone
     * @return
     */
    private User createUserWithPhone(String phone) {
        // 1.创建用户
        User user = new User();

        // user phone
        user.setPhone(phone);
        // user name
        user.setNickName(SystemConstants.USER_NICK_NAME_PREFIX + RandomUtil.randomString(10));
        // 2.保存用户
        save(user);
        return user;
    }
}
