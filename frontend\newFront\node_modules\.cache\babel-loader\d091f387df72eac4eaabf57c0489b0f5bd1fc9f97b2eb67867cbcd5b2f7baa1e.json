{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport HeaderBar from '@/components/HeaderBar.vue';\nimport LoadingSpinner from '@/components/LoadingSpinner.vue';\nimport EmptyState from '@/components/EmptyState.vue';\nimport { blogApi, userApi } from '@/api';\nexport default {\n  name: 'BlogDetail',\n  components: {\n    HeaderBar,\n    LoadingSpinner,\n    EmptyState\n  },\n  data() {\n    return {\n      blog: {},\n      loading: false,\n      currentUser: null\n    };\n  },\n  computed: {\n    blogImages() {\n      if (this.blog.images) {\n        return this.blog.images.split(',').filter(img => img.trim());\n      }\n      return [];\n    },\n    isMyBlog() {\n      return this.currentUser && this.blog.userId === this.currentUser.id;\n    }\n  },\n  created() {\n    this.loadCurrentUser();\n    this.loadBlogDetail();\n  },\n  methods: {\n    // 加载当前用户信息\n    async loadCurrentUser() {\n      try {\n        const response = await userApi.getCurrentUser();\n        this.currentUser = response.data;\n      } catch (error) {\n        // 用户未登录\n      }\n    },\n    // 加载博客详情\n    async loadBlogDetail() {\n      const blogId = this.$route.params.id;\n      if (!blogId) {\n        this.$message.error('博客ID不存在');\n        this.goBack();\n        return;\n      }\n      this.loading = true;\n      try {\n        const response = await blogApi.getBlogById(blogId);\n        this.blog = response.data || {};\n      } catch (error) {\n        console.error('加载博客详情失败:', error);\n        this.$message.error('加载博客详情失败');\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 返回上一页\n    goBack() {\n      this.$router.go(-1);\n    },\n    // 格式化时间\n    formatTime(time) {\n      if (!time) return '';\n      const date = new Date(time);\n      const now = new Date();\n      const diff = now - date;\n      if (diff < 60000) {\n        return '刚刚';\n      } else if (diff < 3600000) {\n        return `${Math.floor(diff / 60000)}分钟前`;\n      } else if (diff < 86400000) {\n        return `${Math.floor(diff / 3600000)}小时前`;\n      } else {\n        return date.toLocaleDateString();\n      }\n    },\n    // 点赞/取消点赞\n    async toggleLike() {\n      if (!this.currentUser) {\n        this.$message.warning('请先登录');\n        this.$router.push('/login');\n        return;\n      }\n      try {\n        await blogApi.likeBlog(this.blog.id);\n        // 重新加载博客信息\n        await this.loadBlogDetail();\n      } catch (error) {\n        console.error('点赞操作失败:', error);\n        this.$message.error('点赞操作失败');\n      }\n    },\n    // 跳转到用户资料\n    toUserProfile(userId) {\n      this.$router.push(`/other-info/${userId}`);\n    },\n    // 关注用户\n    followUser() {\n      this.$message.info('关注功能开发中');\n    },\n    // 显示评论\n    showComments() {\n      this.$message.info('评论功能开发中');\n    },\n    // 分享博客\n    shareBlog() {\n      if (navigator.share) {\n        navigator.share({\n          title: this.blog.title,\n          text: this.blog.content,\n          url: window.location.href\n        });\n      } else {\n        navigator.clipboard.writeText(window.location.href).then(() => {\n          this.$message.success('链接已复制到剪贴板');\n        }).catch(() => {\n          this.$message.error('分享失败');\n        });\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "LoadingSpinner", "EmptyState", "blogApi", "userApi", "name", "components", "data", "blog", "loading", "currentUser", "computed", "blogImages", "images", "split", "filter", "img", "trim", "isMyBlog", "userId", "id", "created", "loadCurrentUser", "loadBlogDetail", "methods", "response", "getCurrentUser", "error", "blogId", "$route", "params", "$message", "goBack", "getBlogById", "console", "$router", "go", "formatTime", "time", "date", "Date", "now", "diff", "Math", "floor", "toLocaleDateString", "toggleLike", "warning", "push", "likeBlog", "toUserProfile", "followUser", "info", "showComments", "shareBlog", "navigator", "share", "title", "text", "content", "url", "window", "location", "href", "clipboard", "writeText", "then", "success", "catch"], "sources": ["src/views/BlogDetail.vue"], "sourcesContent": ["<template>\n  <div class=\"blog-detail-page\">\n    <!-- 头部 -->\n    <HeaderBar \n      title=\"博客详情\" \n      :show-back=\"true\" \n      @back=\"goBack\"\n    />\n    \n    <!-- 博客内容 -->\n    <div v-if=\"blog.id\" class=\"blog-detail\">\n      <!-- 博客图片 -->\n      <div v-if=\"blogImages.length > 0\" class=\"blog-images\">\n        <el-carousel height=\"250px\" indicator-position=\"outside\">\n          <el-carousel-item v-for=\"(image, index) in blogImages\" :key=\"index\">\n            <img :src=\"image\" :alt=\"blog.title\" class=\"blog-image\">\n          </el-carousel-item>\n        </el-carousel>\n      </div>\n      \n      <!-- 博客信息 -->\n      <div class=\"blog-info\">\n        <h2 class=\"blog-title\">{{ blog.title }}</h2>\n        \n        <!-- 作者信息 -->\n        <div class=\"blog-author\">\n          <img \n            :src=\"blog.icon || '/imgs/icons/default-icon.png'\" \n            :alt=\"blog.name\"\n            class=\"author-avatar\"\n            @click=\"toUserProfile(blog.userId)\"\n          >\n          <div class=\"author-info\">\n            <div class=\"author-name\">{{ blog.name }}</div>\n            <div class=\"publish-time\">{{ formatTime(blog.createTime) }}</div>\n          </div>\n          <el-button \n            v-if=\"!isMyBlog\" \n            type=\"primary\" \n            size=\"small\" \n            @click=\"followUser\"\n          >\n            关注\n          </el-button>\n        </div>\n        \n        <!-- 博客内容 -->\n        <div class=\"blog-content\">\n          <p>{{ blog.content }}</p>\n        </div>\n        \n        <!-- 博客操作 -->\n        <div class=\"blog-actions\">\n          <div class=\"action-item\" @click=\"toggleLike\">\n            <svg \n              t=\"1646634642977\" \n              class=\"like-icon\" \n              viewBox=\"0 0 1024 1024\" \n              version=\"1.1\" \n              xmlns=\"http://www.w3.org/2000/svg\" \n              width=\"20\" \n              height=\"20\"\n            >\n              <path \n                d=\"M160 944c0 8.8-7.2 16-16 16h-32c-26.5 0-48-21.5-48-48V528c0-26.5 21.5-48 48-48h32c8.8 0 16 7.2 16 16v448zM96 416c-53 0-96 43-96 96v416c0 53 43 96 96 96h96c17.7 0 32-14.3 32-32V448c0-17.7-14.3-32-32-32H96zM505.6 64c16.2 0 26.4 8.7 31 13.9 4.6 5.2 12.1 16.3 10.3 32.4l-23.5 203.4c-4.9 42.2 8.6 84.6 36.8 116.4 28.3 31.7 68.9 49.9 111.4 49.9h271.2c6.6 0 10.8 3.3 13.2 6.1s5 7.5 4 14l-48 303.4c-6.9 43.6-29.1 83.4-62.7 112C815.8 944.2 773 960 728.9 960h-317c-33.1 0-59.9-26.8-59.9-59.9v-455c0-6.1 1.7-12 5-17.1 69.5-109 106.4-234.2 107-364h41.6z m0-64h-44.9C427.2 0 400 27.2 400 60.7c0 127.1-39.1 251.2-112 355.3v484.1c0 68.4 55.5 123.9 123.9 123.9h317c122.7 0 227.2-89.3 246.3-210.5l47.9-303.4c7.8-49.4-30.4-94.1-80.4-94.1H671.6c-50.9 0-90.5-44.4-84.6-95l23.5-203.4C617.7 55 568.7 0 505.6 0z\" \n                :fill=\"blog.isLike ? '#ff6633' : '#82848a'\"\n              ></path>\n            </svg>\n            <span>{{ blog.liked }}</span>\n          </div>\n          \n          <div class=\"action-item\" @click=\"showComments\">\n            <i class=\"el-icon-chat-dot-round\"></i>\n            <span>评论</span>\n          </div>\n          \n          <div class=\"action-item\" @click=\"shareBlog\">\n            <i class=\"el-icon-share\"></i>\n            <span>分享</span>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 加载状态 -->\n    <LoadingSpinner v-if=\"loading\" :full-screen=\"true\" text=\"加载中...\" />\n    \n    <!-- 错误状态 -->\n    <EmptyState \n      v-if=\"!loading && !blog.id\" \n      text=\"博客不存在\"\n      icon=\"el-icon-warning\"\n      :show-action=\"true\"\n      action-text=\"返回\"\n      @action=\"goBack\"\n    />\n  </div>\n</template>\n\n<script>\nimport HeaderBar from '@/components/HeaderBar.vue'\nimport LoadingSpinner from '@/components/LoadingSpinner.vue'\nimport EmptyState from '@/components/EmptyState.vue'\nimport { blogApi, userApi } from '@/api'\n\nexport default {\n  name: 'BlogDetail',\n  components: {\n    HeaderBar,\n    LoadingSpinner,\n    EmptyState\n  },\n  data() {\n    return {\n      blog: {},\n      loading: false,\n      currentUser: null\n    }\n  },\n  computed: {\n    blogImages() {\n      if (this.blog.images) {\n        return this.blog.images.split(',').filter(img => img.trim())\n      }\n      return []\n    },\n    \n    isMyBlog() {\n      return this.currentUser && this.blog.userId === this.currentUser.id\n    }\n  },\n  created() {\n    this.loadCurrentUser()\n    this.loadBlogDetail()\n  },\n  methods: {\n    // 加载当前用户信息\n    async loadCurrentUser() {\n      try {\n        const response = await userApi.getCurrentUser()\n        this.currentUser = response.data\n      } catch (error) {\n        // 用户未登录\n      }\n    },\n    \n    // 加载博客详情\n    async loadBlogDetail() {\n      const blogId = this.$route.params.id\n      if (!blogId) {\n        this.$message.error('博客ID不存在')\n        this.goBack()\n        return\n      }\n      \n      this.loading = true\n      try {\n        const response = await blogApi.getBlogById(blogId)\n        this.blog = response.data || {}\n      } catch (error) {\n        console.error('加载博客详情失败:', error)\n        this.$message.error('加载博客详情失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    \n    // 返回上一页\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    // 格式化时间\n    formatTime(time) {\n      if (!time) return ''\n      const date = new Date(time)\n      const now = new Date()\n      const diff = now - date\n      \n      if (diff < 60000) {\n        return '刚刚'\n      } else if (diff < 3600000) {\n        return `${Math.floor(diff / 60000)}分钟前`\n      } else if (diff < 86400000) {\n        return `${Math.floor(diff / 3600000)}小时前`\n      } else {\n        return date.toLocaleDateString()\n      }\n    },\n    \n    // 点赞/取消点赞\n    async toggleLike() {\n      if (!this.currentUser) {\n        this.$message.warning('请先登录')\n        this.$router.push('/login')\n        return\n      }\n      \n      try {\n        await blogApi.likeBlog(this.blog.id)\n        // 重新加载博客信息\n        await this.loadBlogDetail()\n      } catch (error) {\n        console.error('点赞操作失败:', error)\n        this.$message.error('点赞操作失败')\n      }\n    },\n    \n    // 跳转到用户资料\n    toUserProfile(userId) {\n      this.$router.push(`/other-info/${userId}`)\n    },\n    \n    // 关注用户\n    followUser() {\n      this.$message.info('关注功能开发中')\n    },\n    \n    // 显示评论\n    showComments() {\n      this.$message.info('评论功能开发中')\n    },\n    \n    // 分享博客\n    shareBlog() {\n      if (navigator.share) {\n        navigator.share({\n          title: this.blog.title,\n          text: this.blog.content,\n          url: window.location.href\n        })\n      } else {\n        navigator.clipboard.writeText(window.location.href).then(() => {\n          this.$message.success('链接已复制到剪贴板')\n        }).catch(() => {\n          this.$message.error('分享失败')\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.blog-detail-page {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.blog-detail {\n  padding-top: 50px;\n}\n\n/* 博客图片 */\n.blog-images {\n  background-color: #fff;\n}\n\n.blog-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* 博客信息 */\n.blog-info {\n  background-color: #fff;\n  padding: 20px;\n}\n\n.blog-title {\n  font-size: 20px;\n  font-weight: 600;\n  color: #333;\n  margin: 0 0 15px 0;\n  line-height: 1.4;\n}\n\n/* 作者信息 */\n.blog-author {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  padding-bottom: 15px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.author-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  margin-right: 12px;\n  cursor: pointer;\n}\n\n.author-info {\n  flex: 1;\n}\n\n.author-name {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 2px;\n}\n\n.publish-time {\n  font-size: 12px;\n  color: #999;\n}\n\n/* 博客内容 */\n.blog-content {\n  margin-bottom: 20px;\n}\n\n.blog-content p {\n  font-size: 14px;\n  color: #333;\n  line-height: 1.6;\n  margin: 0;\n}\n\n/* 博客操作 */\n.blog-actions {\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.action-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  cursor: pointer;\n  padding: 10px;\n  border-radius: 8px;\n  transition: background-color 0.3s ease;\n}\n\n.action-item:hover {\n  background-color: #f5f5f5;\n}\n\n.action-item i,\n.like-icon {\n  margin-bottom: 4px;\n}\n\n.action-item i {\n  font-size: 20px;\n  color: #666;\n}\n\n.action-item span {\n  font-size: 12px;\n  color: #666;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .blog-info {\n    padding: 15px;\n  }\n  \n  .blog-title {\n    font-size: 18px;\n  }\n  \n  .author-avatar {\n    width: 35px;\n    height: 35px;\n  }\n}\n</style>\n"], "mappings": ";;;AAoGA,OAAAA,SAAA;AACA,OAAAC,cAAA;AACA,OAAAC,UAAA;AACA,SAAAC,OAAA,EAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAN,SAAA;IACAC,cAAA;IACAC;EACA;EACAK,KAAA;IACA;MACAC,IAAA;MACAC,OAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACAC,WAAA;MACA,SAAAJ,IAAA,CAAAK,MAAA;QACA,YAAAL,IAAA,CAAAK,MAAA,CAAAC,KAAA,MAAAC,MAAA,CAAAC,GAAA,IAAAA,GAAA,CAAAC,IAAA;MACA;MACA;IACA;IAEAC,SAAA;MACA,YAAAR,WAAA,SAAAF,IAAA,CAAAW,MAAA,UAAAT,WAAA,CAAAU,EAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,eAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA;IACA,MAAAF,gBAAA;MACA;QACA,MAAAG,QAAA,SAAArB,OAAA,CAAAsB,cAAA;QACA,KAAAhB,WAAA,GAAAe,QAAA,CAAAlB,IAAA;MACA,SAAAoB,KAAA;QACA;MAAA;IAEA;IAEA;IACA,MAAAJ,eAAA;MACA,MAAAK,MAAA,QAAAC,MAAA,CAAAC,MAAA,CAAAV,EAAA;MACA,KAAAQ,MAAA;QACA,KAAAG,QAAA,CAAAJ,KAAA;QACA,KAAAK,MAAA;QACA;MACA;MAEA,KAAAvB,OAAA;MACA;QACA,MAAAgB,QAAA,SAAAtB,OAAA,CAAA8B,WAAA,CAAAL,MAAA;QACA,KAAApB,IAAA,GAAAiB,QAAA,CAAAlB,IAAA;MACA,SAAAoB,KAAA;QACAO,OAAA,CAAAP,KAAA,cAAAA,KAAA;QACA,KAAAI,QAAA,CAAAJ,KAAA;MACA;QACA,KAAAlB,OAAA;MACA;IACA;IAEA;IACAuB,OAAA;MACA,KAAAG,OAAA,CAAAC,EAAA;IACA;IAEA;IACAC,WAAAC,IAAA;MACA,KAAAA,IAAA;MACA,MAAAC,IAAA,OAAAC,IAAA,CAAAF,IAAA;MACA,MAAAG,GAAA,OAAAD,IAAA;MACA,MAAAE,IAAA,GAAAD,GAAA,GAAAF,IAAA;MAEA,IAAAG,IAAA;QACA;MACA,WAAAA,IAAA;QACA,UAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA;MACA,WAAAA,IAAA;QACA,UAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA;MACA;QACA,OAAAH,IAAA,CAAAM,kBAAA;MACA;IACA;IAEA;IACA,MAAAC,WAAA;MACA,UAAApC,WAAA;QACA,KAAAqB,QAAA,CAAAgB,OAAA;QACA,KAAAZ,OAAA,CAAAa,IAAA;QACA;MACA;MAEA;QACA,MAAA7C,OAAA,CAAA8C,QAAA,MAAAzC,IAAA,CAAAY,EAAA;QACA;QACA,WAAAG,cAAA;MACA,SAAAI,KAAA;QACAO,OAAA,CAAAP,KAAA,YAAAA,KAAA;QACA,KAAAI,QAAA,CAAAJ,KAAA;MACA;IACA;IAEA;IACAuB,cAAA/B,MAAA;MACA,KAAAgB,OAAA,CAAAa,IAAA,gBAAA7B,MAAA;IACA;IAEA;IACAgC,WAAA;MACA,KAAApB,QAAA,CAAAqB,IAAA;IACA;IAEA;IACAC,aAAA;MACA,KAAAtB,QAAA,CAAAqB,IAAA;IACA;IAEA;IACAE,UAAA;MACA,IAAAC,SAAA,CAAAC,KAAA;QACAD,SAAA,CAAAC,KAAA;UACAC,KAAA,OAAAjD,IAAA,CAAAiD,KAAA;UACAC,IAAA,OAAAlD,IAAA,CAAAmD,OAAA;UACAC,GAAA,EAAAC,MAAA,CAAAC,QAAA,CAAAC;QACA;MACA;QACAR,SAAA,CAAAS,SAAA,CAAAC,SAAA,CAAAJ,MAAA,CAAAC,QAAA,CAAAC,IAAA,EAAAG,IAAA;UACA,KAAAnC,QAAA,CAAAoC,OAAA;QACA,GAAAC,KAAA;UACA,KAAArC,QAAA,CAAAJ,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}