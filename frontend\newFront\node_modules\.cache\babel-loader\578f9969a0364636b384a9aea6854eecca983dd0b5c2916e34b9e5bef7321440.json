{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport HeaderBar from '@/components/HeaderBar.vue';\nimport { blogApi } from '@/api';\nexport default {\n  name: 'BlogEdit',\n  components: {\n    HeaderBar\n  },\n  data() {\n    return {\n      form: {\n        title: '',\n        content: '',\n        images: ''\n      },\n      imageList: [],\n      publishing: false\n    };\n  },\n  methods: {\n    // 返回上一页\n    goBack() {\n      if (this.form.title || this.form.content || this.imageList.length > 0) {\n        this.$confirm('确定要放弃编辑吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          this.$router.go(-1);\n        });\n      } else {\n        this.$router.go(-1);\n      }\n    },\n    // 图片上传成功\n    handleUploadSuccess(response, file, fileList) {\n      if (response.success) {\n        this.imageList = fileList;\n        this.updateImages();\n      } else {\n        this.$message.error('图片上传失败');\n      }\n    },\n    // 移除图片\n    handleRemove(file, fileList) {\n      this.imageList = fileList;\n      this.updateImages();\n    },\n    // 更新图片字符串\n    updateImages() {\n      const images = this.imageList.filter(item => item.response && item.response.success).map(item => item.response.data).join(',');\n      this.form.images = images;\n    },\n    // 发布博客\n    async publishBlog() {\n      if (!this.validateForm()) {\n        return;\n      }\n      this.publishing = true;\n      try {\n        await blogApi.saveBlog(this.form);\n        this.$message.success('发布成功');\n        this.$router.push('/');\n      } catch (error) {\n        console.error('发布失败:', error);\n        this.$message.error('发布失败');\n      } finally {\n        this.publishing = false;\n      }\n    },\n    // 表单验证\n    validateForm() {\n      if (!this.form.title.trim()) {\n        this.$message.error('请输入博客标题');\n        return false;\n      }\n      if (!this.form.content.trim()) {\n        this.$message.error('请输入博客内容');\n        return false;\n      }\n      return true;\n    }\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "blogApi", "name", "components", "data", "form", "title", "content", "images", "imageList", "publishing", "methods", "goBack", "length", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "$router", "go", "handleUploadSuccess", "response", "file", "fileList", "success", "updateImages", "$message", "error", "handleRemove", "filter", "item", "map", "join", "publishBlog", "validateForm", "saveBlog", "push", "console", "trim"], "sources": ["src/views/BlogEdit.vue"], "sourcesContent": ["<template>\n  <div class=\"blog-edit-page\">\n    <!-- 头部 -->\n    <HeaderBar \n      title=\"发布博客\" \n      :show-back=\"true\" \n      @back=\"goBack\"\n    >\n      <template #actions>\n        <el-button \n          type=\"primary\" \n          size=\"small\" \n          :loading=\"publishing\"\n          @click=\"publishBlog\"\n        >\n          发布\n        </el-button>\n      </template>\n    </HeaderBar>\n    \n    <!-- 编辑表单 -->\n    <div class=\"blog-edit-form\">\n      <!-- 标题输入 -->\n      <el-input\n        v-model=\"form.title\"\n        placeholder=\"请输入博客标题\"\n        class=\"title-input\"\n        maxlength=\"50\"\n        show-word-limit\n      />\n      \n      <!-- 内容输入 -->\n      <el-input\n        v-model=\"form.content\"\n        type=\"textarea\"\n        placeholder=\"分享你的想法...\"\n        class=\"content-input\"\n        :rows=\"10\"\n        maxlength=\"500\"\n        show-word-limit\n      />\n      \n      <!-- 图片上传 -->\n      <div class=\"image-upload\">\n        <div class=\"upload-title\">添加图片</div>\n        <el-upload\n          action=\"/api/upload\"\n          list-type=\"picture-card\"\n          :file-list=\"imageList\"\n          :on-success=\"handleUploadSuccess\"\n          :on-remove=\"handleRemove\"\n          :limit=\"9\"\n          accept=\"image/*\"\n        >\n          <i class=\"el-icon-plus\"></i>\n        </el-upload>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport HeaderBar from '@/components/HeaderBar.vue'\nimport { blogApi } from '@/api'\n\nexport default {\n  name: 'BlogEdit',\n  components: {\n    HeaderBar\n  },\n  data() {\n    return {\n      form: {\n        title: '',\n        content: '',\n        images: ''\n      },\n      imageList: [],\n      publishing: false\n    }\n  },\n  methods: {\n    // 返回上一页\n    goBack() {\n      if (this.form.title || this.form.content || this.imageList.length > 0) {\n        this.$confirm('确定要放弃编辑吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          this.$router.go(-1)\n        })\n      } else {\n        this.$router.go(-1)\n      }\n    },\n    \n    // 图片上传成功\n    handleUploadSuccess(response, file, fileList) {\n      if (response.success) {\n        this.imageList = fileList\n        this.updateImages()\n      } else {\n        this.$message.error('图片上传失败')\n      }\n    },\n    \n    // 移除图片\n    handleRemove(file, fileList) {\n      this.imageList = fileList\n      this.updateImages()\n    },\n    \n    // 更新图片字符串\n    updateImages() {\n      const images = this.imageList\n        .filter(item => item.response && item.response.success)\n        .map(item => item.response.data)\n        .join(',')\n      this.form.images = images\n    },\n    \n    // 发布博客\n    async publishBlog() {\n      if (!this.validateForm()) {\n        return\n      }\n      \n      this.publishing = true\n      try {\n        await blogApi.saveBlog(this.form)\n        this.$message.success('发布成功')\n        this.$router.push('/')\n      } catch (error) {\n        console.error('发布失败:', error)\n        this.$message.error('发布失败')\n      } finally {\n        this.publishing = false\n      }\n    },\n    \n    // 表单验证\n    validateForm() {\n      if (!this.form.title.trim()) {\n        this.$message.error('请输入博客标题')\n        return false\n      }\n      \n      if (!this.form.content.trim()) {\n        this.$message.error('请输入博客内容')\n        return false\n      }\n      \n      return true\n    }\n  }\n}\n</script>\n\n<style scoped>\n.blog-edit-page {\n  height: 100vh;\n  background-color: #f5f5f5;\n  display: flex;\n  flex-direction: column;\n}\n\n.blog-edit-form {\n  flex: 1;\n  padding: 60px 15px 20px;\n  overflow-y: auto;\n}\n\n.title-input {\n  margin-bottom: 15px;\n}\n\n.title-input .el-input__inner {\n  font-size: 16px;\n  font-weight: 500;\n  border: none;\n  background-color: #fff;\n  padding: 15px;\n}\n\n.content-input {\n  margin-bottom: 20px;\n}\n\n.content-input .el-textarea__inner {\n  border: none;\n  background-color: #fff;\n  padding: 15px;\n  font-size: 14px;\n  line-height: 1.6;\n}\n\n.image-upload {\n  background-color: #fff;\n  padding: 15px;\n  border-radius: 8px;\n}\n\n.upload-title {\n  font-size: 14px;\n  color: #333;\n  margin-bottom: 10px;\n}\n</style>\n"], "mappings": ";;;;AA8DA,OAAAA,SAAA;AACA,SAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAH;EACA;EACAI,KAAA;IACA;MACAC,IAAA;QACAC,KAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACAC,SAAA;MACAC,UAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,OAAA;MACA,SAAAP,IAAA,CAAAC,KAAA,SAAAD,IAAA,CAAAE,OAAA,SAAAE,SAAA,CAAAI,MAAA;QACA,KAAAC,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAAC,IAAA;UACA,KAAAC,OAAA,CAAAC,EAAA;QACA;MACA;QACA,KAAAD,OAAA,CAAAC,EAAA;MACA;IACA;IAEA;IACAC,oBAAAC,QAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,IAAAF,QAAA,CAAAG,OAAA;QACA,KAAAhB,SAAA,GAAAe,QAAA;QACA,KAAAE,YAAA;MACA;QACA,KAAAC,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAC,aAAAN,IAAA,EAAAC,QAAA;MACA,KAAAf,SAAA,GAAAe,QAAA;MACA,KAAAE,YAAA;IACA;IAEA;IACAA,aAAA;MACA,MAAAlB,MAAA,QAAAC,SAAA,CACAqB,MAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAT,QAAA,IAAAS,IAAA,CAAAT,QAAA,CAAAG,OAAA,EACAO,GAAA,CAAAD,IAAA,IAAAA,IAAA,CAAAT,QAAA,CAAAlB,IAAA,EACA6B,IAAA;MACA,KAAA5B,IAAA,CAAAG,MAAA,GAAAA,MAAA;IACA;IAEA;IACA,MAAA0B,YAAA;MACA,UAAAC,YAAA;QACA;MACA;MAEA,KAAAzB,UAAA;MACA;QACA,MAAAT,OAAA,CAAAmC,QAAA,MAAA/B,IAAA;QACA,KAAAsB,QAAA,CAAAF,OAAA;QACA,KAAAN,OAAA,CAAAkB,IAAA;MACA,SAAAT,KAAA;QACAU,OAAA,CAAAV,KAAA,UAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;QACA,KAAAlB,UAAA;MACA;IACA;IAEA;IACAyB,aAAA;MACA,UAAA9B,IAAA,CAAAC,KAAA,CAAAiC,IAAA;QACA,KAAAZ,QAAA,CAAAC,KAAA;QACA;MACA;MAEA,UAAAvB,IAAA,CAAAE,OAAA,CAAAgC,IAAA;QACA,KAAAZ,QAAA,CAAAC,KAAA;QACA;MACA;MAEA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}