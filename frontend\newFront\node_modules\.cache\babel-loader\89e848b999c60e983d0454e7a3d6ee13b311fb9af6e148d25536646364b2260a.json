{"ast": null, "code": "import _Message2 from \"element-ui/lib/message\";\nimport _Message from \"element-ui/lib/message\";\nimport axios from 'axios';\n// 创建axios实例\nconst request = axios.create({\n  baseURL: '/api',\n  // API基础路径\n  timeout: 5000 // 请求超时时间\n});\n\n// 请求拦截器\nrequest.interceptors.request.use(config => {\n  // 从sessionStorage获取token\n  const token = sessionStorage.getItem('token');\n  if (token) {\n    config.headers['authorization'] = token;\n  }\n  return config;\n}, error => {\n  console.error('请求错误:', error);\n  return Promise.reject(error);\n});\n\n// 响应拦截器\nrequest.interceptors.response.use(response => {\n  const res = response.data;\n\n  // 判断执行结果\n  if (!res.success) {\n    _Message.error(res.errorMsg || '请求失败');\n    return Promise.reject(res.errorMsg || '请求失败');\n  }\n  return res;\n}, error => {\n  console.error('响应错误:', error);\n  if (error.response) {\n    const {\n      status\n    } = error.response;\n    if (status === 401) {\n      // 未登录，跳转到登录页\n      _Message.error('请先登录');\n      setTimeout(() => {\n        window.location.href = '/login';\n      }, 1000);\n      return Promise.reject('请先登录');\n    } else if (status === 403) {\n      _Message.error('权限不足');\n      return Promise.reject('权限不足');\n    } else if (status === 404) {\n      _Message.error('请求的资源不存在');\n      return Promise.reject('请求的资源不存在');\n    } else if (status >= 500) {\n      _Message.error('服务器内部错误');\n      return Promise.reject('服务器内部错误');\n    }\n  } else if (error.code === 'ECONNABORTED') {\n    _Message.error('请求超时');\n    return Promise.reject('请求超时');\n  } else {\n    _Message.error('网络错误');\n    return Promise.reject('网络错误');\n  }\n  return Promise.reject(error);\n});\nexport default request;", "map": {"version": 3, "names": ["axios", "request", "create", "baseURL", "timeout", "interceptors", "use", "config", "token", "sessionStorage", "getItem", "headers", "error", "console", "Promise", "reject", "response", "res", "data", "success", "_Message", "errorMsg", "status", "setTimeout", "window", "location", "href", "code"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/src/utils/request.js"], "sourcesContent": ["import axios from 'axios'\nimport { Message } from 'element-ui'\n\n// 创建axios实例\nconst request = axios.create({\n  baseURL: '/api', // API基础路径\n  timeout: 5000 // 请求超时时间\n})\n\n// 请求拦截器\nrequest.interceptors.request.use(\n  config => {\n    // 从sessionStorage获取token\n    const token = sessionStorage.getItem('token')\n    if (token) {\n      config.headers['authorization'] = token\n    }\n    return config\n  },\n  error => {\n    console.error('请求错误:', error)\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器\nrequest.interceptors.response.use(\n  response => {\n    const res = response.data\n    \n    // 判断执行结果\n    if (!res.success) {\n      Message.error(res.errorMsg || '请求失败')\n      return Promise.reject(res.errorMsg || '请求失败')\n    }\n    \n    return res\n  },\n  error => {\n    console.error('响应错误:', error)\n    \n    if (error.response) {\n      const { status } = error.response\n      \n      if (status === 401) {\n        // 未登录，跳转到登录页\n        Message.error('请先登录')\n        setTimeout(() => {\n          window.location.href = '/login'\n        }, 1000)\n        return Promise.reject('请先登录')\n      } else if (status === 403) {\n        Message.error('权限不足')\n        return Promise.reject('权限不足')\n      } else if (status === 404) {\n        Message.error('请求的资源不存在')\n        return Promise.reject('请求的资源不存在')\n      } else if (status >= 500) {\n        Message.error('服务器内部错误')\n        return Promise.reject('服务器内部错误')\n      }\n    } else if (error.code === 'ECONNABORTED') {\n      Message.error('请求超时')\n      return Promise.reject('请求超时')\n    } else {\n      Message.error('网络错误')\n      return Promise.reject('网络错误')\n    }\n    \n    return Promise.reject(error)\n  }\n)\n\nexport default request\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AAGzB;AACA,MAAMC,OAAO,GAAGD,KAAK,CAACE,MAAM,CAAC;EAC3BC,OAAO,EAAE,MAAM;EAAE;EACjBC,OAAO,EAAE,IAAI,CAAC;AAChB,CAAC,CAAC;;AAEF;AACAH,OAAO,CAACI,YAAY,CAACJ,OAAO,CAACK,GAAG,CAC9BC,MAAM,IAAI;EACR;EACA,MAAMC,KAAK,GAAGC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;EAC7C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACI,OAAO,CAAC,eAAe,CAAC,GAAGH,KAAK;EACzC;EACA,OAAOD,MAAM;AACf,CAAC,EACDK,KAAK,IAAI;EACPC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;EAC7B,OAAOE,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAX,OAAO,CAACI,YAAY,CAACW,QAAQ,CAACV,GAAG,CAC/BU,QAAQ,IAAI;EACV,MAAMC,GAAG,GAAGD,QAAQ,CAACE,IAAI;;EAEzB;EACA,IAAI,CAACD,GAAG,CAACE,OAAO,EAAE;IAChBC,QAAA,CAAQR,KAAK,CAACK,GAAG,CAACI,QAAQ,IAAI,MAAM,CAAC;IACrC,OAAOP,OAAO,CAACC,MAAM,CAACE,GAAG,CAACI,QAAQ,IAAI,MAAM,CAAC;EAC/C;EAEA,OAAOJ,GAAG;AACZ,CAAC,EACDL,KAAK,IAAI;EACPC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;EAE7B,IAAIA,KAAK,CAACI,QAAQ,EAAE;IAClB,MAAM;MAAEM;IAAO,CAAC,GAAGV,KAAK,CAACI,QAAQ;IAEjC,IAAIM,MAAM,KAAK,GAAG,EAAE;MAClB;MACAF,QAAA,CAAQR,KAAK,CAAC,MAAM,CAAC;MACrBW,UAAU,CAAC,MAAM;QACfC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MACjC,CAAC,EAAE,IAAI,CAAC;MACR,OAAOZ,OAAO,CAACC,MAAM,CAAC,MAAM,CAAC;IAC/B,CAAC,MAAM,IAAIO,MAAM,KAAK,GAAG,EAAE;MACzBF,QAAA,CAAQR,KAAK,CAAC,MAAM,CAAC;MACrB,OAAOE,OAAO,CAACC,MAAM,CAAC,MAAM,CAAC;IAC/B,CAAC,MAAM,IAAIO,MAAM,KAAK,GAAG,EAAE;MACzBF,QAAA,CAAQR,KAAK,CAAC,UAAU,CAAC;MACzB,OAAOE,OAAO,CAACC,MAAM,CAAC,UAAU,CAAC;IACnC,CAAC,MAAM,IAAIO,MAAM,IAAI,GAAG,EAAE;MACxBF,QAAA,CAAQR,KAAK,CAAC,SAAS,CAAC;MACxB,OAAOE,OAAO,CAACC,MAAM,CAAC,SAAS,CAAC;IAClC;EACF,CAAC,MAAM,IAAIH,KAAK,CAACe,IAAI,KAAK,cAAc,EAAE;IACxCP,QAAA,CAAQR,KAAK,CAAC,MAAM,CAAC;IACrB,OAAOE,OAAO,CAACC,MAAM,CAAC,MAAM,CAAC;EAC/B,CAAC,MAAM;IACLK,QAAA,CAAQR,KAAK,CAAC,MAAM,CAAC;IACrB,OAAOE,OAAO,CAACC,MAAM,CAAC,MAAM,CAAC;EAC/B;EAEA,OAAOD,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAeX,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}