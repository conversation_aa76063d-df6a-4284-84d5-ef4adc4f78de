{"ast": null, "code": "import request from '@/utils/request';\n\n/**\n * 博客相关API\n */\nexport default {\n  /**\n   * 获取热门博客列表\n   * @param {number} current 当前页码\n   */\n  getHotBlogs(current = 1) {\n    return request({\n      url: '/blog/hot',\n      method: 'get',\n      params: {\n        current\n      }\n    });\n  },\n  /**\n   * 根据ID获取博客详情\n   * @param {number} blogId 博客ID\n   */\n  getBlogById(blogId) {\n    return request({\n      url: `/blog/${blogId}`,\n      method: 'get'\n    });\n  },\n  /**\n   * 发布博客\n   * @param {Object} blog 博客数据\n   */\n  saveBlog(blog) {\n    return request({\n      url: '/blog',\n      method: 'post',\n      data: blog\n    });\n  },\n  /**\n   * 点赞/取消点赞博客\n   * @param {number} blogId 博客ID\n   */\n  likeBlog(blogId) {\n    return request({\n      url: `/blog/like/${blogId}`,\n      method: 'put'\n    });\n  },\n  /**\n   * 获取博客点赞用户列表\n   * @param {number} blogId 博客ID\n   */\n  getBlogLikes(blogId) {\n    return request({\n      url: `/blog/likes/${blogId}`,\n      method: 'get'\n    });\n  },\n  /**\n   * 获取我的博客列表\n   * @param {number} current 当前页码\n   */\n  getMyBlogs(current = 1) {\n    return request({\n      url: '/blog/of/me',\n      method: 'get',\n      params: {\n        current\n      }\n    });\n  },\n  /**\n   * 根据用户ID获取博客列表\n   * @param {Object} params 查询参数\n   * @param {number} params.id 用户ID\n   * @param {number} params.current 当前页码\n   */\n  getBlogsByUserId(params) {\n    return request({\n      url: '/blog/of/user',\n      method: 'get',\n      params\n    });\n  },\n  /**\n   * 获取关注用户的博客列表（滚动分页）\n   * @param {Object} params 查询参数\n   * @param {number} params.lastId 上次查询的最后一条记录ID\n   * @param {number} params.offset 偏移量\n   */\n  getFollowBlogs(params) {\n    return request({\n      url: '/blog/of/follow',\n      method: 'get',\n      params\n    });\n  }\n};", "map": {"version": 3, "names": ["request", "getHotBlogs", "current", "url", "method", "params", "getBlogById", "blogId", "saveBlog", "blog", "data", "likeBlog", "getBlogLikes", "getMyBlogs", "getBlogsByUserId", "getFollowBlogs"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/src/api/blog.js"], "sourcesContent": ["import request from '@/utils/request'\n\n/**\n * 博客相关API\n */\nexport default {\n  /**\n   * 获取热门博客列表\n   * @param {number} current 当前页码\n   */\n  getHotBlogs(current = 1) {\n    return request({\n      url: '/blog/hot',\n      method: 'get',\n      params: { current }\n    })\n  },\n\n  /**\n   * 根据ID获取博客详情\n   * @param {number} blogId 博客ID\n   */\n  getBlogById(blogId) {\n    return request({\n      url: `/blog/${blogId}`,\n      method: 'get'\n    })\n  },\n\n  /**\n   * 发布博客\n   * @param {Object} blog 博客数据\n   */\n  saveBlog(blog) {\n    return request({\n      url: '/blog',\n      method: 'post',\n      data: blog\n    })\n  },\n\n  /**\n   * 点赞/取消点赞博客\n   * @param {number} blogId 博客ID\n   */\n  likeBlog(blogId) {\n    return request({\n      url: `/blog/like/${blogId}`,\n      method: 'put'\n    })\n  },\n\n  /**\n   * 获取博客点赞用户列表\n   * @param {number} blogId 博客ID\n   */\n  getBlogLikes(blogId) {\n    return request({\n      url: `/blog/likes/${blogId}`,\n      method: 'get'\n    })\n  },\n\n  /**\n   * 获取我的博客列表\n   * @param {number} current 当前页码\n   */\n  getMyBlogs(current = 1) {\n    return request({\n      url: '/blog/of/me',\n      method: 'get',\n      params: { current }\n    })\n  },\n\n  /**\n   * 根据用户ID获取博客列表\n   * @param {Object} params 查询参数\n   * @param {number} params.id 用户ID\n   * @param {number} params.current 当前页码\n   */\n  getBlogsByUserId(params) {\n    return request({\n      url: '/blog/of/user',\n      method: 'get',\n      params\n    })\n  },\n\n  /**\n   * 获取关注用户的博客列表（滚动分页）\n   * @param {Object} params 查询参数\n   * @param {number} params.lastId 上次查询的最后一条记录ID\n   * @param {number} params.offset 偏移量\n   */\n  getFollowBlogs(params) {\n    return request({\n      url: '/blog/of/follow',\n      method: 'get',\n      params\n    })\n  }\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,iBAAiB;;AAErC;AACA;AACA;AACA,eAAe;EACb;AACF;AACA;AACA;EACEC,WAAWA,CAACC,OAAO,GAAG,CAAC,EAAE;IACvB,OAAOF,OAAO,CAAC;MACbG,GAAG,EAAE,WAAW;MAChBC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEH;MAAQ;IACpB,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACEI,WAAWA,CAACC,MAAM,EAAE;IAClB,OAAOP,OAAO,CAAC;MACbG,GAAG,EAAE,SAASI,MAAM,EAAE;MACtBH,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACEI,QAAQA,CAACC,IAAI,EAAE;IACb,OAAOT,OAAO,CAAC;MACbG,GAAG,EAAE,OAAO;MACZC,MAAM,EAAE,MAAM;MACdM,IAAI,EAAED;IACR,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACEE,QAAQA,CAACJ,MAAM,EAAE;IACf,OAAOP,OAAO,CAAC;MACbG,GAAG,EAAE,cAAcI,MAAM,EAAE;MAC3BH,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACEQ,YAAYA,CAACL,MAAM,EAAE;IACnB,OAAOP,OAAO,CAAC;MACbG,GAAG,EAAE,eAAeI,MAAM,EAAE;MAC5BH,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;EACES,UAAUA,CAACX,OAAO,GAAG,CAAC,EAAE;IACtB,OAAOF,OAAO,CAAC;MACbG,GAAG,EAAE,aAAa;MAClBC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;QAAEH;MAAQ;IACpB,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEY,gBAAgBA,CAACT,MAAM,EAAE;IACvB,OAAOL,OAAO,CAAC;MACbG,GAAG,EAAE,eAAe;MACpBC,MAAM,EAAE,KAAK;MACbC;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEU,cAAcA,CAACV,MAAM,EAAE;IACrB,OAAOL,OAAO,CAAC;MACbG,GAAG,EAAE,iBAAiB;MACtBC,MAAM,EAAE,KAAK;MACbC;IACF,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}