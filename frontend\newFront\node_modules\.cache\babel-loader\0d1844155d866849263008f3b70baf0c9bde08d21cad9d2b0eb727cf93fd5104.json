{"ast": null, "code": "'use strict';\n\nrequire(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.for-each.js\");\nvar utils = require('../utils');\n\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n * @returns {Object} New object resulting from merging config2 to config1\n */\nmodule.exports = function mergeConfig(config1, config2) {\n  // eslint-disable-next-line no-param-reassign\n  config2 = config2 || {};\n  var config = {};\n  function getMergedValue(target, source) {\n    if (utils.isPlainObject(target) && utils.isPlainObject(source)) {\n      return utils.merge(target, source);\n    } else if (utils.isPlainObject(source)) {\n      return utils.merge({}, source);\n    } else if (utils.isArray(source)) {\n      return source.slice();\n    }\n    return source;\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDeepProperties(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      return getMergedValue(config1[prop], config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      return getMergedValue(undefined, config1[prop]);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function valueFromConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      return getMergedValue(undefined, config2[prop]);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function defaultToConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      return getMergedValue(undefined, config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      return getMergedValue(undefined, config1[prop]);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDirectKeys(prop) {\n    if (prop in config2) {\n      return getMergedValue(config1[prop], config2[prop]);\n    } else if (prop in config1) {\n      return getMergedValue(undefined, config1[prop]);\n    }\n  }\n  var mergeMap = {\n    'url': valueFromConfig2,\n    'method': valueFromConfig2,\n    'data': valueFromConfig2,\n    'baseURL': defaultToConfig2,\n    'transformRequest': defaultToConfig2,\n    'transformResponse': defaultToConfig2,\n    'paramsSerializer': defaultToConfig2,\n    'timeout': defaultToConfig2,\n    'timeoutMessage': defaultToConfig2,\n    'withCredentials': defaultToConfig2,\n    'adapter': defaultToConfig2,\n    'responseType': defaultToConfig2,\n    'xsrfCookieName': defaultToConfig2,\n    'xsrfHeaderName': defaultToConfig2,\n    'onUploadProgress': defaultToConfig2,\n    'onDownloadProgress': defaultToConfig2,\n    'decompress': defaultToConfig2,\n    'maxContentLength': defaultToConfig2,\n    'maxBodyLength': defaultToConfig2,\n    'beforeRedirect': defaultToConfig2,\n    'transport': defaultToConfig2,\n    'httpAgent': defaultToConfig2,\n    'httpsAgent': defaultToConfig2,\n    'cancelToken': defaultToConfig2,\n    'socketPath': defaultToConfig2,\n    'responseEncoding': defaultToConfig2,\n    'validateStatus': mergeDirectKeys\n  };\n  utils.forEach(Object.keys(config1).concat(Object.keys(config2)), function computeConfigValue(prop) {\n    var merge = mergeMap[prop] || mergeDeepProperties;\n    var configValue = merge(prop);\n    utils.isUndefined(configValue) && merge !== mergeDirectKeys || (config[prop] = configValue);\n  });\n  return config;\n};", "map": {"version": 3, "names": ["require", "utils", "module", "exports", "mergeConfig", "config1", "config2", "config", "getMergedValue", "target", "source", "isPlainObject", "merge", "isArray", "slice", "mergeDeepProperties", "prop", "isUndefined", "undefined", "valueFromConfig2", "defaultToConfig2", "mergeDirectKeys", "mergeMap", "for<PERSON>ach", "Object", "keys", "concat", "computeConfigValue", "config<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/node_modules/axios/lib/core/mergeConfig.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('../utils');\n\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n * @returns {Object} New object resulting from merging config2 to config1\n */\nmodule.exports = function mergeConfig(config1, config2) {\n  // eslint-disable-next-line no-param-reassign\n  config2 = config2 || {};\n  var config = {};\n\n  function getMergedValue(target, source) {\n    if (utils.isPlainObject(target) && utils.isPlainObject(source)) {\n      return utils.merge(target, source);\n    } else if (utils.isPlainObject(source)) {\n      return utils.merge({}, source);\n    } else if (utils.isArray(source)) {\n      return source.slice();\n    }\n    return source;\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDeepProperties(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      return getMergedValue(config1[prop], config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      return getMergedValue(undefined, config1[prop]);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function valueFromConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      return getMergedValue(undefined, config2[prop]);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function defaultToConfig2(prop) {\n    if (!utils.isUndefined(config2[prop])) {\n      return getMergedValue(undefined, config2[prop]);\n    } else if (!utils.isUndefined(config1[prop])) {\n      return getMergedValue(undefined, config1[prop]);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDirectKeys(prop) {\n    if (prop in config2) {\n      return getMergedValue(config1[prop], config2[prop]);\n    } else if (prop in config1) {\n      return getMergedValue(undefined, config1[prop]);\n    }\n  }\n\n  var mergeMap = {\n    'url': valueFromConfig2,\n    'method': valueFromConfig2,\n    'data': valueFromConfig2,\n    'baseURL': defaultToConfig2,\n    'transformRequest': defaultToConfig2,\n    'transformResponse': defaultToConfig2,\n    'paramsSerializer': defaultToConfig2,\n    'timeout': defaultToConfig2,\n    'timeoutMessage': defaultToConfig2,\n    'withCredentials': defaultToConfig2,\n    'adapter': defaultToConfig2,\n    'responseType': defaultToConfig2,\n    'xsrfCookieName': defaultToConfig2,\n    'xsrfHeaderName': defaultToConfig2,\n    'onUploadProgress': defaultToConfig2,\n    'onDownloadProgress': defaultToConfig2,\n    'decompress': defaultToConfig2,\n    'maxContentLength': defaultToConfig2,\n    'maxBodyLength': defaultToConfig2,\n    'beforeRedirect': defaultToConfig2,\n    'transport': defaultToConfig2,\n    'httpAgent': defaultToConfig2,\n    'httpsAgent': defaultToConfig2,\n    'cancelToken': defaultToConfig2,\n    'socketPath': defaultToConfig2,\n    'responseEncoding': defaultToConfig2,\n    'validateStatus': mergeDirectKeys\n  };\n\n  utils.forEach(Object.keys(config1).concat(Object.keys(config2)), function computeConfigValue(prop) {\n    var merge = mergeMap[prop] || mergeDeepProperties;\n    var configValue = merge(prop);\n    (utils.isUndefined(configValue) && merge !== mergeDirectKeys) || (config[prop] = configValue);\n  });\n\n  return config;\n};\n"], "mappings": "AAAA,YAAY;;AAACA,OAAA;AAAAA,OAAA;AAEb,IAAIC,KAAK,GAAGD,OAAO,CAAC,UAAU,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAE,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACtD;EACAA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIC,MAAM,GAAG,CAAC,CAAC;EAEf,SAASC,cAAcA,CAACC,MAAM,EAAEC,MAAM,EAAE;IACtC,IAAIT,KAAK,CAACU,aAAa,CAACF,MAAM,CAAC,IAAIR,KAAK,CAACU,aAAa,CAACD,MAAM,CAAC,EAAE;MAC9D,OAAOT,KAAK,CAACW,KAAK,CAACH,MAAM,EAAEC,MAAM,CAAC;IACpC,CAAC,MAAM,IAAIT,KAAK,CAACU,aAAa,CAACD,MAAM,CAAC,EAAE;MACtC,OAAOT,KAAK,CAACW,KAAK,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC;IAChC,CAAC,MAAM,IAAIT,KAAK,CAACY,OAAO,CAACH,MAAM,CAAC,EAAE;MAChC,OAAOA,MAAM,CAACI,KAAK,CAAC,CAAC;IACvB;IACA,OAAOJ,MAAM;EACf;;EAEA;EACA,SAASK,mBAAmBA,CAACC,IAAI,EAAE;IACjC,IAAI,CAACf,KAAK,CAACgB,WAAW,CAACX,OAAO,CAACU,IAAI,CAAC,CAAC,EAAE;MACrC,OAAOR,cAAc,CAACH,OAAO,CAACW,IAAI,CAAC,EAAEV,OAAO,CAACU,IAAI,CAAC,CAAC;IACrD,CAAC,MAAM,IAAI,CAACf,KAAK,CAACgB,WAAW,CAACZ,OAAO,CAACW,IAAI,CAAC,CAAC,EAAE;MAC5C,OAAOR,cAAc,CAACU,SAAS,EAAEb,OAAO,CAACW,IAAI,CAAC,CAAC;IACjD;EACF;;EAEA;EACA,SAASG,gBAAgBA,CAACH,IAAI,EAAE;IAC9B,IAAI,CAACf,KAAK,CAACgB,WAAW,CAACX,OAAO,CAACU,IAAI,CAAC,CAAC,EAAE;MACrC,OAAOR,cAAc,CAACU,SAAS,EAAEZ,OAAO,CAACU,IAAI,CAAC,CAAC;IACjD;EACF;;EAEA;EACA,SAASI,gBAAgBA,CAACJ,IAAI,EAAE;IAC9B,IAAI,CAACf,KAAK,CAACgB,WAAW,CAACX,OAAO,CAACU,IAAI,CAAC,CAAC,EAAE;MACrC,OAAOR,cAAc,CAACU,SAAS,EAAEZ,OAAO,CAACU,IAAI,CAAC,CAAC;IACjD,CAAC,MAAM,IAAI,CAACf,KAAK,CAACgB,WAAW,CAACZ,OAAO,CAACW,IAAI,CAAC,CAAC,EAAE;MAC5C,OAAOR,cAAc,CAACU,SAAS,EAAEb,OAAO,CAACW,IAAI,CAAC,CAAC;IACjD;EACF;;EAEA;EACA,SAASK,eAAeA,CAACL,IAAI,EAAE;IAC7B,IAAIA,IAAI,IAAIV,OAAO,EAAE;MACnB,OAAOE,cAAc,CAACH,OAAO,CAACW,IAAI,CAAC,EAAEV,OAAO,CAACU,IAAI,CAAC,CAAC;IACrD,CAAC,MAAM,IAAIA,IAAI,IAAIX,OAAO,EAAE;MAC1B,OAAOG,cAAc,CAACU,SAAS,EAAEb,OAAO,CAACW,IAAI,CAAC,CAAC;IACjD;EACF;EAEA,IAAIM,QAAQ,GAAG;IACb,KAAK,EAAEH,gBAAgB;IACvB,QAAQ,EAAEA,gBAAgB;IAC1B,MAAM,EAAEA,gBAAgB;IACxB,SAAS,EAAEC,gBAAgB;IAC3B,kBAAkB,EAAEA,gBAAgB;IACpC,mBAAmB,EAAEA,gBAAgB;IACrC,kBAAkB,EAAEA,gBAAgB;IACpC,SAAS,EAAEA,gBAAgB;IAC3B,gBAAgB,EAAEA,gBAAgB;IAClC,iBAAiB,EAAEA,gBAAgB;IACnC,SAAS,EAAEA,gBAAgB;IAC3B,cAAc,EAAEA,gBAAgB;IAChC,gBAAgB,EAAEA,gBAAgB;IAClC,gBAAgB,EAAEA,gBAAgB;IAClC,kBAAkB,EAAEA,gBAAgB;IACpC,oBAAoB,EAAEA,gBAAgB;IACtC,YAAY,EAAEA,gBAAgB;IAC9B,kBAAkB,EAAEA,gBAAgB;IACpC,eAAe,EAAEA,gBAAgB;IACjC,gBAAgB,EAAEA,gBAAgB;IAClC,WAAW,EAAEA,gBAAgB;IAC7B,WAAW,EAAEA,gBAAgB;IAC7B,YAAY,EAAEA,gBAAgB;IAC9B,aAAa,EAAEA,gBAAgB;IAC/B,YAAY,EAAEA,gBAAgB;IAC9B,kBAAkB,EAAEA,gBAAgB;IACpC,gBAAgB,EAAEC;EACpB,CAAC;EAEDpB,KAAK,CAACsB,OAAO,CAACC,MAAM,CAACC,IAAI,CAACpB,OAAO,CAAC,CAACqB,MAAM,CAACF,MAAM,CAACC,IAAI,CAACnB,OAAO,CAAC,CAAC,EAAE,SAASqB,kBAAkBA,CAACX,IAAI,EAAE;IACjG,IAAIJ,KAAK,GAAGU,QAAQ,CAACN,IAAI,CAAC,IAAID,mBAAmB;IACjD,IAAIa,WAAW,GAAGhB,KAAK,CAACI,IAAI,CAAC;IAC5Bf,KAAK,CAACgB,WAAW,CAACW,WAAW,CAAC,IAAIhB,KAAK,KAAKS,eAAe,KAAMd,MAAM,CAACS,IAAI,CAAC,GAAGY,WAAW,CAAC;EAC/F,CAAC,CAAC;EAEF,OAAOrB,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}