{"ast": null, "code": "export default {\n  name: 'EmptyState',\n  props: {\n    text: {\n      type: String,\n      default: '暂无数据'\n    },\n    icon: {\n      type: String,\n      default: 'el-icon-box'\n    },\n    showAction: {\n      type: Boolean,\n      default: false\n    },\n    actionText: {\n      type: String,\n      default: '重新加载'\n    }\n  },\n  computed: {\n    iconClass() {\n      return this.icon;\n    }\n  },\n  methods: {\n    handleAction() {\n      this.$emit('action');\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "text", "type", "String", "default", "icon", "showAction", "Boolean", "actionText", "computed", "iconClass", "methods", "handleAction", "$emit"], "sources": ["src/components/EmptyState.vue"], "sourcesContent": ["<template>\n  <div class=\"empty-state\">\n    <div class=\"empty-icon\">\n      <i :class=\"iconClass\"></i>\n    </div>\n    <div class=\"empty-text\">{{ text }}</div>\n    <div v-if=\"showAction\" class=\"empty-action\">\n      <el-button type=\"primary\" @click=\"handleAction\">{{ actionText }}</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'EmptyState',\n  props: {\n    text: {\n      type: String,\n      default: '暂无数据'\n    },\n    icon: {\n      type: String,\n      default: 'el-icon-box'\n    },\n    showAction: {\n      type: Boolean,\n      default: false\n    },\n    actionText: {\n      type: String,\n      default: '重新加载'\n    }\n  },\n  computed: {\n    iconClass() {\n      return this.icon\n    }\n  },\n  methods: {\n    handleAction() {\n      this.$emit('action')\n    }\n  }\n}\n</script>\n\n<style scoped>\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 20px;\n  text-align: center;\n}\n\n.empty-icon {\n  margin-bottom: 16px;\n}\n\n.empty-icon i {\n  font-size: 64px;\n  color: #ddd;\n}\n\n.empty-text {\n  font-size: 14px;\n  color: #999;\n  margin-bottom: 20px;\n}\n\n.empty-action {\n  margin-top: 10px;\n}\n</style>\n"], "mappings": "AAaA;EACAA,IAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,IAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,UAAA;MACAJ,IAAA,EAAAK,OAAA;MACAH,OAAA;IACA;IACAI,UAAA;MACAN,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAK,QAAA;IACAC,UAAA;MACA,YAAAL,IAAA;IACA;EACA;EACAM,OAAA;IACAC,aAAA;MACA,KAAAC,KAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}