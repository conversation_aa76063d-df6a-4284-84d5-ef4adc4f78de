package com.hmdp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hmdp.dto.Result;
import com.hmdp.dto.ScrollResult;
import com.hmdp.dto.UserDTO;
import com.hmdp.entity.Blog;
import com.hmdp.entity.Follow;
import com.hmdp.entity.User;
import com.hmdp.mapper.BlogMapper;
import com.hmdp.service.IBlogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmdp.service.IFollowService;
import com.hmdp.service.IUserService;
import com.hmdp.utils.SystemConstants;
import com.hmdp.utils.UserHolder;
import net.bytebuddy.matcher.CollectionErasureMatcher;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.hmdp.utils.RedisConstants.BLOG_LIKED_KEY;
import static com.hmdp.utils.RedisConstants.FEED_KEY;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Service
public class BlogServiceImpl extends ServiceImpl<BlogMapper, Blog> implements IBlogService {

    @Resource
    private IUserService userService;

    @Resource
    private StringRedisTemplate redisTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private IFollowService followService;

    @Override
    public Result queryHotBlog(Integer current) {
        // 根据用户查询
        Page<Blog> page = query()
                .orderByDesc("liked")
                .page(new Page<>(current, SystemConstants.MAX_PAGE_SIZE));
        // 获取当前页数据
        List<Blog> records = page.getRecords();
        // 查询用户
        // 查询点没点赞
        records.forEach(blog -> {
            queryBlogUser(blog);
            isBlogLiked(blog);
        });

        return Result.ok(records);
    }

    @Override
    public Result queryById(Long id) {
        // 1. 查询 blog
        Blog blog = getById(id);
        if (blog == null) {
            return Result.fail("笔记不存在");
        }
        // 2. 查询 blog 有关的用户
        queryBlogUser(blog);
        isBlogLiked(blog);
        return Result.ok(blog);
    }

    private void isBlogLiked(Blog blog) {
        // 检查用户是否登录
        UserDTO user = UserHolder.getUser();
        if (user == null) {
            // 未登录用户，直接设置未点赞
            blog.setIsLike(false);
            return;
        }

        // 1. 先获取用户 Id
        Long userId = user.getId();
        if (userId == null) {
            // 用户ID为空，直接设置未点赞
            blog.setIsLike(false);
            return;
        }

        String key = BLOG_LIKED_KEY + blog.getId();
        // 2. 判断当前用户是否,点过赞
        Double score = redisTemplate.opsForZSet().score(key, userId.toString());
        blog.setIsLike(score != null);
    }

    @Override
    public Result isLike(Long id) {
        UserDTO user = UserHolder.getUser();
        if (user == null) {
            return Result.fail("请先登录！");
        }
        // 1. 先获取用户 Id
        Long userId = user.getId();
        if (userId == null) {
            return Result.fail("用户信息异常！");
        }

        String key = BLOG_LIKED_KEY + id;
        // 2. 判断当前用户是否,点过赞
        Double score = redisTemplate.opsForZSet().score(key, userId.toString());

        if (score == null) {
            // 3. 没点过，先更新数据库点赞 + 1
            boolean isSuccess = update().setSql("liked = liked + 1").eq("id", id).update();
            // 3.2 更新 redis ，存入用户的 value 值
            if (isSuccess) {
                redisTemplate.opsForZSet().add(key, userId.toString(), System.currentTimeMillis());
            }
        } else {
            // 4. 点过赞，数据库点赞 - 1
            boolean isSuccess = update().setSql("liked = liked - 1").eq("id", id).update();
            // 4.1 更新 redis ，移除用户的 value 值
            if (isSuccess) {
                redisTemplate.opsForZSet().remove(key, userId.toString());
            }
        }
        return Result.ok();
    }

    @Override
    public Result queryBlogLikes(Long id) {
        String key = BLOG_LIKED_KEY + id;
        // 1. 先从 redis 中查询点赞前五的用户列表
        Set<String> top5 = redisTemplate.opsForZSet().range(key, 0, 4);
        if (top5 == null || top5.isEmpty()) {
            return Result.ok(Collections.emptyList());
        }
        // 2. 解析出其中用户 id
        List<Long> ids = top5.stream().map(Long::valueOf).collect(Collectors.toList());

        String idStr = StrUtil.join(",", ids);
        // 3. 根据用户id 查询用户 WHERE id IN ( 5 , 1 ) ORDER BY FIELD(id,5,1)

        List<UserDTO> userDTOS = userService.query()
                .in("id", ids).last("ORDER BY FIELD (id, " + idStr + ")").list()
                .stream()
                .map(user -> BeanUtil.copyProperties(user, UserDTO.class))
                .collect(Collectors.toList());

        // 3. 返回
        return Result.ok(userDTOS);
    }

    @Override
    public Result saveBlog(Blog blog) {
        // 1. 获取登录用户
        UserDTO user = UserHolder.getUser();
        blog.setUserId(user.getId());
        // 2. 保存探店博文
        boolean isSuccess = save(blog);
        if (!isSuccess) {
            return Result.fail("保存博文失败");
        }

        // 3. 获取关注当前用户的粉丝 id
        List<Follow> follows = followService.query().eq("follow_user_id", user.getId()).list();
        System.out.println("粉丝数量：" + follows.size());

        // 4. 推送所有笔记 id 给所有粉丝
        for (Follow follow : follows) {
            // 4.1 获取粉丝 id
            Long userId = follow.getUserId();
            // 4.2 推送
            String key = "feed:" + userId;

            stringRedisTemplate.opsForZSet().add(key, blog.getId().toString(), System.currentTimeMillis());
        }

        // 5. 返回id
        return Result.ok(blog.getId());
    }

    @Override
    public Result queryBlogOfFollow(Long max, Integer offset) {
        // 1. 获取当前用户
        Long userId = UserHolder.getUser().getId();

        String key = FEED_KEY + userId;
        // 2. 查询收件箱
        Set<ZSetOperations.TypedTuple<String>> typedTuples = stringRedisTemplate.opsForZSet().reverseRangeByScoreWithScores(key, 0, max, offset, 2);

        if (typedTuples == null) {
            return Result.ok();
        }

        long miniTime = 0;
        int os = 1;
        List<Long> ids = new ArrayList<>(typedTuples.size());
        // 3. 解析收件箱中数据
        for (ZSetOperations.TypedTuple<String> tuple : typedTuples) {
            // 3.1 存入id
            ids.add(Long.valueOf(tuple.getValue()));
            // 3.2 存入最小时间
            long time = tuple.getScore().longValue();
            if (time == miniTime) {
                os++;
            } else {
                miniTime = time;
            }
        }

        // 5. 根据id查询blog
        // 关键修正：确保 ids 不为空，否则 Mybatis-Plus 会生成错误的 SQL
        if (ids.isEmpty()) {
            return Result.ok(Collections.emptyList()); // 双重检查，以防万一
        }

        // 根据 id 查询博客信息
        String idsStr = StrUtil.join(",", ids);

        List<Blog> blogs = query().in("id", idsStr).last("ORDER BY FIELD(id, " + idsStr + ")").list();

        // 6. 遍历blogs，查询isLike和author
        for (Blog blog : blogs) {
            queryBlogUser(blog);

            isBlogLiked(blog);
        }

        // 7. 封装到博客中
        ScrollResult scrollResult = new ScrollResult();
        scrollResult.setList(blogs);
        scrollResult.setOffset(os);
        scrollResult.setMinTime(miniTime);

        return Result.ok(scrollResult);
    }

    private void queryBlogUser(Blog blog) {
        Long userId = blog.getUserId();
        User user = userService.getById(userId);
        if (user == null) {
            // 如果用户不存在，可以设置默认值或直接返回
            blog.setName("匿名用户");
            blog.setIcon(""); // 默认头像
            return;
        }
        blog.setName(user.getNickName());
        blog.setIcon(user.getIcon());
    }
}