{"ast": null, "code": "import Vue from 'vue';\nimport Vuex from 'vuex';\nVue.use(Vuex);\nexport default new Vuex.Store({\n  state: {\n    // 用户信息\n    user: null,\n    // 登录状态\n    isLoggedIn: false,\n    // 商户类型列表\n    shopTypes: [],\n    // 当前位置\n    location: {\n      x: null,\n      y: null\n    }\n  },\n  getters: {\n    // 获取用户信息\n    getUserInfo: state => state.user,\n    // 获取登录状态\n    getLoginStatus: state => state.isLoggedIn,\n    // 获取商户类型列表\n    getShopTypes: state => state.shopTypes,\n    // 获取当前位置\n    getLocation: state => state.location\n  },\n  mutations: {\n    // 设置用户信息\n    SET_USER(state, user) {\n      state.user = user;\n      state.isLoggedIn = !!user;\n    },\n    // 清除用户信息\n    CLEAR_USER(state) {\n      state.user = null;\n      state.isLoggedIn = false;\n      sessionStorage.removeItem('token');\n    },\n    // 设置商户类型列表\n    SET_SHOP_TYPES(state, types) {\n      state.shopTypes = types;\n    },\n    // 设置位置信息\n    SET_LOCATION(state, location) {\n      state.location = location;\n    }\n  },\n  actions: {\n    // 登录\n    login({\n      commit\n    }, {\n      user,\n      token\n    }) {\n      commit('SET_USER', user);\n      sessionStorage.setItem('token', token);\n    },\n    // 登出\n    logout({\n      commit\n    }) {\n      commit('CLEAR_USER');\n    },\n    // 获取用户信息\n    async fetchUserInfo({\n      commit\n    }) {\n      try {\n        const token = sessionStorage.getItem('token');\n        if (token) {\n          // 这里可以调用API获取用户信息\n          // const response = await userApi.getUserInfo()\n          // commit('SET_USER', response.data)\n          console.log('Token found, user info fetch logic here');\n        }\n      } catch (error) {\n        console.error('获取用户信息失败:', error);\n        commit('CLEAR_USER');\n      }\n    },\n    // 获取商户类型列表\n    async fetchShopTypes({\n      commit\n    }) {\n      try {\n        // 这里调用API获取商户类型\n        // const response = await shopApi.getShopTypes()\n        // commit('SET_SHOP_TYPES', response.data)\n        console.log('Shop types fetch logic here');\n        commit('SET_SHOP_TYPES', []);\n      } catch (error) {\n        console.error('获取商户类型失败:', error);\n      }\n    }\n  }\n});", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Vuex", "use", "Store", "state", "user", "isLoggedIn", "shopTypes", "location", "x", "y", "getters", "getUserInfo", "getLoginStatus", "getShopTypes", "getLocation", "mutations", "SET_USER", "CLEAR_USER", "sessionStorage", "removeItem", "SET_SHOP_TYPES", "types", "SET_LOCATION", "actions", "login", "commit", "token", "setItem", "logout", "fetchUserInfo", "getItem", "console", "log", "error", "fetchShopTypes"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/src/store/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Vuex from 'vuex'\n\nVue.use(Vuex)\n\nexport default new Vuex.Store({\n  state: {\n    // 用户信息\n    user: null,\n    // 登录状态\n    isLoggedIn: false,\n    // 商户类型列表\n    shopTypes: [],\n    // 当前位置\n    location: {\n      x: null,\n      y: null\n    }\n  },\n  \n  getters: {\n    // 获取用户信息\n    getUserInfo: state => state.user,\n    // 获取登录状态\n    getLoginStatus: state => state.isLoggedIn,\n    // 获取商户类型列表\n    getShopTypes: state => state.shopTypes,\n    // 获取当前位置\n    getLocation: state => state.location\n  },\n  \n  mutations: {\n    // 设置用户信息\n    SET_USER(state, user) {\n      state.user = user\n      state.isLoggedIn = !!user\n    },\n    \n    // 清除用户信息\n    CLEAR_USER(state) {\n      state.user = null\n      state.isLoggedIn = false\n      sessionStorage.removeItem('token')\n    },\n    \n    // 设置商户类型列表\n    SET_SHOP_TYPES(state, types) {\n      state.shopTypes = types\n    },\n    \n    // 设置位置信息\n    SET_LOCATION(state, location) {\n      state.location = location\n    }\n  },\n  \n  actions: {\n    // 登录\n    login({ commit }, { user, token }) {\n      commit('SET_USER', user)\n      sessionStorage.setItem('token', token)\n    },\n    \n    // 登出\n    logout({ commit }) {\n      commit('CLEAR_USER')\n    },\n    \n    // 获取用户信息\n    async fetchUserInfo({ commit }) {\n      try {\n        const token = sessionStorage.getItem('token')\n        if (token) {\n          // 这里可以调用API获取用户信息\n          // const response = await userApi.getUserInfo()\n          // commit('SET_USER', response.data)\n          console.log('Token found, user info fetch logic here')\n        }\n      } catch (error) {\n        console.error('获取用户信息失败:', error)\n        commit('CLEAR_USER')\n      }\n    },\n    \n    // 获取商户类型列表\n    async fetchShopTypes({ commit }) {\n      try {\n        // 这里调用API获取商户类型\n        // const response = await shopApi.getShopTypes()\n        // commit('SET_SHOP_TYPES', response.data)\n        console.log('Shop types fetch logic here')\n        commit('SET_SHOP_TYPES', [])\n      } catch (error) {\n        console.error('获取商户类型失败:', error)\n      }\n    }\n  }\n})\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,IAAI,MAAM,MAAM;AAEvBD,GAAG,CAACE,GAAG,CAACD,IAAI,CAAC;AAEb,eAAe,IAAIA,IAAI,CAACE,KAAK,CAAC;EAC5BC,KAAK,EAAE;IACL;IACAC,IAAI,EAAE,IAAI;IACV;IACAC,UAAU,EAAE,KAAK;IACjB;IACAC,SAAS,EAAE,EAAE;IACb;IACAC,QAAQ,EAAE;MACRC,CAAC,EAAE,IAAI;MACPC,CAAC,EAAE;IACL;EACF,CAAC;EAEDC,OAAO,EAAE;IACP;IACAC,WAAW,EAAER,KAAK,IAAIA,KAAK,CAACC,IAAI;IAChC;IACAQ,cAAc,EAAET,KAAK,IAAIA,KAAK,CAACE,UAAU;IACzC;IACAQ,YAAY,EAAEV,KAAK,IAAIA,KAAK,CAACG,SAAS;IACtC;IACAQ,WAAW,EAAEX,KAAK,IAAIA,KAAK,CAACI;EAC9B,CAAC;EAEDQ,SAAS,EAAE;IACT;IACAC,QAAQA,CAACb,KAAK,EAAEC,IAAI,EAAE;MACpBD,KAAK,CAACC,IAAI,GAAGA,IAAI;MACjBD,KAAK,CAACE,UAAU,GAAG,CAAC,CAACD,IAAI;IAC3B,CAAC;IAED;IACAa,UAAUA,CAACd,KAAK,EAAE;MAChBA,KAAK,CAACC,IAAI,GAAG,IAAI;MACjBD,KAAK,CAACE,UAAU,GAAG,KAAK;MACxBa,cAAc,CAACC,UAAU,CAAC,OAAO,CAAC;IACpC,CAAC;IAED;IACAC,cAAcA,CAACjB,KAAK,EAAEkB,KAAK,EAAE;MAC3BlB,KAAK,CAACG,SAAS,GAAGe,KAAK;IACzB,CAAC;IAED;IACAC,YAAYA,CAACnB,KAAK,EAAEI,QAAQ,EAAE;MAC5BJ,KAAK,CAACI,QAAQ,GAAGA,QAAQ;IAC3B;EACF,CAAC;EAEDgB,OAAO,EAAE;IACP;IACAC,KAAKA,CAAC;MAAEC;IAAO,CAAC,EAAE;MAAErB,IAAI;MAAEsB;IAAM,CAAC,EAAE;MACjCD,MAAM,CAAC,UAAU,EAAErB,IAAI,CAAC;MACxBc,cAAc,CAACS,OAAO,CAAC,OAAO,EAAED,KAAK,CAAC;IACxC,CAAC;IAED;IACAE,MAAMA,CAAC;MAAEH;IAAO,CAAC,EAAE;MACjBA,MAAM,CAAC,YAAY,CAAC;IACtB,CAAC;IAED;IACA,MAAMI,aAAaA,CAAC;MAAEJ;IAAO,CAAC,EAAE;MAC9B,IAAI;QACF,MAAMC,KAAK,GAAGR,cAAc,CAACY,OAAO,CAAC,OAAO,CAAC;QAC7C,IAAIJ,KAAK,EAAE;UACT;UACA;UACA;UACAK,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACxD;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCR,MAAM,CAAC,YAAY,CAAC;MACtB;IACF,CAAC;IAED;IACA,MAAMS,cAAcA,CAAC;MAAET;IAAO,CAAC,EAAE;MAC/B,IAAI;QACF;QACA;QACA;QACAM,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1CP,MAAM,CAAC,gBAAgB,EAAE,EAAE,CAAC;MAC9B,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}