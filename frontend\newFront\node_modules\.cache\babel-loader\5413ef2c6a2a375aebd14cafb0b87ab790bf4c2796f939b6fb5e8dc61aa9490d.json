{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"loading-container\",\n    class: {\n      \"full-screen\": _vm.fullScreen\n    }\n  }, [_c(\"div\", {\n    staticClass: \"loading-spinner\"\n  }, [_c(\"div\", {\n    staticClass: \"spinner\"\n  }), _vm.text ? _c(\"div\", {\n    staticClass: \"loading-text\"\n  }, [_vm._v(_vm._s(_vm.text))]) : _vm._e()])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "class", "fullScreen", "text", "_v", "_s", "_e", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/interview/project/HmdpReconstruction/frontend/newFront/src/components/LoadingSpinner.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      staticClass: \"loading-container\",\n      class: { \"full-screen\": _vm.fullScreen },\n    },\n    [\n      _c(\"div\", { staticClass: \"loading-spinner\" }, [\n        _c(\"div\", { staticClass: \"spinner\" }),\n        _vm.text\n          ? _c(\"div\", { staticClass: \"loading-text\" }, [\n              _vm._v(_vm._s(_vm.text)),\n            ])\n          : _vm._e(),\n      ]),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IACEE,WAAW,EAAE,mBAAmB;IAChCC,KAAK,EAAE;MAAE,aAAa,EAAEJ,GAAG,CAACK;IAAW;EACzC,CAAC,EACD,CACEJ,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAU,CAAC,CAAC,EACrCH,GAAG,CAACM,IAAI,GACJL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCH,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,EAAE,CAACR,GAAG,CAACM,IAAI,CAAC,CAAC,CACzB,CAAC,GACFN,GAAG,CAACS,EAAE,CAAC,CAAC,CACb,CAAC,CAEN,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBX,MAAM,CAACY,aAAa,GAAG,IAAI;AAE3B,SAASZ,MAAM,EAAEW,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}